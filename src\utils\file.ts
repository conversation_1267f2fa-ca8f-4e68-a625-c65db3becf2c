import { saveAs } from "file-saver";

export function downloadFile(
  content: string,
  filename: string,
  fileType: string
) {
  // Prepending a BOM sequence at the beginning of the text file to encoded as UTF-8.
  // const BOM = new Uint8Array([0xef, 0xbb, 0xbf]);
  const file = new File([content], filename, { type: fileType });
  saveAs(file);
}

export function formatSize(
  size: number,
  pointLength = 2,
  units?: string[]
): string {
  if (typeof size === "undefined") return "0";
  if (typeof units === "undefined")
    units = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  let unit;
  while ((unit = units.shift() as string) && size >= 1024) size = size / 1024;
  return (
    (unit === units[0]
      ? size
      : size
          .toFixed(pointLength === undefined ? 2 : pointLength)
          .replace(".00", "")) +
    " " +
    unit
  );
}

export function getTextByteSize(str: string): number {
  return new TextEncoder().encode(str).length;
}

/**
 * 计算资源数组的总大小
 * @param resources 资源数组
 * @returns 总大小（字节）
 */
export function calculateTotalResourceSize(resources: Resource[]): number {
  return resources.reduce((total, resource) => total + (resource.size || 0), 0);
}

/**
 * 检查文件是否超过大小限制
 * @param file 文件对象
 * @param maxSize 最大大小（字节）
 * @returns 是否超过限制
 */
export function isFileSizeExceeded(file: File, maxSize: number): boolean {
  return file.size > maxSize;
}

/**
 * 检查文件数量是否超过限制
 * @param currentCount 当前文件数量
 * @param newFilesCount 新添加的文件数量
 * @param maxCount 最大文件数量
 * @returns 是否超过限制
 */
export function isFileCountExceeded(currentCount: number, newFilesCount: number, maxCount: number): boolean {
  return (currentCount + newFilesCount) > maxCount;
}

/**
 * 计算原始文件数量（不包括分割后的文件）
 * @param resources 资源数组
 * @returns 原始文件数量
 */
export function countOriginalFiles(resources: Resource[]): number {
  // 使用Set来存储原始文件ID
  const originalFileIds = new Set<string>();
  
  resources.forEach(resource => {
    // 分割后的文件ID格式为 "原始ID_索引"，如 "abc123_1"
    // 原始文件ID格式为不包含下划线的ID
    const idParts = resource.id.split('_');
    
    // 如果ID包含下划线，说明是分割后的文件，取第一部分作为原始ID
    // 否则就是原始文件ID
    const originalId = idParts[0];
    
    originalFileIds.add(originalId);
  });
  
  return originalFileIds.size;
}
