import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, AlignmentType, UnderlineType } from 'docx';
import { saveAs } from 'file-saver';

// 定义Word文档样式常量 - 按中文文档标准
const STYLES = {
  title: {
    size: 36, // 小二号 18pt
    bold: true,
    color: "000000", // 黑色
  },
  heading1: {
    size: 36, // 小二号 18pt - 主章节标题
    bold: true,
    color: "000000", // 黑色
  },
  heading2: {
    size: 30, // 小三号 15pt - 子章节标题
    bold: true,
    color: "000000", // 黑色
  },
  heading3: {
    size: 30, // 小三号 15pt
    bold: true,
    color: "000000", // 黑色
  },
  normal: {
    size: 24, // 小四号 12pt - 段落
    color: "000000", // 黑色
  },
  code: {
    size: 22, // 代码稍小
    font: "Consolas",
    color: "000000", // 黑色
  },
  listItem: {
    size: 24, // 小四号 12pt
    color: "000000", // 黑色
  },
};

// Markdown解析器类
class MarkdownParser {
  private static parseInlineFormats(text: string): TextRun[] {
    const runs: TextRun[] = [];
    
    // 先去掉所有的 ** 标记
    const cleanText = text.replace(/\*\*/g, '');
    
    // 处理行内代码
    if (cleanText.includes('`')) {
      const codeParts = cleanText.split(/(`[^`]*?`)/g);
      for (const codePart of codeParts) {
        if (!codePart) continue;
        
        if (codePart.startsWith('`') && codePart.endsWith('`')) {
          const codeText = codePart.slice(1, -1);
          runs.push(new TextRun({
            text: codeText,
            font: STYLES.code.font,
            size: STYLES.code.size,
            color: STYLES.code.color,
          }));
        } else {
          runs.push(new TextRun({
            text: codePart,
            size: STYLES.normal.size,
            color: STYLES.normal.color,
          }));
        }
      }
    } else {
      // 纯普通文本
      runs.push(new TextRun({
        text: cleanText,
        size: STYLES.normal.size,
        color: STYLES.normal.color,
      }));
    }
    
    return runs;
  }
  
  static parseMarkdownToParagraphs(markdown: string): Paragraph[] {
    const paragraphs: Paragraph[] = [];
    const lines = markdown.split('\n');
    let i = 0;
    
    while (i < lines.length) {
      const line = lines[i].trim();
      
      if (!line) {
        i++;
        continue;
      }
      
      // 处理标题
      if (line.startsWith('#')) {
        const level = line.match(/^#+/)?.[0].length || 1;
        const titleText = line.replace(/^#+\s*/, '');
        
        // 清理标题文本中的 ** 标记
        const cleanTitleText = titleText.replace(/\*\*/g, '');
        
        let headingLevel;
        let style;
        
        switch (level) {
          case 1:
            headingLevel = HeadingLevel.HEADING_1;
            style = STYLES.title; // 文档标题
            break;
          case 2:
            headingLevel = HeadingLevel.HEADING_1;
            style = STYLES.heading1; // 主章节标题
            break;
          case 3:
            headingLevel = HeadingLevel.HEADING_2;
            style = STYLES.heading2; // 子章节标题
            break;
          default:
            headingLevel = HeadingLevel.HEADING_3;
            style = STYLES.heading3; // 三级标题
            break;
        }
        
        paragraphs.push(new Paragraph({
          heading: headingLevel,
          spacing: { before: 240, after: 120 },
          children: [new TextRun({
            text: cleanTitleText,
            bold: style.bold,
            size: style.size,
            color: style.color,
          })],
        }));
        i++;
        continue;
      }
      
      // 处理无序列表
      if (line.match(/^[-*+]\s+/)) {
        const listItems: string[] = [];
        
        while (i < lines.length && lines[i].trim().match(/^[-*+]\s+/)) {
          const itemText = lines[i].trim().replace(/^[-*+]\s+/, '');
          listItems.push(itemText);
          i++;
        }
        
        listItems.forEach((item) => {
          paragraphs.push(new Paragraph({
            children: [
              new TextRun({
                text: "• ",
                size: STYLES.listItem.size,
                color: STYLES.listItem.color,
              }),
              ...this.parseInlineFormats(item),
            ],
            spacing: { before: 60, after: 60 },
            indent: { left: 360 },
          }));
        });
        continue;
      }
      
      // 处理有序列表
      if (line.match(/^\d+\.\s+/)) {
        const listItems: string[] = [];

        
        while (i < lines.length && lines[i].trim().match(/^\d+\.\s+/)) {
          const itemText = lines[i].trim().replace(/^\d+\.\s+/, '');
          listItems.push(itemText);
          i++;
        }
        
        listItems.forEach((item, index) => {
          paragraphs.push(new Paragraph({
            children: [
              new TextRun({
                text: `${index + 1}. `,
                size: STYLES.listItem.size,
                color: STYLES.listItem.color,
              }),
              ...this.parseInlineFormats(item),
            ],
            spacing: { before: 60, after: 60 },
            indent: { left: 360 },
          }));
        });
        continue;
      }
      
      // 处理代码块
      if (line.startsWith('```')) {
        const codeLines: string[] = [];
        i++; // 跳过开始的```
        
        while (i < lines.length && !lines[i].trim().startsWith('```')) {
          codeLines.push(lines[i]);
          i++;
        }
        
        if (i < lines.length) i++; // 跳过结束的```
        
        codeLines.forEach(codeLine => {
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: codeLine,
              font: STYLES.code.font,
              size: STYLES.code.size,
              color: STYLES.code.color,
            })],
            spacing: { before: 120, after: 120 },
            indent: { left: 360 },
          }));
        });
        continue;
      }
      
      // 处理分隔线 - 已移除，不生成横线
      if (line.match(/^-{3,}$/)) {
        // 跳过分隔线，不生成任何内容
        i++;
        continue;
      }
      
      // 处理普通段落
      if (line) {
        const cleanLine = line.replace(/\*\*/g, '');
        
        // 检查是否为子章节格式
        const isSubSection = cleanLine.match(/^\d+\.\d+([、：:\s]|$)/);
        
        if (isSubSection) {
          // 使用子章节样式
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: cleanLine,
              bold: STYLES.heading2.bold,
              size: STYLES.heading2.size,
              color: STYLES.heading2.color,
            })],
            spacing: { before: 240, after: 120 },
          }));
        } else {
          // 处理普通段落
          const inlineRuns = this.parseInlineFormats(cleanLine);
          paragraphs.push(new Paragraph({
            children: inlineRuns,
            spacing: { 
              before: 200,
              after: 200,
              line: 360
            },
            indent: { firstLine: 480 },
          }));
        }
        
        i++;
        continue;
      }
      
      i++;
    }
    
    return paragraphs;
  }
}

// 创建资源表格
function createResourceTable(resources: Array<{ name: string }>): Table {
  const rows = [
    new TableRow({
      children: [
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: "序号",
              bold: true,
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
          width: { size: 20, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: "资源名称",
              bold: true,
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
          width: { size: 80, type: WidthType.PERCENTAGE },
        }),
      ],
    }),
  ];
  
  resources.forEach((resource, index) => {
    rows.push(new TableRow({
      children: [
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: (index + 1).toString(),
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
        }),
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: resource.name,
              size: STYLES.normal.size,
            })],
          })],
        }),
      ],
    }));
  });
  
  return new Table({
    rows,
    width: { size: 100, type: WidthType.PERCENTAGE },
  });
}

// 创建来源表格
function createSourceTable(sources: Array<{ title?: string; url: string }>): Table {
  const rows = [
    new TableRow({
      children: [
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: "序号",
              bold: true,
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
          width: { size: 15, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: "标题",
              bold: true,
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
          width: { size: 50, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: "链接",
              bold: true,
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
          width: { size: 35, type: WidthType.PERCENTAGE },
        }),
      ],
    }),
  ];
  
  sources.forEach((source, index) => {
    rows.push(new TableRow({
      children: [
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: (index + 1).toString(),
              size: STYLES.normal.size,
            })],
            alignment: AlignmentType.CENTER,
          })],
        }),
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: source.title || source.url,
              size: STYLES.normal.size,
            })],
          })],
        }),
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({
              text: source.url,
              size: STYLES.normal.size,
              color: "000000",
              underline: { type: UnderlineType.SINGLE },
            })],
          })],
        }),
      ],
    }));
  });
  
  return new Table({
    rows,
    width: { size: 100, type: WidthType.PERCENTAGE },
  });
}

// 主导出函数
export async function exportToWord(
  title: string,
  content: string,
  resources: Array<{ name: string }> = [],
  sources: Array<{ title?: string; url: string }> = []
): Promise<void> {
  try {
    const documentChildren: (Paragraph | Table)[] = [];
    
    // 检查内容是否已包含标题，避免重复
    const contentLines = content.split('\n');
    const hasExistingTitle = contentLines.some(line => 
      line.trim() === title || line.trim().includes(title)
    );
    
    // 只有当内容中没有标题时才添加文档标题
    if (!hasExistingTitle) {
      documentChildren.push(new Paragraph({
        children: [new TextRun({
          text: title,
          bold: true,
          size: STYLES.title.size,
          color: "000000",
        })],
        spacing: { before: 240, after: 480 },
        alignment: AlignmentType.CENTER,
      }));
    }
    
    // 解析并添加主要内容
    const contentParagraphs = MarkdownParser.parseMarkdownToParagraphs(content);
    documentChildren.push(...contentParagraphs);
    
    // 添加资源表格
    if (resources.length > 0) {
      documentChildren.push(new Paragraph({
        children: [new TextRun({
          text: `本地研究资源 (共${resources.length}项)`,
          bold: true,
          size: STYLES.heading2.size,
          color: "000000",
        })],
        spacing: { before: 480, after: 240 },
      }));
      
      documentChildren.push(createResourceTable(resources));
    }
    
    // 添加来源表格
    if (sources.length > 0) {
      documentChildren.push(new Paragraph({
        children: [new TextRun({
          text: `研究信息来源 (共${sources.length}项)`,
          bold: true,
          size: STYLES.heading2.size,
          color: "000000",
        })],
        spacing: { before: 480, after: 240 },
      }));
      
      documentChildren.push(createSourceTable(sources));
    }
    
    // 创建Word文档
    const doc = new Document({
      sections: [{
        properties: {},
        children: documentChildren,
      }],
    });
    
    // 生成并下载文件
    const buffer = await Packer.toBlob(doc);
    saveAs(buffer, `${title}.docx`);
    
  } catch (error) {
    console.error('Word导出失败:', error);
    throw new Error('Word文档导出失败');
  }
}