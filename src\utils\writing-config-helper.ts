import { WritingConfig } from '@/types/outline';
import { useOutlineStore } from '@/store/outline';

/**
 * 根据写作配置生成写作风格指导提示词
 */
export function generateWritingStylePrompt(config: WritingConfig): string {
  const styleMap = {
    academic: '学术写作风格：采用严谨的学术语言体系，以连贯的段落形式展开论述，通过逻辑递进的方式构建完整的理论框架。在论证过程中自然融入权威文献引用和数据支撑，运用"首先...其次...再者...最后"等学术连接词构建清晰的逻辑链条，语言表达客观中性，基于实证分析得出结论',
    business: '商务写作风格（中国官方/正式报告风格）：采用分层递进的结构逻辑，在需要条理化展示复杂内容时适当使用"1、2、3、4"等数字列举要点。保持段落间的自然连贯，每个要点控制在150-200字，确保表述清晰、逻辑严谨。运用"同时"、"此外"、"另一方面"、"综合而言"等连接词实现段落间的流畅过渡。语言精练庄重，体现正式报告的权威性和专业性，重点突出关键数据和业绩指标的深度分析',
    journalistic: '新闻写作风格：以连贯的叙述方式呈现核心事实，通过自然的语言过渡串联关键信息点，避免生硬的条目罗列。运用"据悉"、"与此同时"、"值得注意的是"等新闻常用连接词，保持报道的流畅性和可读性，语言简洁生动，突出新闻价值和时效性',
    technical: '技术写作风格：采用逻辑严密的技术叙述方式，通过"基于此"、"进而"、"由此可见"等技术连接词构建清晰的技术路径。将技术细节和数据分析有机融入连贯的段落表述中，避免机械的参数罗列，确保技术逻辑的完整性，语言准确专业'
  };

  const formatMap = {
    numbered: '章节采用数字编号格式',
    plain: '章节采用简洁标题格式',
    custom: '章节采用自定义格式'
  };

  let styleGuidance = `
**写作风格要求：**
${styleMap[config.style]}

**语言要求：**
${config.languageRequirements}

**格式要求：**
- 标题格式：${formatMap[config.format.titleFormat]}
- ${config.format.includeTableOfContents ? '包含目录结构' : '不包含目录结构'}
- ${config.format.includeCitations ? '包含引用标注' : '不包含引用标注'}
- ${config.format.includeImages ? '包含图片引用' : '不包含图片引用'}
- 段落间距：${config.format.paragraphSpacing}

**内容要求：**
- ${config.avoidDuplication ? '避免内容重复，确保各章节内容独特性' : '允许必要的内容重复以保持连贯性'}
- ${config.enableContextMemory ? '保持上下文记忆，确保章节间逻辑连贯' : '每章节相对独立'}
- ${config.enableQualityCheck ? '启用质量检查，确保内容准确性和完整性' : '使用标准质量要求'}`;

  if (config.customInstructions && config.customInstructions.trim()) {
    styleGuidance += `

**自定义写作指令：**
${config.customInstructions}`;
  }

  return styleGuidance;
}

/**
 * 将写作配置应用到基础提示词中
 */
export function applyWritingConfigToPrompt(basePrompt: string, config: WritingConfig): string {
  const writingStyleGuidance = generateWritingStylePrompt(config);
  
  // 如果基础提示词包含占位符，则替换
  if (basePrompt.includes('{writingStyleGuidance}')) {
    return basePrompt.replace('{writingStyleGuidance}', writingStyleGuidance);
  }
  
  // 否则在提示词末尾添加写作指导
  return `${basePrompt}\n\n${writingStyleGuidance}`;
}

/**
 * 获取有效的写作配置（优先使用大纲配置，降级到默认配置）
 */
export function getEffectiveWritingConfig(overrideConfig?: WritingConfig): WritingConfig {
  // 如果直接传入了配置，优先使用
  if (overrideConfig) {
    return overrideConfig;
  }
  
  const outlineStore = useOutlineStore.getState();
  
  // 如果有当前大纲，使用大纲的写作配置
  if (outlineStore.currentOutline) {
    console.log('🔍 获取大纲写作配置 - 大纲名称:', outlineStore.currentOutline.title);
    console.log('🔍 获取大纲写作配置 - 配置内容:', outlineStore.currentOutline.writingConfig);
    console.log('🎨 写作风格:', outlineStore.currentOutline.writingConfig.style);
    console.log('🌍 语言要求:', outlineStore.currentOutline.writingConfig.languageRequirements);
    return outlineStore.currentOutline.writingConfig;
  }
  
  // 否则使用默认配置
  const defaultConfig = {
    mode: 'chapter' as const,
    format: {
      titleFormat: 'numbered' as const,
      includeTableOfContents: true,
      includeCitations: true,
      includeImages: false,
      paragraphSpacing: 'normal' as const,
    },
    avoidDuplication: true,
    enableContextMemory: true,
    chapterDelay: 2000,
    sectionDelay: 1000,
    style: 'academic' as const,
    languageRequirements: '使用中文撰写，保持学术写作风格',
    enableQualityCheck: true,
    customInstructions: '',
  };
  
  console.log('使用默认写作配置:', defaultConfig);
  return defaultConfig;
}

/**
 * 根据写作配置生成延迟设置
 */
export function getConfiguredDelays(config?: WritingConfig): { chapterDelay: number; sectionDelay: number } {
  const effectiveConfig = config || getEffectiveWritingConfig();
  return {
    chapterDelay: effectiveConfig.chapterDelay,
    sectionDelay: effectiveConfig.sectionDelay,
  };
}

/**
 * 检查写作配置是否启用了特定功能
 */
export function isFeatureEnabled(feature: keyof WritingConfig, config?: WritingConfig): boolean {
  const effectiveConfig = config || getEffectiveWritingConfig();
  return Boolean(effectiveConfig[feature]);
} 