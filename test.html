<!DOCTYPE html>
<html>
<head>
    <title>PDF Worker Test</title>
</head>
<body>
    <h1>PDF Worker Test</h1>
    <div id="result"></div>
    
    <script>
        async function testWorker() {
            try {
                const response = await fetch('/scripts/pdf.worker.min.mjs');
                const contentType = response.headers.get('content-type');
                const status = response.status;
                
                document.getElementById('result').innerHTML = `
                    <p>Status: ${status}</p>
                    <p>Content-Type: ${contentType}</p>
                    <p>Size: ${response.headers.get('content-length')} bytes</p>
                `;
                
                if (contentType && contentType.includes('javascript')) {
                    document.getElementById('result').innerHTML += '<p style="color: green;">✓ MIME type is correct!</p>';
                } else {
                    document.getElementById('result').innerHTML += '<p style="color: red;">✗ MIME type is incorrect!</p>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        testWorker();
    </script>
</body>
</html> 