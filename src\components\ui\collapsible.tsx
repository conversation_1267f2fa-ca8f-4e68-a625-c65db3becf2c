"use client";

import * as React from "react";

interface CollapsibleProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

interface CollapsibleTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
}

interface CollapsibleContentProps {
  children: React.ReactNode;
  className?: string;
}

const CollapsibleContext = React.createContext<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
}>({
  open: false,
  onOpenChange: () => {},
});

const Collapsible: React.FC<CollapsibleProps> = ({ open = false, onOpenChange, children }) => {
  return (
    <CollapsibleContext.Provider value={{ open, onOpenChange: onOpenChange || (() => {}) }}>
      {children}
    </CollapsibleContext.Provider>
  );
};

const CollapsibleTrigger: React.FC<CollapsibleTriggerProps> = ({ children, asChild }) => {
  const { open, onOpenChange } = React.useContext(CollapsibleContext);
  
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, {
      onClick: () => onOpenChange(!open),
    });
  }
  
  return (
    <button onClick={() => onOpenChange(!open)}>
      {children}
    </button>
  );
};

const CollapsibleContent: React.FC<CollapsibleContentProps> = ({ children, className = "" }) => {
  const { open } = React.useContext(CollapsibleContext);
  
  if (!open) return null;
  
  return (
    <div className={className}>
      {children}
    </div>
  );
};

export { Collapsible, CollapsibleTrigger, CollapsibleContent };