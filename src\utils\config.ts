// 获取系统配置
export async function getSystemConfig() {
  try {
    const response = await fetch('/api/config');
    if (!response.ok) {
      throw new Error('Failed to fetch config');
    }
    return await response.json();
  } catch (error) {
    console.warn('Failed to fetch system config, using defaults:', error);
    return {
      locationKnowledgePath: '',
    };
  }
}

// 生成可点击的本地资源链接
export function generateLocalResourceLink(resourceName: string, index: number, baseUrl?: string) {
  if (!baseUrl) {
    return `[L-${index}]: ${resourceName}`;
  }
  
  // 确保基础URL以斜杠结尾
  const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl : baseUrl + '/';
  const resourceUrl = normalizedBaseUrl + encodeURIComponent(resourceName);
  
  return `[L-${index}]: [${resourceName}](${resourceUrl})`;
} 