<div align="center">
<h1>AI驱动的企业管理报告自动化平台</h1>

![GitHub deployments](https://img.shields.io/github/deployments/u14app/gemini-next-chat/Production)
![GitHub Release](https://img.shields.io/github/v/release/u14app/deep-research)
![Docker Image Size](https://img.shields.io/docker/image-size/xiangfa/deep-research/latest)
![Docker Pulls](https://img.shields.io/docker/pulls/xiangfa/deep-research)
[![License: MIT](https://img.shields.io/badge/License-MIT-default.svg)](https://opensource.org/licenses/MIT)

[![Gemini](https://img.shields.io/badge/Gemini-8E75B2?style=flat&logo=googlegemini&logoColor=white)](https://ai.google.dev/)
[![Next](https://img.shields.io/badge/Next.js-111111?style=flat&logo=nextdotjs&logoColor=white)](https://nextjs.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-06B6D4?style=flat&logo=tailwindcss&logoColor=white)](https://tailwindcss.com/)
[![shadcn/ui](https://img.shields.io/badge/shadcn/ui-111111?style=flat&logo=shadcnui&logoColor=white)](https://ui.shadcn.com/)

[![Vercel](https://img.shields.io/badge/Vercel-111111?style=flat&logo=vercel&logoColor=white)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fu14app%2Fdeep-research&project-name=deep-research&repository-name=deep-research)
[![Cloudflare](https://img.shields.io/badge/Cloudflare-F69652?style=flat&logo=cloudflare&logoColor=white)](./docs/How-to-deploy-to-Cloudflare-Pages.md)
[![PWA](https://img.shields.io/badge/PWA-blue?style=flat&logo=pwa&logoColor=white)](https://research.u14.app/)

</div>

**基于AI的企业管理报告智能生成**

AI驱动的企业管理报告自动化平台利用多种强大的AI模型，在几分钟内生成深入的企业管理分析报告。它结合先进的"思考"和"任务"模型，配合互联网连接，为企业投资分析、行业研究、竞争对手分析等各种主题提供快速且深入的专业洞察。**您的隐私至上 - 所有数据在本地处理和存储。**

## ✨ 核心特性

- **快速企业报告生成**：约2分钟内生成全面的企业管理报告，显著加速您的分析流程。
- **多平台支持**：支持快速部署到 Vercel、Cloudflare 等平台。
- **AI驱动分析**：利用先进的AI模型进行准确且深入的企业管理分析。
- **隐私优先**：您的数据保持私密和安全，所有数据存储在浏览器本地。
- **多LLM支持**：支持多种主流大语言模型，包括 Gemini、OpenAI、Anthropic、Deepseek、Grok、Mistral、Azure OpenAI、任何 OpenAI 兼容 LLMs、OpenRouter、Ollama 等。
- **网络搜索支持**：支持 Searxng、Tavily、Firecrawl、Exa、Bocha 等搜索引擎，让不支持搜索的LLMs更便捷地使用网络搜索功能。
- **思考与任务模型**：采用复杂的"思考"和"任务"模型来平衡深度和速度，确保快速获得高质量结果。支持切换研究模型。
- **支持深度研究**：您可以在项目的任何阶段优化或调整研究内容，支持从该阶段重新研究。
- **本地知识库**：支持上传和处理文本、Office、PDF等资源文件生成本地知识库。
- **智能编辑**：支持研究内容编辑，具有两种编辑模式：WYSIWYM 和 Markdown。可以调整阅读级别、文章长度和全文翻译。
- **知识图谱**：支持一键生成知识图谱，让您系统性了解报告内容。
- **研究历史**：支持保存研究历史，您可以随时回顾之前的研究结果并再次进行深入研究。
- **本地与服务器API支持**：提供本地和服务器端API调用选项的灵活性，以满足您的需求。
- **SaaS和MCP支持**：您可以通过SSE API将此项目用作企业报告服务(SaaS)，或通过MCP服务在其他AI服务中使用。
- **PWA支持**：采用渐进式Web应用(PWA)技术，您可以像软件一样使用该项目。
- **多Key负载支持**：支持多Key负载以提高API响应效率。
- **多语言支持**：English、简体中文、Español。
- **现代技术构建**：使用 Next.js 15 和 Shadcn UI 开发，确保现代、高性能和视觉吸引力的用户体验。
- **MIT许可**：在MIT许可下开源，可免费用于个人和商业用途。

## 🎯 Roadmap

- [x] Support preservation of research history
- [x] Support editing final report and search results
- [x] Support for other LLM models
- [x] Support file upload and local knowledge base
- [x] Support SSE API and MCP server

## 🚀 Getting Started

### Use Free Gemini (recommend)

1. Get [Gemini API Key](https://aistudio.google.com/app/apikey)
2. One-click deployment of the project, you can choose to deploy to Vercel or Cloudflare

   [![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fu14app%2Fdeep-research&project-name=deep-research&repository-name=deep-research)

   Currently the project supports deployment to Cloudflare, but you need to follow [How to deploy to Cloudflare Pages](./docs/How-to-deploy-to-Cloudflare-Pages.md) to do it.

3. Start using

### Use Other LLM

1. Deploy the project to Vercel or Cloudflare
2. Set the LLM API key
3. Set the LLM API base URL (optional)
4. Start using

## ⌨️ Development

按照以下步骤在本地浏览器中运行AI驱动的企业管理报告自动化平台。

### Prerequisites

- [Node.js](https://nodejs.org/) (version 18.18.0 or later recommended)
- [pnpm](https://pnpm.io/) or [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)

### Installation

1. **Clone the repository:**

   ```bash
   git clone https://github.com/u14app/deep-research.git
   cd deep-research
   ```

2. **Install dependencies:**

   ```bash
   pnpm install  # or npm install or yarn install
   ```

3. **Set up Environment Variables:**

   You need to modify the file `env.tpl` to `.env`, or create a `.env` file and write the variables to this file.

   ```bash
   # For Development
   cp env.tpl .env.local
   # For Production
   cp env.tpl .env
   ```

4. **Run the development server:**

   ```bash
   pnpm dev  # or npm run dev or yarn dev
   ```

   打开浏览器访问 [http://localhost:3000](http://localhost:3000) 来使用AI驱动的企业管理报告自动化平台。

### Custom Model List

The project allow custom model list, but **only works in proxy mode**. Please add an environment variable named `NEXT_PUBLIC_MODEL_LIST` in the `.env` file or environment variables page.

Custom model lists use `,` to separate multiple models. If you want to disable a model, use the `-` symbol followed by the model name, i.e. `-existing-model-name`. To only allow the specified model to be available, use `-all,+new-model-name`.

## 🚢 Deployment

### Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fu14app%2Fdeep-research&project-name=deep-research&repository-name=deep-research)

### Cloudflare

Currently the project supports deployment to Cloudflare, but you need to follow [How to deploy to Cloudflare Pages](./docs/How-to-deploy-to-Cloudflare-Pages.md) to do it.

### Docker

> The Docker version needs to be 20 or above, otherwise it will prompt that the image cannot be found.

> ⚠️ Note: Most of the time, the docker version will lag behind the latest version by 1 to 2 days, so the "update exists" prompt will continue to appear after deployment, which is normal.

```bash
docker pull xiangfa/deep-research:latest
docker run -d --name deep-research -p 3333:3000 xiangfa/deep-research
```

You can also specify additional environment variables:

```bash
docker run -d --name deep-research \
   -p 3333:3000 \
   -e ACCESS_PASSWORD=your-password \
   -e GOOGLE_GENERATIVE_AI_API_KEY=AIzaSy... \
   xiangfa/deep-research
```

or build your own docker image:

```bash
docker build -t deep-research .
docker run -d --name deep-research -p 3333:3000 deep-research
```

If you need to specify other environment variables, please add `-e key=value` to the above command to specify it.

Deploy using `docker-compose.yml`:

```bash
version: '3.9'
services:
   deep-research:
      image: xiangfa/deep-research
      container_name: deep-research
      environment:
         - ACCESS_PASSWORD=your-password
         - GOOGLE_GENERATIVE_AI_API_KEY=AIzaSy...
      ports:
         - 3333:3000
```

or build your own docker compose:

```bash
docker compose -f docker-compose.yml build
```

### Static Deployment

You can also build a static page version directly, and then upload all files in the `out` directory to any website service that supports static pages, such as Github Page, Cloudflare, Vercel, etc..

```bash
pnpm build:export
```

## ⚙️ Configuration

如"快速开始"部分所述，AI驱动的企业管理报告自动化平台使用以下环境变量进行服务器端API配置：

Please refer to the file [env.tpl](./env.tpl) for all available environment variables.

**Important Notes on Environment Variables:**

- **Privacy Reminder:** These environment variables are primarily used for **server-side API calls**. When using the **local API mode**, no API keys or server-side configurations are needed, further enhancing your privacy.

- **Multi-key Support:** Supports multiple keys, each key is separated by `,`, i.e. `key1,key2,key3`.

- **Security Setting:** By setting `ACCESS_PASSWORD`, you can better protect the security of the server API.

- **Make variables effective:** After adding or modifying this environment variable, please redeploy the project for the changes to take effect.

### Chapter Word Count Configuration

AI驱动的企业管理报告自动化平台支持通过环境变量灵活控制每章节字数，让您适应不同AI模型的token输出能力。

You can configure the word count range for each chapter by adding the following environment variables to your `.env` file:

```bash
# Chapter 1: Business Model Analysis (default: 2800-3800 words)
CHAPTER_1_WORD_MIN=2800
CHAPTER_1_WORD_MAX=3800

# Chapter 2: Operating Conditions Analysis (default: 3000-3800 words)  
CHAPTER_2_WORD_MIN=3000
CHAPTER_2_WORD_MAX=3800

# Chapter 3: Management Team Analysis (default: 2000-2800 words)
CHAPTER_3_WORD_MIN=2000
CHAPTER_3_WORD_MAX=2800

# Chapter 4: Governance Structure Analysis (default: 1800-2500 words)
CHAPTER_4_WORD_MIN=1800
CHAPTER_4_WORD_MAX=2500

# Chapter 5: Development & Investment Return Analysis (default: 2800-3800 words)
CHAPTER_5_WORD_MIN=2800
CHAPTER_5_WORD_MAX=3800

# Chapter 6: Summary & Evaluation (default: 2200-3000 words)
CHAPTER_6_WORD_MIN=2200
CHAPTER_6_WORD_MAX=3000
```

For detailed configuration guidelines and model-specific recommendations, please refer to the [Chapter Word Count Configuration Guide](./docs/CHAPTER_WORD_COUNT_CONFIG.md).

## 📄 API documentation

Currently the project supports two forms of API: Server-Sent Events (SSE) and Model Context Protocol (MCP).

### Server-Sent Events API

企业管理报告自动化平台API提供实时接口，用于启动和监控复杂的研究任务。

Recommended to use the API via `@microsoft/fetch-event-source`, to get the final report, you need to listen to the `message` event, the data will be returned in the form of a text stream.

#### POST method

Endpoint: `/api/sse`

Method: `POST`

Body:

```typescript
interface SSEConfig {
  // Research topic
  query: string;
  // AI provider, Possible values ​​include: google, openai, anthropic, deepseek, xai, mistral, azure, openrouter, openaicompatible, pollinations, ollama
  provider: string;
  // Thinking model id
  thinkingModel: string;
  // Task model id
  taskModel: string;
  // Search provider, Possible values ​​include: model, tavily, firecrawl, exa, bocha, searxng
  searchProvider: string;
  // Response Language, also affects the search language. (optional)
  language?: string;
  // Maximum number of search results. Default, `5` (optional)
  maxResult?: number;
  // Whether to include content-related images in the final report. Default, `true`. (optional)
  enableCitationImage?: boolean;
  // Whether to include citation links in search results and final reports. Default, `true`. (optional)
  enableReferences?: boolean;
}
```

Headers:

```typescript
interface Headers {
  "Content-Type": "application/json";
  // If you set an access password
  // Authorization: "Bearer YOUR_ACCESS_PASSWORD";
}
```

See the detailed [API documentation](./docs/deep-research-api-doc.md).

#### GET method

This is an interesting implementation. You can watch the whole process of deep research directly through the URL just like watching a video.

You can access the deep research report via the following link:

```text
http://localhost:3000/api/sse/live?query=AI+trends+for+this+year&provider=pollinations&thinkingModel=openai&taskModel=openai-fast&searchProvider=searxng
```

Query Params:

```typescript
// The parameters are the same as POST parameters
interface QueryParams extends SSEConfig {
  // If you set the `ACCESS_PASSWORD` environment variable, this parameter is required
  password?: string;
}
```

### Model Context Protocol (MCP) Server

Currently supports `StreamableHTTP` and `SSE` Server Transport.

StreamableHTTP server endpoint: `/api/mcp`, transport type: `streamable-http`

SSE server endpoint: `/api/mcp/sse`, transport type: `sse`

```json
{
  "mcpServers": {
    "deep-research": {
      "url": "http://127.0.0.1:3000/api/mcp",
      "transportType": "streamable-http",
      "timeout": 600
    }
  }
}
```

**Note:** Since deep research take a long time to execute, you need to set a longer timeout to avoid interrupting the study.

If your server sets `ACCESS_PASSWORD`, the MCP service will be protected and you need to add additional headers parameters:

```json
{
  "mcpServers": {
    "deep-research": {
      "url": "http://127.0.0.1:3000/api/mcp",
      "transportType": "streamable-http",
      "timeout": 600,
      "headers": {
        "Authorization": "Bearer YOUR_ACCESS_PASSWORD"
      }
    }
  }
}
```

**Enabling MCP service requires setting global environment variables:**

```bash
# MCP Server AI provider
# Possible values ​​include: google, openai, anthropic, deepseek, xai, mistral, azure, openrouter, openaicompatible, pollinations, ollama
MCP_AI_PROVIDER=google
# MCP Server search provider. Default, `model`
# Possible values ​​include: model, tavily, firecrawl, exa, bocha, searxng
MCP_SEARCH_PROVIDER=tavily
# MCP Server thinking model id, the core model used in deep research.
MCP_THINKING_MODEL=gemini-2.0-flash-thinking-exp
# MCP Server task model id, used for secondary tasks, high output models are recommended.
MCP_TASK_MODEL=gemini-2.0-flash-exp
```

**Note:** To ensure that the MCP service can be used normally, you need to set the environment variables of the corresponding model and search engine. For specific environment variable parameters, please refer to [env.tpl](./env.tpl).

## 🪄 How it works

1. **Research topic**

   - Input research topic
   - Use local research resources (optional)
   - Start thinking (or rethinking)

2. **Propose your ideas**

   - The system asks questions
     - Answer system questions (optional)
     - Write a research plan (or rewrite the research plan)
   - The system outputs the research plan
     - Start in-depth research (or re-research)
     - The system generates SERP queries

3. **Information collection**

   - Initial research
     - Retrieve local research resources based on SERP queries
     - Collect information from the Internet based on SERP queries
   - In-depth research (this process can be repeated)
     - Propose research suggestions (optional)
     - Start a new round of information collection (the process is the same as the initial research)

4. **Generate Final Report**

   - Make a writing request (optional)
   - Summarize all research materials into a comprehensive Markdown report
   - Regenerate research report (optional)

```mermaid
flowchart TB
    A[Research Topic]:::start

    subgraph Propose[Propose your ideas]
        B1[System asks questions]:::process
        B2[System outputs the research plan]:::process
        B3[System generates SERP queries]:::process
        B1 --> B2
        B2 --> B3
    end

    subgraph Collect[Information collection]
        C1[Initial research]:::collection
        C1a[Retrieve local research resources based on SERP queries]:::collection
        C1b[Collect information from the Internet based on SERP queries]:::collection
        C2[In-depth research]:::recursive
        Refine{More in-depth research needed?}:::decision

        C1 --> C1a
        C1 --> C1b
        C1a --> C2
        C1b --> C2
        C2 --> Refine
        Refine -->|Yes| C2
    end

    Report[Generate Final Report]:::output

    A --> Propose
    B3 --> C1

    %% Connect the exit from the loop/subgraph to the final report
    Refine -->|No| Report

    %% Styling
    classDef start fill:#7bed9f,stroke:#2ed573,color:black
    classDef process fill:#70a1ff,stroke:#1e90ff,color:black
    classDef recursive fill:#ffa502,stroke:#ff7f50,color:black
    classDef output fill:#ff4757,stroke:#ff6b81,color:black
    classDef collection fill:#a8e6cf,stroke:#3b7a57,color:black
    classDef decision fill:#c8d6e5,stroke:#8395a7,color:black

    class A start
    class B1,B2,B3 process
    class C1,C1a,C1b collection
    class C2 recursive
    class Refine decision
    class Report output
```

## 🙋 FAQs

**Why does my Ollama or SearXNG not work properly and displays the error `TypeError: Failed to fetch`?**

If your request generates `CORS` due to browser security restrictions, you need to configure parameters for Ollama or SearXNG to allow cross-domain requests. You can also consider using the server proxy mode, which is a backend server that makes requests, which can effectively avoid cross-domain issues.

## 🛡️ Privacy

AI驱动的企业管理报告自动化平台在设计时充分考虑了您的隐私。**所有研究数据和生成的报告都存储在您的本地计算机上。**我们不会收集或传输您的任何研究数据到外部服务器（除非您明确使用服务器端API调用，在这种情况下数据会通过您配置的代理发送到API）。您的隐私是我们的首要任务。

## 🙏 Acknowledgements

- [Next.js](https://nextjs.org/) - The React framework for building performant web applications.
- [Shadcn UI](https://ui.shadcn.com/) - Beautifully designed components that helped streamline the UI development.
- [AI SDKs](https://sdk.vercel.ai) - Powering the intelligent research capabilities of Deep Research.
- [Deep Research](https://github.com/dzhng/deep-research) - Thanks to the project `dzhng/deep-research` for inspiration.

## 🤝 Contributing

我们欢迎对AI驱动的企业管理报告自动化平台的贡献！如果您有改进想法、bug修复或新功能建议，请随时：

1. Fork the repository.
2. Create a new branch for your feature or bug fix.
3. Make your changes and commit them.
4. Submit a pull request.

For major changes, please open an issue first to discuss your proposed changes.

## ✉️ Contact

If you have any questions, suggestions, or feedback, please create a new [issue](https://github.com/u14app/deep-research/issues).

## 🌟 Star History

[![Star History Chart](https://api.star-history.com/svg?repos=u14app/deep-research&type=Date)](https://www.star-history.com/#u14app/deep-research&Date)

## 📝 License

AI驱动的企业管理报告自动化平台在[MIT许可证](LICENSE)下发布。此许可证允许商业和非商业用途的免费使用、修改和分发。
