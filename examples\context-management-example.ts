/**
 * 长程上下文管理使用示例
 * 演示如何集成和使用长程上下文管理机制
 */

import { LongContextManager } from '@/utils/deep-research/long-context-manager';

// 模拟的模型提供者
const mockModelProvider = {
  async generate(prompt: string): Promise<string> {
    // 模拟LLM响应，包含结构化信息
    return `{
      "keyFindings": [
        "公司采用SaaS模式，具有可扩展性",
        "目标市场规模约100亿美元",
        "核心技术具有专利保护"
      ],
      "dataPoints": {
        "revenue": "5000万元",
        "growth_rate": "150%",
        "market_share": "8%"
      },
      "conclusions": [
        "商业模式具有可持续性",
        "市场前景广阔"
      ],
      "citations": ["[1]", "[L-1]"]
    }`;
  }
};

/**
 * 基础使用示例
 */
async function basicUsageExample() {
  console.log("=== 基础使用示例 ===");
  
  // 1. 初始化长程上下文管理器
  const contextManager = new LongContextManager({
    enableSummaryExtraction: true,
    enableContextInjection: true,
    enableProgressiveContext: true,
    debug: true,
    injectionConfig: {
      maxContextLength: 2000,
      enableDataConsistency: true,
      enableCrossReference: true,
      prioritizeRecentChapters: true
    }
  }, {
    // 生命周期钩子
    onChapterStart: (chapterNum, title) => {
      console.log(`[Hook] 开始生成第${chapterNum}章: ${title}`);
    },
    onChapterComplete: (chapterNum, content, summary) => {
      console.log(`[Hook] 第${chapterNum}章完成，提取了${summary.keyFindings.length}个关键发现`);
    },
    onSectionStart: (chapterNum, sectionIndex) => {
      console.log(`[Hook] 开始生成第${chapterNum}章第${sectionIndex + 1}节`);
    },
    onContextInjected: (chapterNum, contextLength) => {
      console.log(`[Hook] 为第${chapterNum}章注入了${contextLength}字符的上下文`);
    },
    onExtractionComplete: (chapterNum, summary, quality) => {
      console.log(`[Hook] 第${chapterNum}章摘要提取完成，质量: ${quality}`);
    }
  });

  // 2. 初始化新报告
  contextManager.initializeNewReport("测试投资分析报告");

  // 3. 模拟章节生成流程
  for (let chapterNum = 1; chapterNum <= 3; chapterNum++) {
    const chapterTitle = `第${chapterNum}章标题`;
    
    // 生成基础提示词
    const originalPrompt = `请分析第${chapterNum}章的内容...`;
    
    // 增强提示词（从第2章开始会注入上下文）
    const enhancedPrompt = contextManager.enhanceSubChapterPrompt(
      chapterNum,
      0,
      originalPrompt
    );
    
    console.log(`\n--- 第${chapterNum}章提示词增强 ---`);
    console.log("原始提示词长度:", originalPrompt.length);
    console.log("增强提示词长度:", enhancedPrompt.length);
    console.log("是否包含上下文:", enhancedPrompt.includes("### 相关上下文"));
    
    // 模拟章节内容生成
    const mockChapterContent = `
## 第${chapterNum}章内容

这是第${chapterNum}章的模拟内容，包含：
- 关键发现 1：重要的业务洞察
- 关键发现 2：市场趋势分析
- 数据点：收入增长150%
- 结论：该章节的核心结论

引用了[1]和[L-1]等资源。
    `;

    // 处理章节完成
    await contextManager.processChapterCompletion(
      chapterNum,
      chapterTitle,
      mockChapterContent,
      mockModelProvider as any
    );

    // 获取当前统计信息
    const stats = contextManager.getContextStats();
    console.log(`第${chapterNum}章完成后统计:`, {
      totalChapters: stats.totalChapters,
      totalFindings: stats.totalFindings,
      totalConclusions: stats.totalConclusions
    });
  }

  // 4. 生成分析报告
  console.log("\n=== 最终分析报告 ===");
  
  const stats = contextManager.getContextStats();
  console.log("最终统计:", stats);
  
  const connections = contextManager.getChapterConnectionAnalysis();
  console.log("章节关联分析:", connections);
  
  const consistency = contextManager.generateConsistencyReport();
  console.log("一致性评估:", {
    overallScore: consistency.overallScore,
    issuesCount: consistency.issues.length,
    strengthsCount: consistency.strengths.length
  });

  // 5. 导出数据
  const exportData = contextManager.exportContextData();
  console.log("导出数据长度:", exportData.length);
}

/**
 * 增强提示词示例
 */
async function promptEnhancementExample() {
  console.log("\n=== 提示词增强示例 ===");
  
  const contextManager = new LongContextManager({
    enableContextInjection: true,
    debug: true
  });
  
  contextManager.initializeNewReport("提示词增强测试");
  
  // 模拟第1章已完成
  await contextManager.processChapterCompletion(
    1,
    "商业模式分析",
    "第1章内容包含SaaS模式、市场规模100亿、专利技术等关键信息...",
    mockModelProvider as any
  );
  
  // 为第2章生成增强提示词
  const originalPrompt = "请分析公司的经营状况，包括财务表现和运营效率。";
  const enhancedPrompt = contextManager.enhanceSubChapterPrompt(2, 0, originalPrompt);
  
  console.log("=== 原始提示词 ===");
  console.log(originalPrompt);
  
  console.log("\n=== 增强提示词 ===");
  console.log(enhancedPrompt);
  
  console.log("\n=== 增强效果分析 ===");
  console.log("长度增加:", enhancedPrompt.length - originalPrompt.length, "字符");
  console.log("包含上下文:", enhancedPrompt.includes("### 相关上下文"));
  console.log("包含一致性检查:", enhancedPrompt.includes("请确保数据一致性"));
}

/**
 * 一致性检查示例
 */
async function consistencyCheckExample() {
  console.log("\n=== 一致性检查示例 ===");
  
  const contextManager = new LongContextManager({
    enableSummaryExtraction: true,
    debug: true
  });
  
  contextManager.initializeNewReport("一致性测试报告");
  
  // 模拟多个章节完成
  const chapterContents = [
    "第1章：SaaS模式，市场规模100亿元，年收入5000万元",
    "第2章：财务表现良好，年收入4800万元，增长率150%", // 故意的数据不一致
    "第3章：团队配置完善，CEO有10年经验"
  ];
  
  for (let i = 0; i < chapterContents.length; i++) {
    await contextManager.processChapterCompletion(
      i + 1,
      `第${i + 1}章`,
      chapterContents[i],
      mockModelProvider as any
    );
  }
  
  // 生成一致性报告
  const report = contextManager.generateConsistencyReport();
  
  console.log("一致性评分:", report.overallScore);
  console.log("发现的问题:");
  report.issues.forEach((issue, idx) => {
    console.log(`  ${idx + 1}. [${issue.severity}] 第${issue.chapter}章: ${issue.description}`);
    console.log(`     建议: ${issue.suggestion}`);
  });
  
  console.log("发现的优势:");
  report.strengths.forEach((strength, idx) => {
    console.log(`  ${idx + 1}. ${strength}`);
  });
}

/**
 * 性能测试示例
 */
async function performanceTestExample() {
  console.log("\n=== 性能测试示例 ===");
  
  const startTime = Date.now();
  
  const contextManager = new LongContextManager({
    enableSummaryExtraction: true,
    enableContextInjection: true,
    debug: false // 关闭调试以获得更准确的性能数据
  });
  
  contextManager.initializeNewReport("性能测试报告");
  
  // 模拟生成6个完整章节
  for (let chapterNum = 1; chapterNum <= 6; chapterNum++) {
    const chapterStartTime = Date.now();
    
    // 模拟章节内容（较大的内容）
    const mockContent = `第${chapterNum}章内容`.repeat(500); // 约2500字符
    
    await contextManager.processChapterCompletion(
      chapterNum,
      `第${chapterNum}章`,
      mockContent,
      mockModelProvider as any
    );
    
    const chapterTime = Date.now() - chapterStartTime;
    console.log(`第${chapterNum}章处理耗时: ${chapterTime}ms`);
  }
  
  const totalTime = Date.now() - startTime;
  console.log(`总耗时: ${totalTime}ms`);
  
  // 内存使用情况
  const stats = contextManager.getContextStats();
  console.log("最终统计:", stats);
  
  // 测试导出性能
  const exportStartTime = Date.now();
  const exportData = contextManager.exportContextData();
  const exportTime = Date.now() - exportStartTime;
  
  console.log(`导出耗时: ${exportTime}ms`);
  console.log(`导出数据大小: ${exportData.length} 字符`);
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  try {
    await basicUsageExample();
    await promptEnhancementExample();
    await consistencyCheckExample();
    await performanceTestExample();
    
    console.log("\n=== 所有示例执行完成 ===");
  } catch (error) {
    console.error("示例执行出错:", error);
  }
}

// 导出示例函数供其他模块使用
export {
  basicUsageExample,
  promptEnhancementExample,
  consistencyCheckExample,
  performanceTestExample,
  runAllExamples
};

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  runAllExamples();
} 