import { useSettingStore } from "../store/setting";
import { generateText } from "ai";
import { createAIProvider } from "@/utils/deep-research/provider";
import { multiApiKeyPolling } from "@/utils/model";
import { generateSignature } from "@/utils/signature";
import { completePath } from "@/utils/url";
import { removeThinkTags } from "@/utils/formatFixer";
import {
  GEMINI_BASE_URL,
  OPENAI_BASE_URL,
  DEEPSEEK_BASE_URL,
} from "@/constants/urls";

export interface DualModelValidationOptions {
  mode: 'chapter' | 'fulltext';
  localKnowledge?: string;
  chapterContext?: string;
  originalWritingModel?: string;
}

export interface DualModelValidationResult {
  originalContent: string;
  validatedContent: string;
  hasChanges: boolean;
  suggestions?: string;
  validationSummary?: string;
}

/**
 * 检查验证模型是否应该启用搜索grounding
 */
function shouldUseSearchGrounding() {
  const settings = useSettingStore.getState();
  const { validationProvider, validationNetworkingEnabled } = settings;
  
  // 如果未启用联网搜索，返回false
  if (validationNetworkingEnabled !== 'enable') {
    return false;
  }
  
  // 目前只有Google提供商支持搜索grounding
  return validationProvider === 'google';
}

/**
 * 创建验证模型提供者
 */
async function createValidationModelProvider() {
  const settings = useSettingStore.getState();
  const { mode, accessPassword, validationProvider, validationApiKey, validationApiProxy, validationModel } = settings;
  
  const options: any = {
    provider: validationProvider,
    model: validationModel,
  };

  // 如果是Google提供商且启用了联网搜索，添加搜索grounding配置
  if (shouldUseSearchGrounding()) {
    options.useSearchGrounding = true;
  }

  switch (validationProvider) {
    case "google":
      if (mode === "local") {
        options.baseURL = completePath(
          validationApiProxy || GEMINI_BASE_URL,
          "/v1beta"
        );
        options.apiKey = multiApiKeyPolling(validationApiKey || "");
      } else {
        options.baseURL = "/api/ai/google/v1beta";
      }
      break;
    case "openai":
      if (mode === "local") {
        options.baseURL = completePath(
          validationApiProxy || OPENAI_BASE_URL,
          "/v1"
        );
        options.apiKey = multiApiKeyPolling(validationApiKey || "");
      } else {
        options.baseURL = "/api/ai/openai/v1";
      }
      break;
    case "deepseek":
      if (mode === "local") {
        options.baseURL = completePath(
          validationApiProxy || DEEPSEEK_BASE_URL,
          "/v1"
        );
        options.apiKey = multiApiKeyPolling(validationApiKey || "");
      } else {
        options.baseURL = "/api/ai/deepseek/v1";
      }
      break;
    case "openaicompatible":
      if (mode === "local") {
        options.baseURL = completePath(validationApiProxy || "", "/v1");
        options.apiKey = multiApiKeyPolling(validationApiKey || "");
      } else {
        options.baseURL = "/api/ai/openaicompatible/v1";
      }
      break;
    default:
      // 对于其他提供商，使用默认配置
      break;
  }

  if (mode === "proxy") {
    options.apiKey = generateSignature(accessPassword, Date.now());
  }
  
  return await createAIProvider(options);
}

/**
 * 创建写作模型提供者（使用指定的模型，确保与第一次写作保持一致）
 */
async function createWritingModelProvider(specifiedModel?: string) {
  const settings = useSettingStore.getState();
  const { mode, provider, accessPassword } = settings;
  
  // 🔥 如果指定了模型，使用指定的模型；否则使用当前的thinkingModel
  const modelToUse = specifiedModel || settings.thinkingModel;
  
  const options: any = {
    provider,
    model: modelToUse,
  };

  switch (provider) {
    case "google":
      const { apiKey = "", apiProxy } = settings;
      if (mode === "local") {
        options.baseURL = completePath(
          apiProxy || GEMINI_BASE_URL,
          "/v1beta"
        );
        options.apiKey = multiApiKeyPolling(apiKey);
      } else {
        options.baseURL = "/api/ai/google/v1beta";
      }
      break;
    case "openai":
      const { openAIApiKey = "", openAIApiProxy } = settings;
      if (mode === "local") {
        options.baseURL = completePath(
          openAIApiProxy || OPENAI_BASE_URL,
          "/v1"
        );
        options.apiKey = multiApiKeyPolling(openAIApiKey);
      } else {
        options.baseURL = "/api/ai/openai/v1";
      }
      break;
    case "deepseek":
      const { deepseekApiKey = "", deepseekApiProxy } = settings;
      if (mode === "local") {
        options.baseURL = completePath(
          deepseekApiProxy || DEEPSEEK_BASE_URL,
          "/v1"
        );
        options.apiKey = multiApiKeyPolling(deepseekApiKey);
      } else {
        options.baseURL = "/api/ai/deepseek/v1";
      }
      break;
    case "openaicompatible":
      const { openAICompatibleApiKey = "", openAICompatibleApiProxy } = settings;
      if (mode === "local") {
        options.baseURL = completePath(openAICompatibleApiProxy, "/v1");
        options.apiKey = multiApiKeyPolling(openAICompatibleApiKey);
      } else {
        options.baseURL = "/api/ai/openaicompatible/v1";
      }
      break;
    default:
      // 对于其他提供商，使用默认配置
      break;
  }

  if (mode === "proxy") {
    options.apiKey = generateSignature(accessPassword, Date.now());
  }
  
  // 写作模型设置
  
  return await createAIProvider(options);
}

/**
 * 创建验证提示词（验证模型使用）
 */
function createValidationPrompt(
  content: string,
  options: DualModelValidationOptions
): string {
  const settings = useSettingStore.getState();
  const isNetworkingEnabled = settings.validationNetworkingEnabled === 'enable';
  
  const networkingInstructions = isNetworkingEnabled ? `
6. **联网验证**：如果遇到需要验证的最新信息、实时数据或外部事实，可以使用联网搜索功能来核实准确性
7. **信息时效性**：检查时间敏感信息是否为最新数据，必要时通过联网搜索获取最新信息
` : `
6. **基于现有资源**：仅基于提供的本地知识库内容进行验证，不进行外部信息查询
`;

  const baseRules = `
你是一个专业的内容审核专家，负责检查AI生成内容中的问题并提供具体的修改建议。

**重要：请只输出具体的修改建议，不要直接修改内容。**

核心检查要点：
1. **引用准确性**：检查所有[L-1]、[L-2]等本地资源引用是否与提供的知识库内容匹配
2. **事实验证**：检查具体的事实、数据、人名、公司名、时间等是否有依据
3. **逻辑一致性**：检查论述是否前后一致，无矛盾
4. **内容真实性**：识别可能的AI自创信息，确保所有重要论点都有本地资源支撑
5. **数据准确性**：验证数字、比例、统计数据等的准确性${networkingInstructions}

输出格式：
如果发现问题，请按以下格式输出修改建议：
1. 问题描述：[具体问题]
2. 修改建议：[具体的修改方案]
3. 理由：[为什么需要这样修改]

如果没有发现问题，请输出：无需修改
`;

  if (options.mode === 'chapter') {
    return `${baseRules}

${options.localKnowledge ? `
参考知识库内容：
${options.localKnowledge}

` : ''}待检查的章节内容：
${content}

请提供修改建议：`;
  } else {
    return `${baseRules}

${options.chapterContext ? `
章节上下文：
${options.chapterContext}

` : ''}待检查的完整内容：
${content}

请提供修改建议：`;
  }
}

/**
 * 创建修改应用提示词（写作模型使用）
 */
function createImprovementPrompt(
  originalContent: string,
  suggestions: string
): string {
  return `
你是一个专业的内容编辑，需要根据审核专家的建议来改进内容。

**重要：请直接输出改进后的内容，保持原有的格式和结构。**

修改原则：
- 严格按照审核建议进行修改
- 保持原文的写作风格和语调
- 保持原有的格式结构（标题、段落、引用等）
- 只修改有问题的部分，其他内容保持不变
- 如果建议是"无需修改"，则直接返回原内容

审核专家的修改建议：
${suggestions}

原始内容：
${originalContent}

请输出改进后的内容：`;
}

/**
 * 执行双模型验证
 */
export async function validateContentWithDualModel(
  content: string,
  options: DualModelValidationOptions
): Promise<DualModelValidationResult> {
  const settings = useSettingStore.getState();
  
  // 检查是否启用了双模型验证
  if (settings.enableDualModelValidation !== 'enable') {
    return {
      originalContent: content,
      validatedContent: content,
      hasChanges: false,
    };
  }

  try {
    // 第一步：验证模型生成修改建议
    const validationPrompt = createValidationPrompt(content, options);
    
    // 🔥 增强的验证模型调用，支持grounding元数据捕获
    const validationResult = await generateText({
      model: await createValidationModelProvider(),
      system: "你是一个专业的内容审核专家，专门负责检查AI生成内容中的问题并提供修改建议。",
      prompt: validationPrompt,
    });
    
    // 🔥 检查和记录grounding元数据
    if ((validationResult as any).providerMetadata?.google) {
      const googleMetadata = (validationResult as any).providerMetadata.google;
      if (googleMetadata.groundingMetadata) {
        // 验证模型搜索grounding元数据处理
        
        // 检查具体的grounding数据
        // const { groundingSupports, webSearchQueries, searchEntryPoint } = googleMetadata.groundingMetadata;
      }
    } else {
      // 如果启用了搜索grounding但没有Google元数据，输出警告
      if (shouldUseSearchGrounding()) {
        console.warn('⚠️ 验证模型应该启用搜索grounding但未检测到Google提供商元数据');
        console.warn('🔧 当前验证模型设置:', {
          provider: settings.validationProvider,
          model: settings.validationModel,
          networkingEnabled: settings.validationNetworkingEnabled
        });
      }
    }
    
    // 清理验证结果
    const cleanedValidationResult = removeThinkTags(validationResult.text);
    const suggestions = cleanedValidationResult.content.trim();
    
    // 如果没有建议或建议是"无需修改"，直接返回原内容
    if (!suggestions || suggestions.includes("无需修改") || suggestions.includes("没有发现问题")) {
      return {
        originalContent: content,
        validatedContent: content,
        hasChanges: false,
        suggestions: suggestions,
        validationSummary: "验证完成，未发现问题"
      };
    }
    
    // 第二步：写作模型根据建议修改内容
    const improvementPrompt = createImprovementPrompt(content, suggestions);
    
    const improvementResult = await generateText({
      model: await createWritingModelProvider(options.originalWritingModel),
      system: "你是一个专业的内容编辑，负责根据审核建议改进内容。",
      prompt: improvementPrompt,
    });
    
    // 清理改进结果
    const cleanedImprovementResult = removeThinkTags(improvementResult.text);
    let validatedContent = cleanedImprovementResult.content.trim();
    
    // 如果改进后内容为空，使用原内容
    if (!validatedContent || validatedContent.length === 0) {
      validatedContent = content;
    }
    
    return {
      originalContent: content,
      validatedContent: validatedContent,
      hasChanges: validatedContent !== content.trim(),
      suggestions: suggestions,
      validationSummary: "双模型验证完成"
    };
    
  } catch (error) {
    console.warn('双模型验证失败，使用原内容:', error);
    return {
      originalContent: content,
      validatedContent: content,
      hasChanges: false,
      validationSummary: "验证失败"
    };
  }
}

/**
 * 检查是否启用双模型验证
 */
export function isDualModelValidationEnabled(): boolean {
  const settings = useSettingStore.getState();
  return settings.enableDualModelValidation === 'enable';
}

/**
 * 检查验证模型是否启用联网搜索
 */
export function isValidationNetworkingEnabled(): boolean {
  const settings = useSettingStore.getState();
  return settings.validationNetworkingEnabled === 'enable';
} 