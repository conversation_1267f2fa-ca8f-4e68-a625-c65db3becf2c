// API 工具函数

/**
 * 获取本地知识库路径配置
 * 这个路径用于生成本地资源的可点击链接
 */
export function getLocationKnowledgePath(): string {
  // 从环境变量获取路径配置
  const path = process.env.LOCATION_KNOWLEDGE_PATH || process.env.NEXT_PUBLIC_LOCATION_KNOWLEDGE_PATH || '';
  
  // 如果配置了路径，确保格式正确
  if (path) {
    // 移除末尾的斜杠，统一处理
    return path.replace(/\/+$/, '');
  }
  
  return '';
}

/**
 * 获取系统配置信息
 */
export function getSystemConfig() {
  return {
    locationKnowledgePath: getLocationKnowledgePath(),
  };
} 