/**
 * Token计算工具
 * 用于估算文本在不同大模型中的token消耗
 */

/**
 * 简单的token估算（基于字符数）
 * 对于中文：约1.5-2个字符 = 1个token
 * 对于英文：约4个字符 = 1个token
 * 对于混合文本：使用加权平均
 */
export function estimateTokenCount(text: string): number {
  if (!text) return 0;
  
  // 统计中文字符数（包括中文标点）
  const chineseChars = (text.match(/[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]/g) || []).length;
  
  // 统计英文字符数（包括数字、英文标点、空格）
  const englishChars = text.length - chineseChars;
  
  // 中文：1.5个字符 ≈ 1个token
  // 英文：4个字符 ≈ 1个token
  const chineseTokens = Math.ceil(chineseChars / 1.5);
  const englishTokens = Math.ceil(englishChars / 4);
  
  return chineseTokens + englishTokens;
}

/**
 * 更精确的token估算（考虑不同模型的编码差异）
 */
export function estimateTokenCountByModel(text: string, model: 'gpt' | 'claude' | 'gemini' | 'other' = 'gpt'): number {
  if (!text) return 0;
  
  const chineseChars = (text.match(/[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]/g) || []).length;
  const englishChars = text.length - chineseChars;
  
  // 不同模型的编码效率略有差异
  const modelRatios = {
    gpt: { chinese: 1.5, english: 4 },      // GPT系列
    claude: { chinese: 1.6, english: 4.2 }, // Claude系列
    gemini: { chinese: 1.4, english: 3.8 }, // Gemini系列
    other: { chinese: 1.5, english: 4 }     // 其他模型
  };
  
  const ratio = modelRatios[model];
  const chineseTokens = Math.ceil(chineseChars / ratio.chinese);
  const englishTokens = Math.ceil(englishChars / ratio.english);
  
  return chineseTokens + englishTokens;
}

/**
 * 格式化token数量显示
 */
export function formatTokenCount(tokenCount: number): string {
  if (tokenCount < 1000) {
    return `${tokenCount} tokens`;
  } else if (tokenCount < 10000) {
    return `${(tokenCount / 1000).toFixed(1)}K tokens`;
  } else {
    return `${(tokenCount / 1000).toFixed(0)}K tokens`;
  }
}

/**
 * 计算文本的字节大小
 */
export function getTextByteSize(str: string): number {
  return new TextEncoder().encode(str).length;
}

/**
 * 格式化字节大小显示
 */
export function formatByteSize(bytes: number): string {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
}

/**
 * 获取文本统计信息
 */
export function getTextStats(text: string) {
  if (!text) {
    return {
      characters: 0,
      chineseChars: 0,
      englishChars: 0,
      words: 0,
      lines: 0,
      tokens: 0,
      bytes: 0,
      formattedTokens: '0 tokens',
      formattedBytes: '0 B'
    };
  }
  
  const characters = text.length;
  const chineseChars = (text.match(/[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]/g) || []).length;
  const englishChars = characters - chineseChars;
  const words = text.split(/\s+/).filter(word => word.length > 0).length;
  const lines = text.split('\n').length;
  const tokens = estimateTokenCount(text);
  const bytes = getTextByteSize(text);
  
  return {
    characters,
    chineseChars,
    englishChars,
    words,
    lines,
    tokens,
    bytes,
    formattedTokens: formatTokenCount(tokens),
    formattedBytes: formatByteSize(bytes)
  };
}

/**
 * 检查文本是否超过token限制
 */
export function checkTokenLimit(text: string, limit: number = 4000): {
  isOverLimit: boolean;
  currentTokens: number;
  limitTokens: number;
  percentage: number;
} {
  const currentTokens = estimateTokenCount(text);
  const isOverLimit = currentTokens > limit;
  const percentage = Math.round((currentTokens / limit) * 100);
  
  return {
    isOverLimit,
    currentTokens,
    limitTokens: limit,
    percentage
  };
} 