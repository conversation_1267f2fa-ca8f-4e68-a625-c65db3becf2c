"use client";
import { useState } from "react";

import { Plus, Trash2, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/Internal/Button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useOutlineStore } from "@/store/outline";

// 安全的日期格式化函数
const formatDate = (date: Date | string): string => {
  try {
    if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    return new Date(date).toLocaleDateString();
  } catch {
    return '无效日期';
  }
};

export default function OutlineTemplates() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [newTemplateDescription, setNewTemplateDescription] = useState('');
  
  const outlineStore = useOutlineStore();
  const { templates, currentOutline } = outlineStore;

  const handleCreateTemplate = () => {
    if (!currentOutline || !newTemplateName.trim()) return;
    
    outlineStore.saveAsTemplate(
      currentOutline.id,
      newTemplateName.trim(),
      newTemplateDescription.trim()
    );
    
    setNewTemplateName('');
    setNewTemplateDescription('');
    setShowCreateDialog(false);
  };

  const handleApplyTemplate = (templateId: string) => {
    outlineStore.applyTemplate(templateId);
  };

  const handleDeleteTemplate = (templateId: string) => {
    if (confirm('确定要删除这个模板吗？')) {
      outlineStore.deleteTemplate(templateId);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'builtin': return 'bg-blue-100 text-blue-800';
      case 'custom': return 'bg-green-100 text-green-800';
      case 'imported': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'builtin': return '内置';
      case 'custom': return '自定义';
      case 'imported': return '导入';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">模板管理</h3>
        <div className="flex space-x-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button disabled={!currentOutline}>
                <Plus className="w-4 h-4 mr-1" />
                保存为模板
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>保存为模板</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">模板名称</label>
                  <Input
                    value={newTemplateName}
                    onChange={(e) => setNewTemplateName(e.target.value)}
                    placeholder="输入模板名称"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">模板描述</label>
                  <Textarea
                    value={newTemplateDescription}
                    onChange={(e) => setNewTemplateDescription(e.target.value)}
                    placeholder="输入模板描述"
                    rows={3}
                  />
                </div>
                
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    取消
                  </Button>
                  <Button 
                    onClick={handleCreateTemplate}
                    disabled={!newTemplateName.trim()}
                  >
                    保存模板
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 模板列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {templates.map((template) => (
          <Card key={template.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <CardTitle className="text-base">{template.name}</CardTitle>
                    <Badge className={getCategoryColor(template.category)}>
                      {getCategoryLabel(template.category)}
                    </Badge>
                    {template.isBuiltIn && (
                      <Star className="w-4 h-4 text-yellow-500" />
                    )}
                  </div>
                  
                  {template.description && (
                    <p className="text-sm text-muted-foreground">
                      {template.description}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                {/* 模板统计 */}
                <div className="grid grid-cols-2 gap-4 text-center text-sm">
                  <div>
                    <div className="font-semibold text-blue-600">
                      {template.chapters.length}
                    </div>
                    <div className="text-muted-foreground">章节</div>
                  </div>
                  
                  <div>
                    <div className="font-semibold text-green-600">
                      {template.chapters.reduce((sum: number, chapter: any) => sum + chapter.sections.length, 0)}
                    </div>
                    <div className="text-muted-foreground">子章节</div>
                  </div>
                </div>
                
                {/* 标签 */}
                {template.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {template.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
                
                {/* 操作按钮 */}
                <div className="flex justify-between items-center pt-2">
                  <div className="text-xs text-muted-foreground">
                    {formatDate(template.createdAt)}
                  </div>
                  
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      onClick={() => handleApplyTemplate(template.id)}
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      应用
                    </Button>
                    
                    {!template.isBuiltIn && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteTemplate(template.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {templates.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-muted-foreground">
              还没有保存的模板。创建一个大纲后可以保存为模板。
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
 