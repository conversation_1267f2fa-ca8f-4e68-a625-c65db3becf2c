"use client";
import { useState } from "react";

import { Plus, Edit2, Trash2, GripVertical, Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useOutlineStore } from "@/store/outline";
import { OutlineSection } from "@/types/outline";
import SectionEditor from "./SectionEditor";

interface SectionListProps {
  chapterId: string;
  sections: OutlineSection[];
}

export default function SectionList({ chapterId, sections }: SectionListProps) {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [showAddSection, setShowAddSection] = useState(false);
  
  const outlineStore = useOutlineStore();

  const handleAddSection = () => {
    setShowAddSection(true);
    setEditingSection(null);
  };

  const handleEditSection = (sectionId: string) => {
    setEditingSection(sectionId);
    setShowAddSection(false);
  };

  const handleDeleteSection = (sectionId: string) => {
    if (confirm('确定要删除这个子章节吗？')) {
      outlineStore.deleteSection(chapterId, sectionId);
    }
  };

  const handleToggleSectionEnabled = (sectionId: string, enabled: boolean) => {
    outlineStore.updateSection(chapterId, sectionId, { enabled });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h6 className="font-medium">子章节 ({sections.length})</h6>
        <Button size="sm" onClick={handleAddSection}>
          <Plus className="w-4 h-4 mr-1" />
          添加子章节
        </Button>
      </div>

      {/* 添加新子章节 */}
      {showAddSection && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <SectionEditor
              onSave={(newSection: Omit<OutlineSection, 'id'>) => {
                console.log('📋 SectionList添加子章节:', { chapterId, newSection });
                outlineStore.addSection(chapterId, newSection);
                setShowAddSection(false);
              }}
              onCancel={() => setShowAddSection(false)}
            />
          </CardContent>
        </Card>
      )}

      {/* 子章节列表 */}
      <div className="space-y-3">
        {sections.map((section) => (
          <Card key={section.id} className={!section.enabled ? 'opacity-60' : ''}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
                  
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">
                      {section.number}、{section.title}
                    </span>
                                         <Badge className="bg-blue-100 text-blue-800">
                       中
                     </Badge>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                                         <Switch
                       checked={section.enabled}
                       onCheckedChange={(checked) => 
                         handleToggleSectionEnabled(section.id, checked)
                       }
                     />
                    {section.enabled ? (
                      <Eye className="w-4 h-4 text-green-600" />
                    ) : (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleEditSection(section.id)}
                  >
                    <Edit2 className="w-4 h-4" />
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleDeleteSection(section.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {editingSection === section.id ? (
                <SectionEditor
                  section={section}
                  onSave={(updatedSection: Omit<OutlineSection, 'id'>) => {
                    console.log('📋 SectionList更新子章节:', { chapterId, sectionId: section.id, updatedSection });
                    outlineStore.updateSection(chapterId, section.id, updatedSection);
                    setEditingSection(null);
                  }}
                  onCancel={() => setEditingSection(null)}
                />
              ) : (
                <div className="space-y-3">
                  {section.description && (
                    <p className="text-sm text-muted-foreground">
                      {section.description}
                    </p>
                  )}
                  
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>
                      字数: {section.wordCount.min}-{section.wordCount.max}字
                    </span>
                  </div>
                  
                  {section.researchPoints.length > 0 && (
                    <div>
                      <h6 className="text-xs font-medium mb-1">研究要点:</h6>
                      <ul className="list-disc list-inside space-y-1 text-xs text-muted-foreground">
                        {section.researchPoints.map((point, index) => (
                          <li key={index}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {sections.length === 0 && !showAddSection && (
        <Card className="border-dashed">
          <CardContent className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              还没有子章节。点击&ldquo;添加子章节&rdquo;开始创建。
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 