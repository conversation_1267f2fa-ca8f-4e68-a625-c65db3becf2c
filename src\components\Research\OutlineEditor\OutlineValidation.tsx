"use client";
import { useState, useEffect, useCallback } from "react";

import { CheckCircle, AlertTriangle, AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useOutlineStore } from "@/store/outline";
import { ResearchOutline, OutlineValidationResult } from "@/types/outline";

interface OutlineValidationProps {
  outline: ResearchOutline;
}

export default function OutlineValidation({ outline }: OutlineValidationProps) {
  const [validationResult, setValidationResult] = useState<OutlineValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  
  const outlineStore = useOutlineStore();

  const runValidation = useCallback(() => {
    setIsValidating(true);
    
    // 模拟异步验证过程
    setTimeout(() => {
      const result = outlineStore.validateOutline(outline);
      setValidationResult(result);
      setIsValidating(false);
    }, 500);
  }, [outline, outlineStore]);

  useEffect(() => {
    runValidation();
  }, [outline, runValidation]);

  const getStatusIcon = () => {
    if (isValidating) {
      return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;
    }
    
    if (!validationResult) {
      return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
    
    if (validationResult.isValid) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    
    return <AlertTriangle className="w-5 h-5 text-red-500" />;
  };

  const getStatusText = () => {
    if (isValidating) return "验证中...";
    if (!validationResult) return "未验证";
    if (validationResult.isValid) return "验证通过";
    return "验证失败";
  };

  const getStatusColor = () => {
    if (isValidating) return "text-blue-600";
    if (!validationResult) return "text-gray-600";
    if (validationResult.isValid) return "text-green-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h4 className="text-lg font-semibold">大纲验证</h4>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className={`font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>
        </div>
        
        <Button
          variant="outline"
          onClick={runValidation}
          disabled={isValidating}
        >
          <RefreshCw className={`w-4 h-4 mr-1 ${isValidating ? 'animate-spin' : ''}`} />
          重新验证
        </Button>
      </div>

      {validationResult && (
        <div className="space-y-4">
          {/* 验证概览 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">验证概览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {validationResult.errors.length}
                  </div>
                  <div className="text-sm text-muted-foreground">错误</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">
                    {validationResult.warnings.length}
                  </div>
                  <div className="text-sm text-muted-foreground">警告</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {validationResult.suggestions.length}
                  </div>
                  <div className="text-sm text-muted-foreground">建议</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 错误列表 */}
          {validationResult.errors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base text-red-600 flex items-center">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  错误 ({validationResult.errors.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {validationResult.errors.map((error, index) => (
                    <Alert key={index} variant="destructive">
                      <AlertDescription>
                        <div className="font-medium">{error.message}</div>
                        {error.chapterId && (
                          <div className="text-sm mt-1">
                            章节ID: {error.chapterId}
                          </div>
                        )}
                        {error.sectionId && (
                          <div className="text-sm mt-1">
                            子章节ID: {error.sectionId}
                          </div>
                        )}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 警告列表 */}
          {validationResult.warnings.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base text-yellow-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  警告 ({validationResult.warnings.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {validationResult.warnings.map((warning, index) => (
                    <Alert key={index}>
                      <AlertDescription>
                        <div className="font-medium">{warning.message}</div>
                        {warning.chapterId && (
                          <div className="text-sm mt-1">
                            章节ID: {warning.chapterId}
                          </div>
                        )}
                        {warning.sectionId && (
                          <div className="text-sm mt-1">
                            子章节ID: {warning.sectionId}
                          </div>
                        )}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 建议列表 */}
          {validationResult.suggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base text-blue-600 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  优化建议 ({validationResult.suggestions.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {validationResult.suggestions.map((suggestion, index) => (
                    <Alert key={index}>
                      <AlertDescription>
                        {suggestion}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 验证通过 */}
          {validationResult.isValid && 
           validationResult.errors.length === 0 && 
           validationResult.warnings.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-600 mb-2">
                  大纲验证通过！
                </h3>
                <p className="text-muted-foreground">
                  您的大纲结构完整，可以开始研究和写作了。
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* 验证规则说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">验证规则说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div>
              <div className="font-medium text-red-600">错误检查:</div>
              <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                <li>大纲标题不能为空</li>
                <li>章节标题不能为空</li>
                <li>字数范围设置必须合理（最小值 &lt; 最大值）</li>
                <li>不能有重复的章节</li>
                <li>不能有循环依赖关系</li>
              </ul>
            </div>
            
            <div>
              <div className="font-medium text-yellow-600">警告检查:</div>
              <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                <li>字数设置过少或过多</li>
                <li>缺少研究要点</li>
                <li>章节间字数分配不均衡</li>
              </ul>
            </div>
            
            <div>
              <div className="font-medium text-blue-600">优化建议:</div>
              <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                <li>章节数量和结构优化</li>
                <li>内容组织和逻辑改进</li>
                <li>研究重点分配建议</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 