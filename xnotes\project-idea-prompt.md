Draft your initial prompt or ideas for a project here. Use this to then kickstart the project with the cursor agent mode when using the agile workflow, documented in docs/agile-readme.md. After the initial prd is drafted, work with the LLM in cursor or with an external LLM to ask questions, have the LLM ask you questions, etc., to really define an adequate prd and story list. Then continue with generating of the architecture document to ensure the project is built in a way that is easy to maintain and scale as you need it to be, along with a clear specification of what technologies and libraries you want to use. This will also help you figure out what rules you might want to initial generate to help you build the project.

Example:

Let's build a nextJs 15 web app to track our monthly income and expenses. I want a modern UI created with tailwind css and shadcn components, secure storage in supabase, and a modern API. I also want it to integrate social login via facebook or google. It also needs to be mobile friendly so I can input expenses on the go quickly, and also access all information when I need to. I envision a login page if I am not authenticated already, and once authenticated a main landing page that shows my overall account balance minus expenses prominently along with the 5 most recent income and expense entries. I would like from the page a very quick mobile friendly way to enter a quick expense or income with minimally the amount and a description. All entries should be saved automatically and securely. I should be logged out automatically if not active for more than 5 minutes.

{ The more details to drive the initial prd draft the better! BUT, you don't have to think of everything up front, get the draft prd done, and then use the AI to communicate with as a PRD expert, and then an architecture expert to further flesh out the details! Also be open to allowing the AI expert to suggest libraries and technology choices if there is something you are not too particular about. Some apps may be better suited to the one you know best, and this can also help you get exposure and learn new technologies. Consider using deeper web research so you are not constrained to the LLM of choice internal knowledge cut offs, you can enable this through MCP to expand the llm capabilities to use perplexity, tavily, or basic web searches to ensure you will be using the latest and greatest available models and libraries. It is also recommended if doing this in Cursor to select the Sonnet or Deepseek Thinking Agent modes, or use a mcp plugin that supports deeper thought. }
