/**
 * AI内容验证工具
 * 用于减少AI幻觉问题
 */

import { useSettingStore } from "../store/setting";
import { generateText } from "ai";
import { createAIProvider } from "@/utils/deep-research/provider";
import { multiApiKeyPolling } from "@/utils/model";
import { generateSignature } from "@/utils/signature";
import { completePath } from "@/utils/url";
import { removeThinkTags } from "@/utils/formatFixer";
import {
  GEMINI_BASE_URL,
  OPENAI_BASE_URL,
  DEEPSEEK_BASE_URL,
} from "@/constants/urls";

export interface AIValidationOptions {
  mode: 'chapter' | 'fulltext';
  localKnowledge?: string;
  chapterContext?: string;
}

export interface AIValidationResult {
  originalContent: string;
  validatedContent: string;
  hasChanges: boolean;
  validationSummary?: string;
}

/**
 * 创建AI模型提供者（非Hook版本，用于工具函数）
 */
async function createModelProvider(model: string, settings?: any) {
  const { mode, provider, accessPassword } = useSettingStore.getState();
  const options: any = {
    provider,
    model,
    settings,
  };

  switch (provider) {
    case "google":
      const { apiKey = "", apiProxy } = useSettingStore.getState();
      if (mode === "local") {
        options.baseURL = completePath(
          apiProxy || GEMINI_BASE_URL,
          "/v1beta"
        );
        options.apiKey = multiApiKeyPolling(apiKey);
      } else {
        options.baseURL = "/api/ai/google/v1beta";
      }
      break;
    case "openai":
      const { openAIApiKey = "", openAIApiProxy } =
        useSettingStore.getState();
      if (mode === "local") {
        options.baseURL = completePath(
          openAIApiProxy || OPENAI_BASE_URL,
          "/v1"
        );
        options.apiKey = multiApiKeyPolling(openAIApiKey);
      } else {
        options.baseURL = "/api/ai/openai/v1";
      }
      break;
    case "deepseek":
      const { deepseekApiKey = "", deepseekApiProxy } =
        useSettingStore.getState();
      if (mode === "local") {
        options.baseURL = completePath(
          deepseekApiProxy || DEEPSEEK_BASE_URL,
          "/v1"
        );
        options.apiKey = multiApiKeyPolling(deepseekApiKey);
      } else {
        options.baseURL = "/api/ai/deepseek/v1";
      }
      break;
    case "openaicompatible":
      const { openAICompatibleApiKey = "", openAICompatibleApiProxy } =
        useSettingStore.getState();
      if (mode === "local") {
        options.baseURL = completePath(openAICompatibleApiProxy, "/v1");
        options.apiKey = multiApiKeyPolling(openAICompatibleApiKey);
      } else {
        options.baseURL = "/api/ai/openaicompatible/v1";
      }
      break;
    default:
      // 对于其他提供商，使用默认配置
      break;
  }

  if (mode === "proxy") {
    options.apiKey = generateSignature(accessPassword, Date.now());
  }
  
  return await createAIProvider(options);
}

/**
 * 执行AI内容验证
 */
export async function validateContentWithAI(
  content: string,
  options: AIValidationOptions
): Promise<AIValidationResult> {
  const settings = useSettingStore.getState();
  
  // 检查是否启用了对应的验证功能
  if (options.mode === 'chapter' && settings.enableChapterValidation !== 'enable') {
    return {
      originalContent: content,
      validatedContent: content,
      hasChanges: false,
    };
  }
  
  if (options.mode === 'fulltext' && settings.enableFullTextValidation !== 'enable') {
    return {
      originalContent: content,
      validatedContent: content,
      hasChanges: false,
    };
  }

  try {
    const prompt = createValidationPrompt(content, options);
    
    // 使用项目现有的AI调用机制
    const result = await generateText({
      model: await createModelProvider(settings.thinkingModel),
      system: "你是一个专业的内容审核专家，专门负责检查和修正AI生成内容中的幻觉问题。",
      prompt: prompt,
    });
    
    // 清理结果中的思考标签和说明文字
    const cleanedResult = removeThinkTags(result.text);
    let validatedContent = cleanedResult.content.trim();

    // 移除可能的说明文字和标题
    validatedContent = validatedContent
      .replace(/^.*?章节级验证.*?\n/i, '')
      .replace(/^.*?全文级验证.*?\n/i, '')
      .replace(/^.*?本地知识库内容.*?\n/i, '')
      .replace(/^.*?修正后的.*?内容.*?\n/i, '')
      .replace(/^.*?检查结果.*?\n/i, '')
      .replace(/^.*?验证结果.*?\n/i, '')
      .replace(/^.*?以下是.*?\n/i, '')
      .replace(/^---+\n/gm, '')
      .replace(/^#+\s*修正后的内容.*?\n/i, '')
      .replace(/^#+\s*验证结果.*?\n/i, '')
      .replace(/^#+\s*章节级验证.*?\n/i, '')
      .replace(/^#+\s*全文级验证.*?\n/i, '')
      .replace(/^参考知识库内容：[\s\S]*?\n\n/, '')
      .replace(/^待检查的内容：[\s\S]*?\n\n/, '')
      .replace(/^请直接输出修正后的内容：.*?\n/i, '')
      .trim();

    // 如果清理后内容为空，使用原内容
    if (!validatedContent || validatedContent.length === 0) {
      validatedContent = content;
    }
    
    return {
      originalContent: content,
      validatedContent: validatedContent,
      hasChanges: validatedContent !== content.trim() && validatedContent.length > 0,
      validationSummary: `${options.mode}级验证完成`
    };
  } catch (error) {
    console.warn('AI验证失败，使用原内容:', error);
    return {
      originalContent: content,
      validatedContent: content,
      hasChanges: false,
    };
  }
}

/**
 * 创建验证提示词
 */
function createValidationPrompt(
  content: string,
  options: AIValidationOptions
): string {
  const baseRules = `
你是一个专业的内容审核专家，负责检查和修正AI生成内容中的幻觉问题。

**重要：请直接输出修正后的内容，不要添加任何说明、标题、前言或后缀。**

核心检查要点：
1. **引用准确性**：检查所有[L-1]、[L-2]等本地资源引用是否与提供的知识库内容匹配
2. **事实验证**：检查具体的事实、数据、人名、公司名、时间等是否有依据
3. **逻辑一致性**：检查论述是否前后一致，无矛盾
4. **内容真实性**：识别可能的AI自创信息，确保所有重要论点都有本地资源支撑
5. **数据准确性**：验证数字、比例、统计数据等的准确性

修改原则：
- 只修正明确错误的内容
- 保持原文风格和结构
- 对于无法验证的内容，添加适当的限定词（如"根据提供资料"、"据了解"等）
- 删除明显的AI幻觉内容
- 确保所有引用都有对应的依据

**输出要求：直接输出修正后的内容，不要包含"修正后的内容："、"章节级验证"等标题或说明文字。**
`;

  if (options.mode === 'chapter') {
    return `${baseRules}

${options.localKnowledge ? `
参考知识库内容：
${options.localKnowledge}

` : ''}待检查的内容：
${content}

请直接输出修正后的内容：`;
  } else {
    return `${baseRules}

${options.chapterContext ? `
章节上下文：
${options.chapterContext}

` : ''}待检查的完整内容：
${content}

请直接输出修正后的内容：`;
  }
}

/**
 * 检查是否启用章节验证
 */
export function isChapterValidationEnabled(): boolean {
  const settings = useSettingStore.getState();
  return settings.enableChapterValidation === 'enable';
}

/**
 * 检查是否启用全文验证
 */
export function isFullTextValidationEnabled(): boolean {
  const settings = useSettingStore.getState();
  return settings.enableFullTextValidation === 'enable';
} 