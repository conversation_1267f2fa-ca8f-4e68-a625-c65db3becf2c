const fetch = require('node-fetch');
const { createParser } = require('eventsource-parser');

const ctrl = new AbortController();

let report = "";

async function fetchSSE() {
  const res = await fetch("http://localhost:3000/api/sse", {  // 替换为你的实际地址
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      query: "分析一下江苏集萃集成电路应用技术创新中心有限公司的技术方面的潜在需求。进行此技术需求分析的主要目的是技术成果转化平台中需要企业的潜在需求来匹配成果、专家、机构、相似企业，为该企业提供服务。给我5~10个潜在技术需求，内容包括标题、需求描述、理由。用JSON结构输出",
      provider: "google",
      thinkingModel: "gemini-2.0-flash-thinking-exp",
      taskModel: "gemini-2.0-flash-exp",
      searchProvider: "model",
      language: "zh-CN",
      maxResult: 5,
      enableCitationImage: true,
      enableReferences: true,
    }),
    signal: ctrl.signal,
  });

  const parser = createParser({
    onEvent(event) {
      if (event.type === "event") {
        const msg = JSON.parse(event.data);
        if (msg.type === "text") {
          report += msg.text;
        } else if (msg.type === "progress") {
          console.log(`[${msg.step}] ${msg.name || ""} ${msg.status}`);
          if (msg.data) console.log(msg.data);
        } else if (msg.type === "error") {
          console.error("Error:", msg.message);
        }
      }
    }
  });

  for await (const chunk of res.body) {
    const str = Buffer.from(chunk).toString();
    parser.feed(str);
  }

  console.log("最终结果：", report);
}

fetchSSE().catch(err => {
  console.error("请求失败:", err);
});
