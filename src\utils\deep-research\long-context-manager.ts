/**
 * 长程上下文管理器
 * 整合章节上下文缓存、智能摘要提取和上下文注入，实现万字长文的逻辑一致性
 */

import { ChapterContextCache, ChapterSummary } from './chapter-context-cache';
import { ChapterSummaryExtractor } from './chapter-summary-extractor';
import { ContextInjector, ContextInjectionConfig } from './context-injector';

export interface LongContextConfig {
  enableSummaryExtraction: boolean;    // 启用摘要提取
  enableContextInjection: boolean;     // 启用上下文注入
  enableProgressiveContext: boolean;   // 启用渐进式上下文
  injectionConfig?: Partial<ContextInjectionConfig>;
  debug: boolean;                      // 调试模式
}

export interface ChapterGenerationHooks {
  onChapterStart?: (chapterNum: number, title: string) => void;
  onChapterComplete?: (chapterNum: number, content: string, summary: ChapterSummary) => void;
  onSectionStart?: (chapterNum: number, sectionIndex: number, title: string) => void;
  onContextInjected?: (chapterNum: number, contextLength: number) => void;
  onExtractionComplete?: (chapterNum: number, summary: ChapterSummary, quality: string) => void;
}

export class LongContextManager {
  private contextCache: ChapterContextCache;
  private summaryExtractor: ChapterSummaryExtractor;
  private contextInjector: ContextInjector;
  private config: LongContextConfig;
  private hooks: ChapterGenerationHooks;

  constructor(
    config: Partial<LongContextConfig> = {},
    hooks: ChapterGenerationHooks = {}
  ) {
    this.config = {
      enableSummaryExtraction: true,
      enableContextInjection: true,
      enableProgressiveContext: true,
      debug: false,
      ...config
    };

    this.hooks = hooks;

    // 初始化组件
    this.contextCache = new ChapterContextCache();
    this.summaryExtractor = new ChapterSummaryExtractor();
    this.contextInjector = new ContextInjector(
      this.contextCache,
      this.config.injectionConfig
    );

    // 恢复缓存状态
    this.contextCache.restore();
  }

  /**
   * 初始化新的报告会话
   */
  initializeNewReport(reportTitle: string): void {
    this.debugLog('Initializing new report:', reportTitle);
    this.contextCache.clear();
  }

  /**
   * 为子章节生成增强的提示词
   */
  enhanceSubChapterPrompt(
    chapterNum: number,
    sectionIndex: number,
    originalPrompt: string
  ): string {
    this.hooks.onSectionStart?.(chapterNum, sectionIndex, '');

    if (!this.config.enableContextInjection) {
      return originalPrompt;
    }

    try {
      const enhancedPrompt = this.contextInjector.injectContextForSubChapter(
        chapterNum,
        sectionIndex,
        originalPrompt
      );

      // 计算注入的上下文长度
      const contextLength = enhancedPrompt.length - originalPrompt.length;
      this.hooks.onContextInjected?.(chapterNum, contextLength);

      this.debugLog(`Enhanced prompt for Chapter ${chapterNum}.${sectionIndex + 1}, context length: ${contextLength}`);
      
      return enhancedPrompt;

    } catch (error) {
      console.error('Error enhancing prompt:', error);
      return originalPrompt;
    }
  }

  /**
   * 处理章节完成后的摘要提取
   */
  async processChapterCompletion(
    chapterNum: number,
    chapterTitle: string,
    chapterContent: string,
    modelProvider: any
  ): Promise<ChapterSummary | null> {
    this.hooks.onChapterStart?.(chapterNum, chapterTitle);

    if (!this.config.enableSummaryExtraction) {
      return null;
    }

    try {
      this.debugLog(`Processing chapter ${chapterNum} completion`);

      // 提取章节摘要
      const summary = await this.summaryExtractor.extractChapterSummary(
        chapterNum,
        chapterContent,
        chapterTitle,
        modelProvider
      );

      // 验证提取质量
      const qualityCheck = this.summaryExtractor.validateExtractionQuality({
        keyFindings: summary.keyFindings,
        keyData: summary.keyData,
        conclusions: summary.conclusions,
        references: summary.references
      });

      this.debugLog(`Chapter ${chapterNum} extraction quality: ${qualityCheck.quality}`);
      if (qualityCheck.issues.length > 0) {
        console.warn(`Chapter ${chapterNum} extraction issues:`, qualityCheck.issues);
      }

      // 保存到缓存
      this.contextCache.saveChapterSummary(summary);

      this.hooks.onExtractionComplete?.(chapterNum, summary, qualityCheck.quality);
      this.hooks.onChapterComplete?.(chapterNum, chapterContent, summary);

      return summary;

    } catch (error) {
      console.error(`Error processing chapter ${chapterNum} completion:`, error);
      return null;
    }
  }

  /**
   * 获取章节关联性提示
   */
  getChapterConnectionPrompt(chapterNum: number): string {
    if (chapterNum === 1) {
      return '';
    }

    return this.contextInjector.injectContextForChapterSummary(chapterNum);
  }

  /**
   * 获取数据一致性检查提示
   */
  getDataConsistencyPrompt(): string {
    return this.contextInjector.generateConsistencyCheckPrompt();
  }

  /**
   * 手动添加跨章节数据
   */
  addCrossChapterData(key: keyof import('./chapter-context-cache').CrossChapterData, data: any): void {
    try {
      this.contextCache.updateCrossChapterData(key, data);
      this.debugLog(`Added cross-chapter data: ${key}`);
    } catch (error) {
      console.error('Error adding cross-chapter data:', error);
    }
  }

  /**
   * 获取当前上下文统计
   */
  getContextStats(): {
    totalChapters: number;
    totalFindings: number;
    totalConclusions: number;
    crossDataKeys: number;
    lastUpdate: Date | null;
  } {
    const stats = this.contextCache.getStats();
    const allSummaries = Array.from(this.contextCache['chapterSummaries'].values());
    const lastUpdate = allSummaries.length > 0 
      ? new Date(Math.max(...allSummaries.map(s => s.generatedAt.getTime())))
      : null;

    return {
      totalChapters: stats.cachedChapters,
      totalFindings: stats.totalKeyFindings,
      totalConclusions: stats.totalConclusions,
      crossDataKeys: stats.crossDataKeys,
      lastUpdate
    };
  }

  /**
   * 获取章节间关联分析
   */
  getChapterConnectionAnalysis(): {
    connections: Array<{
      from: number;
      to: number;
      commonData: string[];
      relationshipStrength: 'strong' | 'medium' | 'weak';
    }>;
    isolatedChapters: number[];
  } {
    const summaries = Array.from(this.contextCache['chapterSummaries'].values());
    const connections: Array<{
      from: number;
      to: number;
      commonData: string[];
      relationshipStrength: 'strong' | 'medium' | 'weak';
    }> = [];

    // 分析章节间的数据共享关系
    for (let i = 0; i < summaries.length; i++) {
      for (let j = i + 1; j < summaries.length; j++) {
        const summary1 = summaries[i];
        const summary2 = summaries[j];
        
        const commonDataKeys = this.findCommonDataKeys(summary1.keyData, summary2.keyData);
        
        if (commonDataKeys.length > 0) {
          const strength = commonDataKeys.length >= 3 ? 'strong' : 
                          commonDataKeys.length >= 2 ? 'medium' : 'weak';
          
          connections.push({
            from: summary1.chapterNum,
            to: summary2.chapterNum,
            commonData: commonDataKeys,
            relationshipStrength: strength
          });
        }
      }
    }

    // 找出孤立章节（很少与其他章节有数据关联的）
    const chapterConnectionCounts = new Map<number, number>();
    connections.forEach(conn => {
      chapterConnectionCounts.set(conn.from, (chapterConnectionCounts.get(conn.from) || 0) + 1);
      chapterConnectionCounts.set(conn.to, (chapterConnectionCounts.get(conn.to) || 0) + 1);
    });

    const isolatedChapters = summaries
      .filter(s => (chapterConnectionCounts.get(s.chapterNum) || 0) <= 1)
      .map(s => s.chapterNum);

    return { connections, isolatedChapters };
  }

  /**
   * 生成报告一致性评估
   */
  generateConsistencyReport(): {
    overallScore: number; // 0-100分
    issues: Array<{
      type: 'data_conflict' | 'missing_reference' | 'isolated_chapter' | 'weak_connection';
      severity: 'high' | 'medium' | 'low';
      chapter: number;
      description: string;
      suggestion: string;
    }>;
    strengths: string[];
  } {
    const stats = this.getContextStats();
    const connectionAnalysis = this.getChapterConnectionAnalysis();
    
    let score = 100;
    const issues: Array<{
      type: 'data_conflict' | 'missing_reference' | 'isolated_chapter' | 'weak_connection';
      severity: 'high' | 'medium' | 'low';
      chapter: number;
      description: string;
      suggestion: string;
    }> = [];
    const strengths: string[] = [];

    // 检查孤立章节
    connectionAnalysis.isolatedChapters.forEach(chapterNum => {
      score -= 10;
      issues.push({
        type: 'isolated_chapter',
        severity: 'medium',
        chapter: chapterNum,
        description: `第${chapterNum}章与其他章节缺乏数据关联`,
        suggestion: '增加与前面章节数据的引用和对比分析'
      });
    });

    // 检查弱连接
    const weakConnections = connectionAnalysis.connections.filter(c => c.relationshipStrength === 'weak');
    if (weakConnections.length > connectionAnalysis.connections.length * 0.5) {
      score -= 15;
      issues.push({
        type: 'weak_connection',
        severity: 'medium',
        chapter: 0,
        description: '章节间关联较弱，缺乏数据一致性',
        suggestion: '加强章节间数据引用，确保逻辑连贯性'
      });
    }

    // 评估优势
    if (stats.crossDataKeys >= 10) {
      strengths.push('跨章节数据丰富，有助于保持一致性');
    }
    
    if (connectionAnalysis.connections.length >= stats.totalChapters - 1) {
      strengths.push('章节间连接充分，逻辑脉络清晰');
    }

    if (stats.totalFindings >= stats.totalChapters * 3) {
      strengths.push('关键发现提取充分，信息密度高');
    }

    return {
      overallScore: Math.max(0, score),
      issues,
      strengths
    };
  }

  /**
   * 导出上下文数据
   */
  exportContextData(): string {
    const stats = this.getContextStats();
    const connectionAnalysis = this.getChapterConnectionAnalysis();
    const consistencyReport = this.generateConsistencyReport();

    return JSON.stringify({
      metadata: {
        exportTime: new Date().toISOString(),
        version: '1.0',
        config: this.config
      },
      stats,
      connectionAnalysis,
      consistencyReport,
      chapterSummaries: Array.from(this.contextCache['chapterSummaries'].entries()),
      crossChapterData: this.contextCache.getCrossChapterData()
    }, null, 2);
  }

  /**
   * 导入上下文数据
   */
  importContextData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      
      // 验证数据格式
      if (!data.chapterSummaries || !data.crossChapterData) {
        throw new Error('Invalid context data format');
      }

      // 清空当前数据
      this.contextCache.clear();

      // 导入章节摘要
      data.chapterSummaries.forEach(([, summary]: [number, ChapterSummary]) => {
        summary.generatedAt = new Date(summary.generatedAt);
        this.contextCache.saveChapterSummary(summary);
      });

      this.debugLog('Context data imported successfully');
      return true;

    } catch (error) {
      console.error('Error importing context data:', error);
      return false;
    }
  }

  /**
   * 清理上下文缓存
   */
  clearContext(): void {
    this.contextCache.clear();
    this.debugLog('Context cache cleared');
  }

  /**
   * 调试日志
   */
  private debugLog(message: string, ...args: any[]): void {
    // Debug logging removed
  }

  /**
   * 找出两个数据对象的共同键
   */
  private findCommonDataKeys(data1: Record<string, any>, data2: Record<string, any>): string[] {
    const keys1 = Object.keys(data1);
    const keys2 = Object.keys(data2);
    
    return keys1.filter(key => keys2.includes(key));
  }
} 