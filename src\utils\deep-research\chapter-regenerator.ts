"use client";

import { ChapterInfo } from "@/components/MagicDown";
import { ResearchOutline, OutlineChapter, OutlineSection } from "@/types/outline";
import { useKnowledgeStore } from "@/store/knowledge";

export interface ChapterRegenerationContext {
  chapterInfo: ChapterInfo;
  editDescription: string;
  editResources: ChapterEditResource[];
  originalReport: string;
  researchOutline: ResearchOutline | null;
  originalContext: {
    plan: string;
    sources: Source[];
    localResources: Resource[];
    images: ImageSource[];
    requirement: string;
    extractedChapterContent?: string;
  };
  researchTopic: string;
  writingConfig?: any; // 🔥 添加写作配置字段
}

export interface ChapterContent {
  title: string;
  content: string;
  startIndex: number;
  endIndex: number;
  level: number;
  type: 'main' | 'sub';
}

import { ChapterEditResource, useTaskStore } from "@/store/task";

// 类型定义
interface Source {
  title: string;
  content?: string;
  description?: string;
}

interface Resource {
  id: string;
  name: string;
  type: string;
  size: number;
  status: string;
}

interface ImageSource {
  url: string;
  title: string;
}

export class ChapterRegenerator {
  
  /**
   * 分析章节用于重新生成
   * @param fullReport 完整报告内容
   * @param chapterTitle 章节标题
   * @param editRequirements 编辑要求
   * @param dedicatedResources 专用资源
   * @param researchOutline 研究大纲（可选）
   */
  static analyzeChapterForRegeneration(
    fullReport: string,
    chapterTitle: string,
    editRequirements: string,
    dedicatedResources: Array<{
      id: string;
      title: string;
      type: 'text' | 'file';
      content: string;
    }> = [],
    researchOutline: any = null,
    domExtractedContent?: string, // 🔥 新增：DOM提取的章节内容
    writingConfig?: any // 🔥 新增：写作配置
  ) {
    console.log('=== Chapter Regeneration Analysis ===');
    console.log('Chapter title:', chapterTitle);
    console.log('Edit requirements:', editRequirements);
    console.log('Dedicated resources count:', dedicatedResources.length);
    console.log('Research outline available:', !!researchOutline);
    console.log('Research outline type:', typeof researchOutline);
    console.log('Research outline chapters:', researchOutline?.chapters?.length || 0);
    console.log('📋 Writing config available:', !!writingConfig);
    if (writingConfig) {
      console.log('🎨 Chapter edit writing style:', writingConfig.style);
      console.log('🌍 Chapter edit language requirements:', writingConfig.languageRequirements);
      console.log('⚙️ Chapter edit custom instructions:', writingConfig.customInstructions || '无');
    }

    try {
      // 🔥 强化的输入验证
      if (!fullReport || typeof fullReport !== 'string') {
        throw new Error('无效的报告内容');
      }
      
      if (!chapterTitle || typeof chapterTitle !== 'string') {
        throw new Error('无效的章节标题');
      }
      
      if (!editRequirements || typeof editRequirements !== 'string') {
        throw new Error('无效的编辑要求');
      }

      // 🔥 获取任务上下文（如果可用）
      let taskContext = null;
      try {
        if (typeof window !== 'undefined') {
          // 在浏览器环境中尝试获取taskStore
          const taskStore = (globalThis as any).__taskStore;
          if (taskStore) {
            taskContext = taskStore.getState();
            console.log('Task context retrieved:', {
              hasQuestion: !!taskContext.question,
              hasSources: taskContext.sources?.length || 0,
              hasResources: taskContext.resources?.length || 0,
              hasOutline: !!taskContext.researchOutline,
              hasReportPlan: !!taskContext.reportPlan,
            });
          }
        }
      } catch (error) {
        console.log('Task context not available:', error);
      }

      // 🔥 增强的研究主题获取逻辑 - 优先保护用户原始输入
      let researchTopic = '';
      try {
        const taskStoreState = useTaskStore.getState();
        // 🔥 优先使用用户的原始输入，避免被意外覆盖
        researchTopic = taskStoreState.question || 
                       taskStoreState.requirement || 
                       taskStoreState.title ||
                       taskContext?.question ||
                       taskContext?.requirement ||
                       taskContext?.title ||
                       '';
        console.log('🔍 研究主题获取结果:', researchTopic);
        console.log('📊 来源分析:', {
          fromQuestion: !!taskStoreState.question,
          fromRequirement: !!taskStoreState.requirement,
          fromTitle: !!taskStoreState.title,
          fromContext: !!(taskContext?.question || taskContext?.requirement || taskContext?.title)
        });
      } catch (error) {
        console.warn('❌ 获取研究主题失败:', error);
        researchTopic = '';
      }

      // 🔥 仅在确实没有研究主题时才使用备用方案（避免覆盖用户输入）
      if (!researchTopic || researchTopic.trim() === '') {
        console.warn('⚠️ 研究主题为空，尝试从其他来源恢复（不会覆盖用户原始输入）...');
        
        // 尝试从大纲中获取
        if (researchOutline?.title) {
          researchTopic = researchOutline.title;
          console.log('📋 从大纲标题恢复研究主题:', researchTopic);
        }
        // 尝试从报告计划中获取
        else if (taskContext?.reportPlan) {
          const planLines = taskContext.reportPlan.split('\n');
          const titleLine = planLines.find((line: string) => line.includes('研究') || line.includes('分析') || line.includes('公司'));
          if (titleLine) {
            researchTopic = titleLine.replace(/^[#\-*\s]*/, '').trim();
            console.log('📝 从报告计划恢复研究主题:', researchTopic);
          }
        }
        // 尝试从章节标题中推断
        else if (chapterTitle) {
          const match = chapterTitle.match(/第\d+章[：:、\s]*(.+?)[、：:]?$/);
          if (match && match[1]) {
            researchTopic = match[1] + '投资价值研究';
            console.log('🔍 从章节标题推断研究主题:', researchTopic);
          }
        }
        
        // 最后的备用方案
        if (!researchTopic || researchTopic.trim() === '') {
          researchTopic = '深度投资价值分析研究';
          console.log('🔄 使用最终备用研究主题:', researchTopic);
        }
      } else {
        console.log('✅ 使用现有的研究主题:', researchTopic);
      }

      // 🔥 仅在确实需要时保存恢复的研究主题，避免覆盖用户输入
      try {
        if (researchTopic && researchTopic.trim() !== '') {
          const currentState = useTaskStore.getState();
          // 🔥 关键修复：只有在当前存储完全为空时才保存，避免覆盖用户的原始输入
          const hasUserInput = currentState.question?.trim() || 
                              currentState.requirement?.trim() || 
                              currentState.title?.trim();
          
          if (!hasUserInput) {
            useTaskStore.getState().setQuestion(researchTopic);
            console.log('💾 保存恢复的研究主题到存储（不覆盖用户输入）:', researchTopic);
          } else {
            console.log('🔒 检测到用户输入，不覆盖现有研究主题:', {
              question: currentState.question,
              requirement: currentState.requirement,
              title: currentState.title
            });
          }
        }
      } catch (error) {
        console.warn('❌ 保存研究主题到存储失败:', error);
      }

      // 🔥 验证和处理专用资源
      const validatedResources = dedicatedResources.filter(resource => {
        if (!resource.content || typeof resource.content !== 'string') {
          console.warn(`资源 ${resource.id} (${resource.title}) 内容无效，已被过滤`);
          return false;
        }
        return true;
      }).map(resource => {
        // 确保资源有正确的标题和类型
        return {
          ...resource,
          title: resource.title || `未命名资源 (${resource.id})`,
          type: resource.type || 'text'
        };
      });
      
      console.log(`验证后的专用资源数量: ${validatedResources.length}`);

      // 🔥 大纲数据验证和处理
      let useOutline = false;
      if (researchOutline && typeof researchOutline === 'object' && 
          researchOutline.chapters && Array.isArray(researchOutline.chapters) && 
          researchOutline.chapters.length > 0) {
        useOutline = true;
        console.log('Using outline-based analysis');
      } else {
        console.log('Outline data invalid or missing, using text-based analysis');
        console.log('Outline validation details:', {
          exists: !!researchOutline,
          isObject: typeof researchOutline === 'object',
          hasChapters: !!(researchOutline && researchOutline.chapters),
          isArray: !!(researchOutline && Array.isArray(researchOutline.chapters)),
          length: researchOutline?.chapters?.length || 0
        });
      }

      // 使用大纲数据进行精确的章节识别
      const regenerationScope = useOutline 
        ? this.identifyRegenerationScopeWithOutline(chapterTitle, researchOutline)
        : { type: 'single_sub' as const, title: chapterTitle, chapters: [{ number: 1, title: chapterTitle, type: 'sub' as const }] };

      console.log('Regeneration scope:', regenerationScope);

      // 🔥 提取章节内容 - 使用增强的提取方法
      let chapterContent = this.extractChapterContentWithOutline(
        fullReport, 
        regenerationScope, 
        useOutline ? researchOutline : null
      );

      console.log('Extracted chapter content length:', chapterContent.length);
      
      // 如果提取的内容太短，尝试使用其他方法
      if (chapterContent.length < 200) {
        console.warn('⚠️ 提取的内容太短，可能不完整，尝试使用备用方法');
        
        // 尝试使用简单文本匹配
        const simpleContent = this.extractSimpleChapterContent(fullReport, chapterTitle);
        
        if (simpleContent.length > chapterContent.length) {
          console.log(`使用简单提取方法获取了更长的内容: ${simpleContent.length} 字符`);
          // 使用更长的内容
          chapterContent = simpleContent;
        }
      }

      // 构建章节信息对象
      const chapterInfo: ChapterInfo = {
        id: `chapter-${Date.now()}`,
        title: chapterTitle,
        level: regenerationScope.type === 'main_with_subs' ? 1 : 2,
        type: regenerationScope.type === 'main_with_subs' ? 'main' as const : 'sub' as const,
        editable: true,
        order: regenerationScope.chapters[0]?.number || 1,
        chapterData: undefined // 设置为可选
      };
      
      // 🔥 提取引用和参考资料
      const references = this.extractReferences(chapterContent);
      
      // 🔥 构建原始上下文信息，用于新的模板式提示词
      const originalContext = {
        plan: taskContext?.reportPlan || "深入分析项目公司的相关业务领域，提供专业、客观的研究分析。",
        sources: taskContext?.sources || [],
        localResources: taskContext?.resources || [],
        images: taskContext?.images || [],
        requirement: researchTopic, // 🔥 使用增强获取的研究主题
        extractedChapterContent: domExtractedContent || chapterContent // 🔥 优先使用DOM提取的内容
      };
      
      console.log(`提取到${references.length}个引用`);

      // 🔥 转换专用资源格式以符合ChapterEditResource类型
      const editResources: ChapterEditResource[] = validatedResources.map(resource => ({
        id: resource.id,
        name: resource.title || `资源-${resource.id}`,
        content: resource.content,
        type: resource.type === 'file' ? 'file' : 'text',
        size: resource.content.length,
        createdAt: Date.now()
      }));

      // 构建新的模板式重新生成上下文
      const regenerationContext: ChapterRegenerationContext = {
        chapterInfo,
        editDescription: editRequirements,
        editResources,
        originalReport: fullReport,
        researchOutline,
        originalContext,
        researchTopic, // 🔥 添加研究主题字段
        writingConfig // 🔥 添加写作配置字段
      };

      // 构建完整提示词（增强版本，包含写作配置）
      const fullPrompt = this.buildTemplateBasedRegenerationPrompt(regenerationContext);

      return {
        chapterInfo,
        fullPrompt,
        regenerationScope,
        extractedContent: chapterContent,
        references,
        taskContext,
        dedicatedResources: editResources,
        originalContext,
        regenerationContext,
        researchTopic // 🔥 返回增强获取的研究主题
      };

    } catch (error) {
      console.error('Chapter regeneration analysis failed:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
        chapterTitle,
        hasOutline: !!researchOutline,
        outlineType: typeof researchOutline
      });
      throw new Error(`章节分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从大纲中提取研究要点
   */
  private static extractResearchPointsFromOutline(chapterTitle: string, outline: any): string[] {
    if (!outline || !outline.chapters || !Array.isArray(outline.chapters)) {
      return [];
    }
    
    try {
      // 遍历章节查找匹配
      for (const chapter of outline.chapters) {
        // 检查主章节
        if (chapter.title === chapterTitle && Array.isArray(chapter.researchPoints)) {
          return chapter.researchPoints;
        }
        
        // 检查子章节
        if (Array.isArray(chapter.sections)) {
          for (const section of chapter.sections) {
            if (section.title === chapterTitle && Array.isArray(section.researchPoints)) {
              return section.researchPoints;
            }
          }
        }
      }
    } catch (error) {
      console.error('Error extracting research points:', error);
    }
    
    return [];
  }

  /**
   * 检测原始章节标题格式
   */
  private static detectOriginalTitleFormat(chapterInfo: any): string {
    if (!chapterInfo || !chapterInfo.title) {
      return '未知格式';
    }

    const title = chapterInfo.title;
    console.log('检测标题格式:', title);
    
    // 检测Markdown格式
    if (title.startsWith('# ')) return `# ${title.substring(2)}`; // H1
    if (title.startsWith('## ')) return `## ${title.substring(3)}`; // H2
    if (title.startsWith('### ')) return `### ${title.substring(4)}`; // H3
    if (title.startsWith('#### ')) return `#### ${title.substring(5)}`; // H4
    
    // 检测粗体格式
    if (title.startsWith('**') && title.endsWith('**')) {
      return `**${title.substring(2, title.length - 2)}**`;
    }
    
    // 检测三级子章节格式 (如 1.1.1)
    const triLevelSubChapterMatch = title.match(/^(\d+\.\d+\.\d+)[、：\s]*(.*)/);
    if (triLevelSubChapterMatch) {
      const sectionNum = triLevelSubChapterMatch[1];
      const sectionTitle = triLevelSubChapterMatch[2] || '';
      // 保持原始格式，包括标点符号
      const delimiter = title.charAt(sectionNum.length) || '、';
      return `${sectionNum}${delimiter}${sectionTitle}`;
    }
    
    // 检测子章节格式 (如 1.1)
    const subChapterMatch = title.match(/^(\d+\.\d+)[、：\s]*(.*)/);
    if (subChapterMatch) {
      const sectionNum = subChapterMatch[1];
      const sectionTitle = subChapterMatch[2] || '';
      // 保持原始格式，包括标点符号
      const delimiter = title.charAt(sectionNum.length) || '、';
      return `${sectionNum}${delimiter}${sectionTitle}`;
    }
    
    // 检测主章节格式
    const mainChapterMatch = title.match(/^第(\d+|[一二三四五六七八九十]+)章[：\s]*(.*)/);
    if (mainChapterMatch) {
      const chapterNum = mainChapterMatch[1];
      const chapterTitle = mainChapterMatch[2] || '';
      // 保持原始格式，包括标点符号
      const delimiter = title.includes('：') ? '：' : title.includes(':') ? ':' : ' ';
      return `第${chapterNum}章${chapterTitle ? delimiter + chapterTitle : ''}`;
    }
    
    // 检测简单编号格式
    const simpleNumberMatch = title.match(/^(\d+)[、：\s]*(.*)/);
    if (simpleNumberMatch) {
      const num = simpleNumberMatch[1];
      const titleText = simpleNumberMatch[2] || '';
      // 保持原始格式，包括标点符号
      const delimiter = title.charAt(num.length) || '、';
      return `${num}${delimiter}${titleText}`;
    }
    
    // 检测无标点的编号格式 (如 "1.1 标题" 或 "1.1.1 标题")
    const noDelimiterMatch = title.match(/^(\d+\.\d+(?:\.\d+)?)\s+(.*)/);
    if (noDelimiterMatch) {
      const num = noDelimiterMatch[1];
      const titleText = noDelimiterMatch[2] || '';
      // 保持原始格式，不添加顿号
      return `${num} ${titleText}`;
    }
    
    // 默认返回原始标题
    return title;
  }
  
  /**
   * 提取章节编号
   */
  private static extractChapterNumber(title: string): string | null {
    if (!title || title.trim().length === 0) {
      return null;
    }
    
    const cleanTitle = title.trim();
    
    // 提取多种格式的章节编号，优先匹配更具体的格式
    const patterns = [
      // 三级子章节格式（最具体的）
      /^(\d+\.\d+\.\d+)[、：\s]/,  // 1.1.1、
      /^(\d+\.\d+\.\d+)$/,        // 1.1.1 (仅编号)
      /^(\d+\.\d+\.\d+)\s/,       // 1.1.1 (空格)
      
      // 二级子章节格式
      /^(\d+\.\d+)[、：\s]/,       // 1.1、
      /^(\d+\.\d+)$/,             // 1.1 (仅编号)
      /^(\d+\.\d+)\s/,            // 1.1 (空格)
      
      // 一级章节格式
      /^(\d+)[、：\s]/,            // 1、
      /^(\d+)$/,                  // 1 (仅编号)
      /^(\d+)\s/,                 // 1 (空格)
      
      // 中文章节格式
      /^第(\d+)章/,               // 第1章
      /^第(\d+)节/,               // 第1节
      
      // Markdown标题中的编号
      /^#+\s+(\d+\.\d+\.\d+)/,    // ## 1.1.1
      /^#+\s+(\d+\.\d+)/,         // ## 1.1
      /^#+\s+(\d+)/,              // ## 1
      
      // 包含标题的格式
      /(\d+\.\d+\.\d+)、/,        // xxx1.1.1、xxx
      /(\d+\.\d+)、/,             // xxx1.1、xxx
      /(\d+)、/                   // xxx1、xxx
    ];
    
    for (const pattern of patterns) {
      const match = cleanTitle.match(pattern);
      if (match && match[1]) {
        // 验证提取的编号是否合理
        const extracted = match[1];
        // 只允许数字和点号
        if (/^[\d.]+$/.test(extracted)) {
          return extracted;
        }
      }
    }
    
    return null;
  }

  /**
   * 构建简化的重新生成提示词
   */
  static buildSimpleRegenerationPrompt(
    chapterInfo: any,
    editRequirements: string,
    formatRequirements: string,
    dedicatedResources: any[],
    taskContext?: any
  ): string {
    // 🔥 构建研究背景
    const researchBackground = taskContext ? `\n\n## 研究背景信息\n- 研究主题：${taskContext.title || '未设置'}\n- 研究要求：${taskContext.requirement || '未设置'}\n- 网络来源数量：${taskContext.sources?.length || 0}\n- 本地资源数量：${taskContext.resources?.length || 0}` : '';

    // 🔥 构建大纲研究要点
    const researchPoints = chapterInfo.researchPoints || [];
    const researchPointsText = researchPoints.length > 0 
      ? `\n\n## 本章节研究要点\n${researchPoints.map((point: string) => `- ${point}`).join('\n')}`
      : '';

    // 🔥 构建专用资源内容
    const dedicatedResourcesText = dedicatedResources.length > 0
      ? `\n\n## 专用参考资料\n${dedicatedResources.map((resource: any, index: number) => {
          // 根据资源类型和大小决定截取的内容长度
          const maxContentLength = resource.type === 'text' ? 3000 : 2000;
          const contentPreview = resource.content.substring(0, maxContentLength);
          const isTruncated = resource.content.length > maxContentLength;
          
          // 添加资源类型和大小信息
          const resourceMeta = `类型: ${resource.type === 'text' ? '文本' : '文件'}, 大小: ${(resource.size / 1024).toFixed(2)}KB`;
          
          return `### 资料${index + 1}：${resource.title} (${resourceMeta})
${contentPreview}${isTruncated ? '...(内容已截断，但AI分析时将考虑完整内容)' : ''}`;
        }).join('\n\n')}`
      : '';

    // 🔥 构建网络来源
    const sourcesText = taskContext && taskContext.sources && taskContext.sources.length > 0
      ? `\n\n## 网络来源\n${taskContext.sources.map((source: any, idx: number) => 
          `<source index="${idx + 1}" url="${source.url || '未知URL'}">\n${source.title || '未知标题'}\n${source.description || ''}\n</source>`
        ).join('\n')}`
      : '';

    // 🔥 构建本地资源
    const localResourcesText = taskContext && taskContext.resources && taskContext.resources.length > 0
      ? `\n\n## 本地资源\n${taskContext.resources.map((resource: any, idx: number) => 
          `<local_resource index="L-${idx + 1}" name="${resource.name || '未命名'}" type="${resource.type || '未知类型'}">\n${resource.name || '未命名资源'}\n</local_resource>`
        ).join('\n')}`
      : '';

    // 🔥 检测原始章节标题格式
    const originalTitleFormat = this.detectOriginalTitleFormat(chapterInfo);
    console.log('检测到的原始标题格式:', originalTitleFormat);
    
    // 🔥 提取章节编号
    const chapterNumber = this.extractChapterNumber(chapterInfo.title);
    console.log('提取的章节编号:', chapterNumber);

    // 🔥 分析原文的章节结构
    const originalStructure = this.analyzeChapterStructure(chapterInfo.content, chapterNumber);
    console.log('原文章节结构:', originalStructure);

    // 🔥 生成结构要求
    const structureRequirements = this.generateStructureRequirements(chapterNumber, originalStructure);

    // 🔥 生成格式要求
    const formatInstructions = `
## 格式要求
1. 必须保持原始章节标题的完整格式："${originalTitleFormat}"
2. 必须保持原始章节编号："${chapterNumber || '无编号'}"
3. 必须使用标准Markdown格式
4. 必须包含原始章节标题的完整内容
5. 必须保持与原文一致的格式风格（包括标题级别、列表样式、段落结构等）
6. 必须确保输出是完整的Markdown文本，可以直接替换原文
7. 不要添加额外的格式标记，如"\`\`\`markdown"或"\`\`\`"
8. 不要添加"以下是重写的内容"等额外说明
9. 不要改变章节编号格式，如原文是"1.1"，不要改为"1、"或"### 1"
10. 子章节编号必须严格保持原格式：
    - 如果原标题是"1.1.1、xxx"，新内容也必须使用"1.1.1、"开头
    - 如果原标题是"1.1 xxx"（无标点），新内容也必须使用"1.1 "开头
    - 如果原标题是"1.1：xxx"，新内容也必须使用"1.1："开头

### 原始标题示例
${originalTitleFormat}

### 章节编号格式
${chapterNumber ? `原始章节编号是"${chapterNumber}"，必须保持这个编号格式不变` : '本章节无明确编号'}

${structureRequirements}

### 格式验证清单
- [ ] 包含完整的原始标题
- [ ] 使用与原文相同的章节编号
- [ ] 使用正确的Markdown标题格式
- [ ] 保持与原文一致的段落结构
- [ ] 保持与原文一致的列表格式
- [ ] 没有添加额外的格式标记
- [ ] 内容可以直接替换原文
- [ ] 包含所有必要的子章节
- [ ] 内容详细度与原文相当
`;

    // 🔥 构建完整提示词
    return `
# 章节重新撰写任务

您是一位专业的研究报告撰写专家。请根据以下要求重新撰写一个研究报告章节。您需要确保输出的内容格式与原文一致，可以直接替换原文。

## 章节信息
- 章节标题：${chapterInfo.title}
- 章节类型：${chapterInfo.type === 'main' ? '主章节' : '子章节'}
- 章节级别：${chapterInfo.level || '未知'}
- 章节编号：${chapterNumber || '无编号'}
- 原文长度：${chapterInfo.content.length} 字符

## 原始章节内容
${chapterInfo.content}

## 编辑要求
${editRequirements}

${formatInstructions}
${researchBackground}
${researchPointsText}
${dedicatedResourcesText}
${sourcesText}
${localResourcesText}
${formatRequirements}

## 引用格式
如果需要引用网络来源，请使用以下格式：
[数字]

例如：根据最新研究[1]，该技术已经...

## 重要提示
1. 您的输出将直接用于替换原文，请确保格式完全兼容
2. 请保持学术或商务报告的专业风格
3. 必须包含原始章节标题，格式与原文一致
4. 必须保持原始章节编号格式不变
5. 输出必须是完整的Markdown文本，不要添加额外的格式标记
6. 不要使用"以下是重写的内容"等额外说明，直接输出可替换的内容
7. 请保留原文中的重要信息和数据，同时根据编辑要求进行改进
8. 生成的内容长度应与原文相当或更详细（至少${Math.floor(chapterInfo.content.length * 0.8)}字符）
9. 必须以完整的章节标题开头，然后是章节内容

请直接输出重写后的章节内容，无需其他说明。
`;
  }

  /**
   * 分析章节的内部结构
   */
  private static analyzeChapterStructure(content: string, mainChapterNumber: string | null): {
    hasSubChapters: boolean;
    subChapterPattern: string;
    subChapters: string[];
    totalSections: number;
  } {
    if (!content || !mainChapterNumber) {
      return {
        hasSubChapters: false,
        subChapterPattern: '',
        subChapters: [],
        totalSections: 0
      };
    }

    // 提取子章节模式
    const subChapterPatterns = [
      new RegExp(`(${mainChapterNumber}\\.\\d+)、`, 'g'), // 1.1、1.2、
      new RegExp(`(${mainChapterNumber}\\.\\d+)：`, 'g'), // 1.1：1.2：
      new RegExp(`(${mainChapterNumber}\\.\\d+)\\s`, 'g'), // 1.1 1.2 
      new RegExp(`###\\s+(${mainChapterNumber}\\.\\d+)`, 'g'), // ### 1.1
    ];

    let detectedSubChapters: string[] = [];
    let usedPattern = '';

    for (const pattern of subChapterPatterns) {
      const matches = [...content.matchAll(pattern)];
      if (matches.length > 0) {
        detectedSubChapters = matches.map(match => match[1]);
        usedPattern = pattern.source;
        break;
      }
    }

    // 去重并排序
    const uniqueSubChapters = [...new Set(detectedSubChapters)].sort();

    return {
      hasSubChapters: uniqueSubChapters.length > 0,
      subChapterPattern: usedPattern,
      subChapters: uniqueSubChapters,
      totalSections: uniqueSubChapters.length
    };
  }

  /**
   * 生成结构要求
   */
  private static generateStructureRequirements(chapterNumber: string | null, structure: any): string {
    if (!structure.hasSubChapters || !chapterNumber) {
      return `
### 内容结构要求
- 必须以完整的章节标题开头："${chapterNumber || ''}、[章节名称]"
- 保持原文的段落结构和层次
- 确保内容详细完整，不要过度简化`;
    }

    return `
### 内容结构要求
- 必须以完整的章节标题开头："${chapterNumber}、[章节名称]"
- 必须包含以下子章节（格式与原文一致）：
${structure.subChapters.map((sub: string) => `  - ${sub}、[对应的子章节内容]`).join('\n')}
- 每个子章节都应该有详细的内容，不要只是标题
- 子章节编号格式必须与原文完全一致
- 总共应包含 ${structure.totalSections} 个子章节
- 保持原文的详细度和深度`;
  }

  /**
   * 使用大纲数据进行精确的章节范围识别
   */
  static identifyRegenerationScopeWithOutline(chapterTitle: string, outline: any) {
    console.log('=== Outline-Based Scope Identification ===');
    console.log('Chapter title:', chapterTitle);
    console.log('Outline chapters:', outline.chapters?.length || 0);

    // 🔥 大纲数据验证
    if (!outline || !outline.chapters || outline.chapters.length === 0) {
      console.log('Invalid outline data, fallback to text-based identification');
      return {
        type: 'single_sub' as const,
        title: chapterTitle,
        chapters: [{ number: 1, title: chapterTitle, type: 'sub' as const }]
      };
    }

    // 🔥 输入验证
    if (!chapterTitle || typeof chapterTitle !== 'string') {
      console.error('Invalid chapter title provided');
      throw new Error('章节标题不能为空');
    }

    console.log('Available chapters in outline:');
    outline.chapters.forEach((ch: any, idx: number) => {
      console.log(`  Chapter ${idx}: ${ch.number} - ${ch.title}`);
      if (ch.sections) {
        ch.sections.forEach((sec: any, secIdx: number) => {
          console.log(`    Section ${secIdx}: ${sec.number} - ${sec.title}`);
        });
      }
    });

    // 🔥 改进的章节匹配逻辑
    for (let chapterIndex = 0; chapterIndex < outline.chapters.length; chapterIndex++) {
      const chapter = outline.chapters[chapterIndex];
      
      // 🔥 章节数据验证
      if (!chapter || typeof chapter.number === 'undefined') {
        console.warn(`Invalid chapter data at index ${chapterIndex}:`, chapter);
        continue;
      }
      
      // 🔥 精确的主章节匹配
      const mainChapterPatterns = [
        `第${chapter.number}章`,           // 第1章
        `## 第${chapter.number}章`,        // ## 第1章
        `**第${chapter.number}章**`,       // **第1章**
        `第${chapter.number}章：`,         // 第1章：
        `第${chapter.number}章 `,          // 第1章 (带空格)
      ];
      
      const isMainChapterMatch = mainChapterPatterns.some(pattern => 
        chapterTitle.includes(pattern) || chapterTitle === pattern.trim()
      );
      
      if (isMainChapterMatch) {
        console.log('✅ Found main chapter match:', chapter);
        return {
          type: 'main_with_subs' as const,
          title: chapterTitle,
          chapterIndex,
          chapters: [{
            number: chapter.number,
            title: chapter.title || `第${chapter.number}章`,
            type: 'main' as const,
            sections: Array.isArray(chapter.sections) ? chapter.sections : [],
            researchPoints: Array.isArray(chapter.researchPoints) ? chapter.researchPoints : []
          }]
        };
      }
      
      // 🔥 精确的子章节匹配
      if (Array.isArray(chapter.sections)) {
        for (let sectionIndex = 0; sectionIndex < chapter.sections.length; sectionIndex++) {
          const section = chapter.sections[sectionIndex];
          
          // 🔥 子章节数据验证
          if (!section || !section.number) {
            console.warn(`Invalid section data at ${chapterIndex}.${sectionIndex}:`, section);
            continue;
          }
          
          // 🔥 精确的子章节匹配
          const subChapterPatterns = [
            `${section.number}、`,          // 1.1、
            `## ${section.number}、`,       // ## 1.1、
            `**${section.number}、**`,      // **1.1、**
            `${section.number} `,           // 1.1 (带空格)
            section.number,                  // 1.1 (精确匹配)
          ];
          
          const isSubChapterMatch = subChapterPatterns.some(pattern => 
            chapterTitle.includes(pattern)
          );
          
          if (isSubChapterMatch) {
            console.log('✅ Found section match:', section);
            return {
              type: 'single_sub' as const,
              title: chapterTitle,
              chapterIndex,
              sectionIndex,
              chapters: [{
                number: chapter.number,
                title: chapter.title || `第${chapter.number}章`,
                type: 'sub' as const,
                sectionNumber: section.number,
                sectionTitle: section.title || `${section.number} 子章节`,
                researchPoints: Array.isArray(section.researchPoints) ? section.researchPoints : []
              }]
            };
          }
        }
      }
    }

    console.log('❌ No outline match found, using fallback');
    return {
      type: 'single_sub' as const,
      title: chapterTitle,
      chapters: [{ number: 1, title: chapterTitle, type: 'sub' as const, researchPoints: [] }]
    };
  }

  /**
   * 使用大纲信息提取章节内容
   */
  static extractChapterContentWithOutline(
    fullReport: string,
    regenerationScope: any,
    outline: any
  ): string {
    console.log('=== Chapter Content Extraction with Outline ===');
    console.log('Regeneration scope:', regenerationScope);
    console.log('Has outline:', !!outline);
    console.log('Outline valid:', !!(outline && outline.chapters && Array.isArray(outline.chapters)));

    // 🔥 输入验证
    if (!fullReport || typeof fullReport !== 'string') {
      console.error('Invalid fullReport provided');
      return '无效的报告内容';
    }

    if (!regenerationScope || typeof regenerationScope !== 'object') {
      console.error('Invalid regenerationScope provided');
      return '无效的章节范围';
    }

    // 如果有大纲且成功识别了章节范围，使用精确提取
    if (outline && outline.chapters && Array.isArray(outline.chapters) && 
        outline.chapters.length > 0 && typeof regenerationScope.chapterIndex !== 'undefined') {
      console.log('Using outline-based extraction with chapter index:', regenerationScope.chapterIndex);
      
      // 尝试使用精确的大纲位置提取
      const outlineBasedContent = this.extractContentByOutlinePosition(fullReport, regenerationScope, outline);
      
      // 验证提取内容的完整性
      if (outlineBasedContent && outlineBasedContent.length > 200) {
        console.log(`✅ 成功使用大纲提取内容，长度: ${outlineBasedContent.length} 字符`);
        return outlineBasedContent;
      } else {
        console.log(`⚠️ 大纲提取内容不完整，长度仅: ${outlineBasedContent.length} 字符，尝试备用方法`);
      }
    }

    // 尝试使用标题匹配提取
    const titleBasedContent = this.extractContentByTitle(fullReport, regenerationScope.title);
    if (titleBasedContent && titleBasedContent.length > 200) {
      console.log(`✅ 成功使用标题匹配提取内容，长度: ${titleBasedContent.length} 字符`);
      return titleBasedContent;
    }

    // 最后备用：使用简单的文本提取
    console.log('Using simple text extraction as fallback');
    const simpleContent = this.extractSimpleChapterContent(fullReport, regenerationScope.title || '未知章节');
    console.log(`⚠️ 使用简单提取方法，提取内容长度: ${simpleContent.length} 字符`);
    
    return simpleContent;
  }

  /**
   * 使用标题匹配提取章节内容
   */
  static extractContentByTitle(fullReport: string, title: string): string {
    if (!title || !fullReport) return '';
    
    console.log(`🔍 尝试使用标题匹配提取: "${title}"`);
    
    // 清理标题，移除特殊字符
    const cleanTitle = title.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // 尝试多种标题格式匹配
    const titlePatterns = [
      new RegExp(`(${cleanTitle}[^\\n]*\\n[\\s\\S]*?)(?=\\d+\\.\\d+[、：]|\\d+[、：]|第[一二三四五六七八九十\\d]+章|$)`, 'i'),
      new RegExp(`(#+\\s+${cleanTitle}[^\\n]*\\n[\\s\\S]*?)(?=#+\\s+|$)`, 'i'),
      new RegExp(`(\\*\\*${cleanTitle}[^\\n*]*\\*\\*[^\\n]*\\n[\\s\\S]*?)(?=\\*\\*|$)`, 'i')
    ];
    
    for (const pattern of titlePatterns) {
      const match = pattern.exec(fullReport);
      if (match) {
        const content = match[1];
        console.log(`✅ 匹配成功，提取内容长度: ${content.length} 字符`);
        return content;
      }
    }
    
    console.log('❌ 标题匹配提取失败');
    return '';
  }

  /**
   * 基于大纲位置进行精确的内容提取
   */
  static extractContentByOutlinePosition(
    fullReport: string,
    scope: any,
    outline: any
  ): string {
    console.log('=== Outline Position-Based Extraction ===');
    console.log('Scope:', scope);
    console.log('Outline exists:', !!outline);
    
    // 🔥 增强的安全检查
    if (!outline || !outline.chapters || !Array.isArray(outline.chapters)) {
      console.error('Invalid outline data in extractContentByOutlinePosition:', outline);
      console.log('Fallback to simple extraction');
      return this.extractSimpleChapterContent(fullReport, scope.title || '未知章节');
    }

    if (typeof scope.chapterIndex === 'undefined' || scope.chapterIndex < 0 || scope.chapterIndex >= outline.chapters.length) {
      console.error('Invalid chapter index:', scope.chapterIndex, 'Available chapters:', outline.chapters.length);
      return this.extractSimpleChapterContent(fullReport, scope.title || '未知章节');
    }
    
    const chapter = outline.chapters[scope.chapterIndex];
    if (!chapter) {
      console.error('Chapter not found at index:', scope.chapterIndex);
      return this.extractSimpleChapterContent(fullReport, scope.title || '未知章节');
    }

    console.log('Working with chapter:', chapter);

    let startPatterns: string[] = [];
    let endPatterns: string[] = [];

    if (scope.type === 'main_with_subs') {
      // 主章节：从第X章开始，到第Y章结束
      if (chapter.number) {
        startPatterns = [
          `第${chapter.number}章`,
          `## 第${chapter.number}章`,
          `**第${chapter.number}章**`,
          `# 第${chapter.number}章`,
          `### 第${chapter.number}章`
        ];
        
        // 查找下一章节
        if (scope.chapterIndex + 1 < outline.chapters.length) {
          const nextChapter = outline.chapters[scope.chapterIndex + 1];
          if (nextChapter && nextChapter.number) {
            endPatterns = [
              `第${nextChapter.number}章`,
              `## 第${nextChapter.number}章`,
              `**第${nextChapter.number}章**`,
              `# 第${nextChapter.number}章`,
              `### 第${nextChapter.number}章`
            ];
          }
        }
      }
    } else if (scope.type === 'single_sub' && typeof scope.sectionIndex !== 'undefined') {
      // 子章节：从X.Y开始，到X.Z或下一章结束
      if (Array.isArray(chapter.sections) && scope.sectionIndex >= 0 && scope.sectionIndex < chapter.sections.length) {
        const section = chapter.sections[scope.sectionIndex];
        if (section && section.number) {
          startPatterns = [
            `${section.number}、`,
            `## ${section.number}、`,
            `**${section.number}、**`,
            `${section.number} `,
            `### ${section.number}、`,
            `# ${section.number}、`
          ];
          
          // 查找下一个子章节或下一章
          if (scope.sectionIndex + 1 < chapter.sections.length) {
            const nextSection = chapter.sections[scope.sectionIndex + 1];
            if (nextSection && nextSection.number) {
              endPatterns = [
                `${nextSection.number}、`,
                `## ${nextSection.number}、`,
                `**${nextSection.number}、**`,
                `${nextSection.number} `,
                `### ${nextSection.number}、`,
                `# ${nextSection.number}、`
              ];
            }
          } else if (scope.chapterIndex + 1 < outline.chapters.length) {
            const nextChapter = outline.chapters[scope.chapterIndex + 1];
            if (nextChapter && nextChapter.number) {
              endPatterns = [
                `第${nextChapter.number}章`,
                `## 第${nextChapter.number}章`,
                `**第${nextChapter.number}章**`,
                `# 第${nextChapter.number}章`,
                `### 第${nextChapter.number}章`
              ];
            }
          }
        }
      }
    }

    // 如果没有找到匹配的模式，使用备用方法
    if (startPatterns.length === 0) {
      console.log('No start patterns found, using title-based extraction');
      return this.extractContentByTitle(fullReport, scope.title);
    }

    // 查找开始位置
    let startIndex = -1;
    let startPattern = '';
    for (const pattern of startPatterns) {
      const index = fullReport.indexOf(pattern);
      if (index !== -1 && (startIndex === -1 || index < startIndex)) {
        startIndex = index;
        startPattern = pattern;
      }
    }

    if (startIndex === -1) {
      console.log('Start pattern not found, using title-based extraction');
      return this.extractContentByTitle(fullReport, scope.title);
    }

    console.log(`Found start pattern "${startPattern}" at index ${startIndex}`);

    // 查找结束位置
    let endIndex = fullReport.length;
    let endPattern = '';
      for (const pattern of endPatterns) {
      const index = fullReport.indexOf(pattern, startIndex + startPattern.length);
      if (index !== -1 && index < endIndex) {
        endIndex = index;
        endPattern = pattern;
      }
    }

    console.log(`Found end pattern "${endPattern}" at index ${endIndex}`);

    // 提取内容
    const content = fullReport.substring(startIndex, endIndex);
    console.log(`Extracted content length: ${content.length} characters`);

    return content;
  }

  /**
   * 简单的章节内容提取（备用方法）
   */
  static extractSimpleChapterContent(fullReport: string, title: string): string {
    console.log('=== Simple Chapter Content Extraction ===');
    console.log('Title:', title);
    console.log('Report length:', fullReport?.length || 0);

    // 🔥 输入验证
    if (!fullReport || typeof fullReport !== 'string') {
      console.error('Invalid fullReport provided to extractSimpleChapterContent');
      return '报告内容无效，无法提取章节';
    }

    if (!title || typeof title !== 'string') {
      console.error('Invalid title provided to extractSimpleChapterContent');
      return '章节标题无效，无法提取内容';
    }

    // 查找标题位置
    const titleStart = fullReport.indexOf(title);
    if (titleStart === -1) {
      console.log('Title not found in report, returning error message');
      return `未找到标题"${title}"，请检查章节标题是否正确`;
    }
    
    console.log('Title found at position:', titleStart);
    
    // 🔥 移除固定长度限制，使用更智能的内容提取逻辑
    // 尝试查找下一个章节标题作为结束位置
    const nextChapterPatterns = [
      /\d+\.\d+\.\d+[、：]/,  // 1.1.1、
      /\d+\.\d+[、：]/,       // 1.1、
      /\d+[、：]/,            // 1、
      /第[一二三四五六七八九十\d]+章/,  // 第X章
      /#+\s+\d+\.\d+/,        // ## 1.1
      /#+\s+\d+/,             // ## 1
      /#+\s+第[一二三四五六七八九十\d]+章/ // ## 第X章
    ];
    
    // 从标题后开始搜索
    const contentAfterTitle = fullReport.substring(titleStart + title.length);
    
    // 查找下一个章节标题
    let endPos = fullReport.length;
    
    for (const pattern of nextChapterPatterns) {
      const match = contentAfterTitle.match(pattern);
      if (match && match.index && match.index > 100) { // 确保至少包含一些内容
        const matchPos = titleStart + title.length + match.index;
        if (matchPos < endPos) {
          endPos = matchPos;
          console.log(`找到下一章节标题: ${match[0]} 在位置 ${matchPos}`);
        }
      }
    }
    
    // 如果没有找到下一章节，尝试使用其他方法确定合理的结束位置
    if (endPos === fullReport.length) {
      console.log('没有找到下一章节标题，尝试使用其他方法确定结束位置');
      
      // 尝试查找大段落分隔符
      const paragraphBreaks = [...contentAfterTitle.matchAll(/\n\s*\n\s*\n/g)];
      if (paragraphBreaks.length > 0 && paragraphBreaks[0].index && paragraphBreaks[0].index > 1000) {
        // 使用第一个大段落分隔符作为结束位置
        endPos = titleStart + title.length + paragraphBreaks[0].index;
        console.log(`使用大段落分隔符作为结束位置: ${endPos}`);
      } else {
        // 如果没有找到大段落分隔符，使用一个合理的最大长度
        const reasonableLength = Math.min(10000, fullReport.length - titleStart);
        endPos = titleStart + reasonableLength;
        console.log(`使用合理的最大长度作为结束位置: ${endPos} (${reasonableLength} 字符)`);
      }
    }
    
    // 提取内容并清理
    let extracted = fullReport.substring(titleStart, endPos);
    
    // 基本清理：移除多余空行、HTML标签等
    extracted = extracted
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/\n{3,}/g, '\n\n') // 将3个以上连续换行符替换为2个
      .trim();
    
    console.log('Extracted content length:', extracted.length);
    console.log('Content preview:', extracted.substring(0, 100) + '...');
    
    return extracted;
  }

  /**
   * 智能检测章节信息
   */
  private static detectChapterInfo(originalReport: string, chapterTitle: string): {
    type: 'main' | 'sub';
    level: number;
    order: number;
    number?: string;
    detectedFormat?: string;
  } {
    
    // 检测主章节模式
    const mainChapterPatterns = [
      /第(\d+)章[：:、\s]*(.+?)$/m,         // 第X章：标题 或 第X章 标题
      /^##?\s*第(\d+)章[：:、\s]*(.+?)$/m,  // ## 第X章：标题
      /^\*\*第(\d+)章[：:、\s]*(.+?)\*\*$/m // **第X章：标题**
    ];
    
    for (const pattern of mainChapterPatterns) {
      const match = chapterTitle.match(pattern);
      if (match) {
        return {
          type: 'main',
          level: 1,
          order: parseInt(match[1]),
          number: match[1],
          detectedFormat: 'main_chapter'
        };
      }
    }
    
    // 检测子章节模式
    const subChapterPatterns = [
      /^(\d+\.\d+)、(.+?)$/m,                // X.X、标题
      /^###?\s*(\d+\.\d+)、(.+?)$/m,         // ### X.X、标题
      /^\*\*(\d+\.\d+)、(.+?)\*\*$/m         // **X.X、标题**
    ];
    
    for (const pattern of subChapterPatterns) {
      const match = chapterTitle.match(pattern);
      if (match) {
        const parts = match[1].split('.');
        return {
          type: 'sub',
          level: 2,
          order: parseInt(parts[1]),
          number: match[1],
          detectedFormat: 'sub_chapter'
        };
      }
    }
    
    // 在原始报告中搜索章节上下文来判断类型
    const titleIndex = originalReport.indexOf(chapterTitle);
    if (titleIndex !== -1) {
      // 检查标题前后的上下文
      const beforeContext = originalReport.substring(Math.max(0, titleIndex - 200), titleIndex);
      const afterContext = originalReport.substring(titleIndex, Math.min(originalReport.length, titleIndex + 200));
      
      // 如果标题前有主章节格式，当前标题可能是子章节
      if (beforeContext.includes('第') && beforeContext.includes('章')) {
        return {
          type: 'sub',
          level: 2,
          order: 1,
          detectedFormat: 'context_sub'
        };
      }
      
      // 如果标题后有子章节格式，当前标题可能是主章节
      if (afterContext.match(/\d+\.\d+、/)) {
        return {
          type: 'main',
          level: 1,
          order: 1,
          detectedFormat: 'context_main'
        };
      }
    }
    
    // 默认返回子章节
    return {
      type: 'sub',
      level: 2,
      order: 1,
      detectedFormat: 'default_sub'
    };
  }
  
  /**
   * 识别章节重新生成的范围
   */
  static identifyRegenerationScope(
    chapterInfo: ChapterInfo, 
    outline: ResearchOutline | null
  ): { type: 'main_with_subs' | 'single_sub', chapters: (OutlineChapter | OutlineSection)[] } {
    
    if (chapterInfo.type === 'main') {
      // 主章节：需要重新生成主章节和所有子章节
      const mainChapter = outline?.chapters.find(ch => ch.id === chapterInfo.id);
      if (mainChapter) {
        return {
          type: 'main_with_subs',
          chapters: [mainChapter, ...mainChapter.sections]
        };
      }
    } else {
      // 子章节：只重新生成该子章节
      const section = this.findSectionInOutline(chapterInfo.id, outline);
      if (section) {
        return {
          type: 'single_sub',
          chapters: [section]
        };
      }
    }
    
    return { type: 'single_sub', chapters: [] };
  }
  
  /**
   * 在大纲中查找指定的子章节
   */
  private static findSectionInOutline(sectionId: string, outline: ResearchOutline | null): OutlineSection | null {
    if (!outline) return null;
    
    for (const chapter of outline.chapters) {
      const section = chapter.sections.find(s => s.id === sectionId);
      if (section) return section;
    }
    return null;
  }
  
  /**
   * 提取章节原始内容
   */
  static extractChapterContent(
    chapterInfo: ChapterInfo,
    originalReport: string,
    regenerationScope: { type: 'main_with_subs' | 'single_sub', chapters: (OutlineChapter | OutlineSection)[] }
  ): ChapterContent[] {
    const extractedContents: ChapterContent[] = [];
    
    // 检查是否有可用的章节数据
    if (regenerationScope.chapters.length === 0) {
      console.warn('⚠️ 没有找到章节大纲数据，将基于章节标题进行文本匹配');
      return this.extractByTitleFallback(chapterInfo, originalReport);
    }
    
    if (regenerationScope.type === 'main_with_subs') {
      // 主章节模式：提取主章节标题到下个主章节之间的所有内容
      const mainChapter = regenerationScope.chapters[0] as OutlineChapter;
      
      // 安全检查
      if (!mainChapter || !mainChapter.number) {
        console.warn('⚠️ 主章节数据不完整，使用标题匹配');
        return this.extractByTitleFallback(chapterInfo, originalReport);
      }
      
      const chapterPattern = new RegExp(`^(第${mainChapter.number}章.*?)(?=第\\d+章|$)`, 'ms');
      const match = originalReport.match(chapterPattern);
      
      if (match) {
        extractedContents.push({
          title: mainChapter.title,
          content: match[1],
          startIndex: originalReport.indexOf(match[1]),
          endIndex: originalReport.indexOf(match[1]) + match[1].length,
          level: 1,
          type: 'main'
        });
      }
    } else {
      // 子章节模式：只提取该子章节内容
      const section = regenerationScope.chapters[0] as OutlineSection;
      
      // 安全检查
      if (!section || !section.number) {
        console.warn('⚠️ 子章节数据不完整，使用标题匹配');
        return this.extractByTitleFallback(chapterInfo, originalReport);
      }
      
      const sectionPattern = new RegExp(`^(${section.number}、.*?)(?=\\d+\\.\\d+、|第\\d+章|$)`, 'ms');
      const match = originalReport.match(sectionPattern);
      
      if (match) {
        extractedContents.push({
          title: section.title,
          content: match[1],
          startIndex: originalReport.indexOf(match[1]),
          endIndex: originalReport.indexOf(match[1]) + match[1].length,
          level: 2,
          type: 'sub'
        });
      }
    }
    
    return extractedContents;
  }
  
  /**
   * 基于标题的备用提取方法
   */
  private static extractByTitleFallback(
    chapterInfo: ChapterInfo,
    originalReport: string
  ): ChapterContent[] {
    const title = chapterInfo.title;
    const titleIndex = originalReport.indexOf(title);
    
    if (titleIndex === -1) {
      console.warn(`⚠️ 在报告中未找到章节标题: "${title}"`);
      return [{
        title: title,
        content: `[未找到原始内容，将基于要求重新生成]`,
        startIndex: 0,
        endIndex: 0,
        level: chapterInfo.level,
        type: chapterInfo.type
      }];
    }
    
    // 找到标题位置，尝试提取章节内容
    const titleLineStart = originalReport.lastIndexOf('\n', titleIndex);
    const actualStart = titleLineStart === -1 ? 0 : titleLineStart + 1;
    
    // 寻找下一个章节或文档结尾
    const remainingText = originalReport.substring(titleIndex);
    const nextChapterPatterns = [
      /(?=第\d+章)/m,          // 下一个主章节
      /(?=^\d+\.\d+、)/m,      // 下一个子章节
      /(?=^#+ )/m,             // Markdown标题
      /(?=^\*\*第\d+章)/m,     // 粗体主章节
      /(?=^\*\*\d+\.\d+、)/m   // 粗体子章节
    ];
    
    let endIndex = originalReport.length;
    for (const pattern of nextChapterPatterns) {
      const match = remainingText.search(pattern);
      if (match > 0) {
        endIndex = titleIndex + match;
        break;
      }
    }
    
    const content = originalReport.substring(actualStart, endIndex);
    
    return [{
      title: title,
      content: content,
      startIndex: actualStart,
      endIndex: endIndex,
      level: chapterInfo.level,
      type: chapterInfo.type
    }];
  }
  
  /**
   * 提取章节中的引用资料编号
   */
  static extractReferences(content: string): string[] {
    const references: string[] = [];
    
    // 匹配引用格式 [数字] 或 [L-数字]
    const refPattern = /\[(?:L-)?(\d+)\]/g;
    const matches = content.match(refPattern);
    
    if (matches) {
      return [...new Set(matches)]; // 去重
    }
    
    return references;
  }
  
  /**
   * 获取引用资料的详细内容
   */
  static async getReferencedResources(
    references: string[],
    sources: Source[],
    localResources: Resource[]
  ): Promise<{ source: string; content: string }[]> {
    const referencedResources: { source: string; content: string }[] = [];
    const knowledgeStore = useKnowledgeStore.getState();
    
    for (const ref of references) {
      // 处理网络来源引用 [1], [2] 等
      if (/^\[\d+\]$/.test(ref)) {
        const index = parseInt(ref.replace(/[\[\]]/g, '')) - 1;
        if (sources[index]) {
          referencedResources.push({
            source: `网络来源${index + 1}: ${sources[index].title}`,
            content: sources[index].content || sources[index].description || ""
          });
        }
      }
      
      // 处理本地资源引用 [L-1], [L-2] 等
      if (/^\[L-\d+\]$/.test(ref)) {
        const index = parseInt(ref.replace(/[\[L-\]]/g, '')) - 1;
        if (localResources[index]) {
          const resource = localResources[index];
          const knowledge = knowledgeStore.get(resource.id);
          if (knowledge) {
            referencedResources.push({
              source: `本地资源${index + 1}: ${knowledge.title}`,
              content: knowledge.content
            });
          }
        }
      }
    }
    
    return referencedResources;
  }
  
  /**
   * 构建符合原始模板格式的章节重新生成提示词
   */
  static buildTemplateBasedRegenerationPrompt(context: ChapterRegenerationContext): string {
    const { chapterInfo, editDescription, editResources, originalContext, researchOutline, researchTopic, writingConfig } = context;
    
    // 🔥 使用增强的研究主题获取逻辑
    const enhancedResearchTopic = researchTopic || 
                                 originalContext.requirement || 
                                 useTaskStore.getState().question || 
                                 useTaskStore.getState().requirement ||
                                 '投资价值分析研究';
    
    console.log('构建提示词时使用的研究主题:', enhancedResearchTopic);
    
    // 识别重新生成范围
    const scope = this.identifyRegenerationScope(chapterInfo, researchOutline);
    const extractedContents = this.extractChapterContent(chapterInfo, context.originalReport, scope);
    
    // 构建专用资源内容
    const editResourcesText = editResources.map(resource => 
      `## ${resource.name}\n${resource.content}`
    ).join('\n\n');
    
    // 🔥 构建完整的原始章节内容 - 整合多个内容源
    let originalContentText = '';
    
    // 方法1: 使用提取的章节内容
    const extractedContentText = extractedContents.map(content => 
      `### ${content.title}\n${content.content}`
    ).join('\n\n');
    
    // 方法2: 使用DOM提取的内容（如果可用）
    const domExtractedContent = originalContext.extractedChapterContent || '';
    
    // 方法3: 简单文本提取备用
    let simpleExtractedContent = '';
    try {
      simpleExtractedContent = this.extractSimpleChapterContent(context.originalReport, chapterInfo.title);
    } catch (error) {
      console.warn('简单提取方法失败:', error);
    }
    
    // 🔥 选择最完整的内容源
    const contentSources = [
      { name: 'DOM提取内容', content: domExtractedContent, length: domExtractedContent.length },
      { name: '章节结构提取', content: extractedContentText, length: extractedContentText.length },
      { name: '简单文本提取', content: simpleExtractedContent, length: simpleExtractedContent.length }
    ].sort((a, b) => b.length - a.length); // 按长度降序排列
    
    console.log('=== 原文内容源比较 ===');
    contentSources.forEach((source, index) => {
      console.log(`${index + 1}. ${source.name}: ${source.length}字符`);
    });
    
    // 选择最长的内容作为主要内容
    const primaryContent = contentSources[0];
    originalContentText = primaryContent.content;
    
    // 🔥 内容质量验证和增强
    if (originalContentText.length < 500) {
      console.warn('⚠️ 主要内容太短，尝试合并多个来源');
      
      // 尝试合并多个内容源
      const combinedContent = contentSources
        .filter(source => source.length > 100)
        .map(source => `=== ${source.name} ===\n${source.content}`)
        .join('\n\n');
        
      if (combinedContent.length > originalContentText.length) {
        originalContentText = combinedContent;
        console.log('✅ 使用合并内容，总长度:', originalContentText.length);
      }
    }
    
    // 🔥 最终内容验证
    if (originalContentText.length < 200) {
      console.error('❌ 所有方法都无法提取到足够的原文内容');
      originalContentText = `⚠️ 原文内容提取不完整，仅有 ${originalContentText.length} 字符。\n\n${originalContentText}`;
    } else {
      console.log('✅ 最终原文内容长度:', originalContentText.length, '字符，来源:', primaryContent.name);
    }
    
    // 🔥 固定字数控制逻辑 - 统一使用4000-6000字
    const originalWordCount = originalContentText.length;
    const wordCountRange = '3000-3500字';
    const minWordCount = 3000;
    
    console.log(`原文字数: ${originalWordCount}, 固定目标字数范围: ${wordCountRange}`);
    
    // 获取完整的研究背景信息
    const projectInfo = this.extractProjectInfo(originalContext, researchOutline);
    
    // 从研究大纲中获取章节信息
    const chapterSection = this.findChapterInOutline(chapterInfo.title, researchOutline);
    const sectionTitle = chapterSection?.title || chapterInfo.title;
    
    // 生成安全的章节编号（用于显示）
    let sectionId = '1.1';
    if (chapterSection?.number && typeof chapterSection.number === 'string') {
      // 使用大纲中的number字段（如"1.1", "1.2"等）
      sectionId = chapterSection.number;
    } else {
      // 备用方案：从章节标题中提取编号
      const extractedNumber = this.extractChapterNumber(chapterInfo.title);
      if (extractedNumber && /^[\d.]+$/.test(extractedNumber)) {
        sectionId = extractedNumber;
      }
    }
    
    // 构建LEARNINGS部分（本地知识库内容）
    const learningsContent = this.buildLearningsContent(originalContext, editResources);
    
    // 构建PLAN部分
    const planContent = originalContext.plan || this.extractPlanFromOutline(chapterSection, researchOutline);
    
    // 确定章节编号和格式（用于提示词构建）
    // const chapterNumber = this.extractChapterNumber(chapterInfo.title);
    // const formatType = scope.type === 'main_with_subs' ? 'main' : 'sub';
    
    const prompt = `

**【重要】单一子章节重新生成要求：**

🔥 **生成范围限制（必须严格遵守）：**
- **仅生成指定子章节**：只生成"${sectionId}、${sectionTitle}"这一个子章节的内容
- **禁止生成其他章节**：严禁生成其他任何子章节（如${sectionId.split('.')[0]}.${parseInt(sectionId.split('.')[1]) + 1}、等）的标题或内容
- **禁止生成主章节标题**：不要生成"第X章"等主章节标题
- **单一章节编辑**：这是针对单个子章节的编辑，不是整个报告重写
- **内容边界明确**：生成的内容必须在指定子章节范围内，不得跨越到其他章节

**整体研究背景：**
本次研究的核心主题：${enhancedResearchTopic}
${projectInfo ? `项目信息：${projectInfo}` : ''}

**原文内容分析：**
${originalWordCount > 0 ? `原始章节字数：${originalWordCount}字\n目标字数范围：${wordCountRange}（固定生成4000-6000字的高质量内容）\n⚠️ 重要：必须基于ORIGINAL_CONTENT中的原文进行改进和扩展，而非重新撰写` : `新撰写章节，目标字数：${wordCountRange}`}

**标题格式（严格限制在当前子章节）：**
- 子章节主标题：严格使用"### **${sectionId}、${sectionTitle}**"格式（这是唯一允许的主标题）
- 二级标题：严格使用"#### ${sectionId}.1、具体标题"格式（如${sectionId}.1、产品核心描述）
- 三级标题：严格使用"##### 具体分析要点"格式（如需要）
- **🚫 禁止标题：** 不得生成任何其他章节的标题（如${sectionId.split('.')[0]}.${parseInt(sectionId.split('.')[1]) + 1}、第${parseInt(sectionId.split('.')[0]) + 1}章等）

**内容重新生成要求（不是分析要求）：**
- **字数控制**：严格控制在${wordCountRange}，${originalWordCount > 0 ? `确保生成内容不少于${minWordCount}字` : `确保生成内容不少于${minWordCount}字`}
- **生成完整的投资报告内容**，而不是分析框架或方法论
- **🔥 核心要求：基于原文内容改进**：
  * 必须以ORIGINAL_CONTENT部分的原始内容为基础
  * 在原文结构和观点基础上进行优化、补充和扩展
  * 保留原文的核心价值观点和重要数据
  * 改进表达方式，增强可读性和说服力
  * **严禁完全重写或忽略原文内容**
- **保持内容核心结构**：保持原文的主要观点和逻辑结构，但进行内容丰富和表达优化
- **内容扩展策略**：
  * 在原文基础上增加更多细节描述
  * 补充相关数据和分析
  * 增强论证逻辑和说服力
  * 优化段落结构和表达方式
- 重点描述：${sectionTitle}的具体情况、数据、表现和投资价值
- 确保内容聚焦，避免泛泛而谈
- 避免重复其他章节已描述的内容
- **特别编辑要求：** ${editDescription}
- **内容连贯性**：确保新生成的内容与整体报告主题"${enhancedResearchTopic}"保持一致

${writingConfig ? this.generateWritingConfigPrompt(writingConfig) : `**写作风格要求：**
- **报告内容风格**：使用投资研究报告的专业写作风格
- **具体描述**：提供具体的数据、案例、表现和评估结果
- **客观专业**：保持客观分析但得出明确结论
- **投资导向**：从投资价值角度进行内容组织`}

**引用规范（必须严格遵守）：**
- **本地资源引用：** 在描述时必须引用本地研究资料，使用[L-1]、[L-2]等格式
- **网络来源引用：** 引用网络资料时使用[1]、[2]等格式
- **引用密度控制：** 每段最多2-3个引用，避免过度引用影响阅读
- **引用位置：** 重要观点和数据后引用，避免在句子中间插入引用
- **引用示例：** "根据公司年报显示，该项技术已获得多项专利保护[L-1]。"

**格式规范（适配分章节写作）：**
- **粗体使用：** 每段最多1-2处粗体，突出关键概念和重要数据
- **段落间距：** 不同要点间必须空行分隔，便于合并时保持清晰结构
- **表格格式：** 使用标准Markdown表格，表格前后各空一行
- **列表格式：** 使用统一的项目符号（-），避免混用不同符号
- **数据展示：** 重要数据可使用表格或突出显示，但避免过度格式化

**分章节写作特别注意：**
- 本节内容将与其他子章节合并，确保格式统一性
- 引用编号在合并时会统一调整，使用相对引用即可
- 标题层级严格按规范，便于自动合并处理

**【关键输出要求】：**
🔥 **严格限制输出范围**：只输出"${sectionId}、${sectionTitle}"这一个子章节的内容，不得包含其他任何章节。

请直接输出完整的投资报告子章节内容，而不是分析框架。${originalWordCount > 0 ? `⚠️ 核心要求：必须基于ORIGINAL_CONTENT中的原始内容进行改进和扩展，严禁完全重写或忽略原文。` : ''}基于研究主题"${enhancedResearchTopic}"和ORIGINAL_CONTENT中的原始内容，对"${sectionTitle}"进行优化和改进。

🚫 **严禁输出内容**：
- 不得生成其他子章节（如${sectionId.split('.')[0]}.${parseInt(sectionId.split('.')[1]) + 1}、${sectionId.split('.')[0]}.${parseInt(sectionId.split('.')[1]) - 1}、等）
- 不得生成主章节标题（如第${parseInt(sectionId.split('.')[0]) + 1}章、第${sectionId.split('.')[0]}章等）
- 不得生成章节总结或其他章节的内容
- 不得包含任何格式说明、写作指导或元信息解释

✅ **必须输出内容**：
- 只输出"### **${sectionId}、${sectionTitle}**"这一个子章节的实际内容
- 确保生成内容不少于${minWordCount}字，保持${wordCountRange}的篇幅
- 内容必须完整、独立，符合投资报告质量标准

---

根据以下研究计划和调研资料，结合研究主题"${enhancedResearchTopic}"，${originalWordCount > 0 ? '基于ORIGINAL_CONTENT中的原始内容进行改进和扩展' : '撰写'}报告子章节"${sectionId}、${sectionTitle}"（仅此一个子章节）：

<CONTENT_SOURCES>
<PLAN>
${planContent}
</PLAN>

<LEARNINGS>
${learningsContent}
</LEARNINGS>

${editResourcesText ? `<EDIT_RESOURCES>\n${editResourcesText}\n</EDIT_RESOURCES>` : ''}

${originalContentText ? `<ORIGINAL_CONTENT>\n⚠️ 重要：以下是原始章节内容，必须基于此内容进行改进和扩展，严禁完全重写！\n\n${originalContentText}\n\n⚠️ 上述原文内容是改进的基础，新内容必须在此基础上进行优化和扩展。\n</ORIGINAL_CONTENT>` : ''}
</CONTENT_SOURCES>

**网络搜索结果学习要点：**

${this.buildNetworkSearchResults(originalContext.sources)}

${this.buildNetworkSourceReferences(originalContext.sources)}

---

**内容生成指导（针对研究主题"${enhancedResearchTopic}"）：**

基于研究主题和学习要点，在${originalWordCount > 0 ? '改进和扩展' : '撰写'}"${sectionTitle}"时应该包含：

*   **具体表现描述：** 基于收集的数据和信息，具体描述该方面的现状、特点和表现
*   **数据支撑分析：** 提供具体的数据、指标和量化信息来支撑内容描述
*   **投资价值评估：** 从投资角度评估该方面对整体投资价值的贡献和影响
*   **风险机遇识别：** 识别相关的投资风险和机遇，提供专业判断
${originalWordCount > 0 ? `*   **原文内容保持：** 保留ORIGINAL_CONTENT中的核心观点、重要数据和主要结论
*   **内容优化改进：** 在原文基础上改进表达方式、增强逻辑性、补充细节描述
*   **篇幅扩展要求：** 确保生成内容达到${wordCountRange}，在原文基础上进行有意义的扩展` : `*   **字数要求：** 确保生成内容达到${wordCountRange}的标准篇幅`}

请直接输出符合格式要求的完整章节内容。`.trim();
    
    return prompt;
  }
  
  /**
   * 在研究大纲中查找章节信息
   */
  private static findChapterInOutline(chapterTitle: string, outline: any): any {
    if (!outline || !outline.chapters) return null;
    
    for (const chapter of outline.chapters) {
      if (chapter.title === chapterTitle) return chapter;
      
      if (chapter.sections) {
        for (const section of chapter.sections) {
          if (section.title === chapterTitle || chapterTitle.includes(section.title)) {
            return section;
          }
        }
      }
    }
    return null;
  }

  /**
   * 构建LEARNINGS内容（本地知识库）
   */
  private static buildLearningsContent(originalContext: any, editResources: any[]): string {
    let learnings = "以下是从提供的本地知识库内容中提取的学习要点：\n\n";
    let keyPointsFound = false;
    
    // 获取知识库实例
    const knowledgeStore = useKnowledgeStore.getState();
    
    // 添加原始研究资源的学习要点
    if (originalContext.localResources && originalContext.localResources.length > 0) {
      originalContext.localResources.forEach((resource: any, index: number) => {
        // 通过resource.id从知识库获取实际内容
        const knowledge = knowledgeStore.get(resource.id);
        if (knowledge && knowledge.content) {
          // 从资源内容中提取关键信息点
          const keyPoints = this.extractKeyPointsFromContent(knowledge.content);
          keyPoints.forEach(point => {
            learnings += `*   **${point}** [L-${index + 1}]。\n`;
            keyPointsFound = true;
          });
        }
      });
    }
    
    // 添加专用编辑资源的学习要点
    if (editResources && editResources.length > 0) {
      editResources.forEach((resource, index) => {
        if (resource.content) {
          const keyPoints = this.extractKeyPointsFromContent(resource.content);
          keyPoints.forEach(point => {
            learnings += `*   **${point}** [L-E${index + 1}]。\n`;
            keyPointsFound = true;
          });
        }
      });
    }
    
    // 如果没有找到任何要点，提供默认内容
    if (!keyPointsFound) {
      learnings += "*   **本次编辑将基于原始章节内容和编辑要求进行优化**。\n";
      learnings += "*   **请参考ORIGINAL_CONTENT部分的原始内容进行分析改进**。\n";
      learnings += "*   **结合编辑要求和专用资源进行内容重新生成**。\n";
    }
    
    // 🔥 修复：只有在有资源需要引用时才添加参考文献标题
    // 避免在子章节中添加多余的"参考文献"标题
    const hasResources = (originalContext.localResources && originalContext.localResources.length > 0) || 
                        (editResources && editResources.length > 0);
    
    if (hasResources) {
      learnings += "\n### 参考文献\n\n";
      
      // 添加参考文献列表
      if (originalContext.localResources && originalContext.localResources.length > 0) {
        originalContext.localResources.forEach((resource: any, index: number) => {
          learnings += `[L-${index + 1}]: ${resource.name}\n`;
        });
      }
      
      if (editResources && editResources.length > 0) {
        editResources.forEach((resource, index) => {
          learnings += `[L-E${index + 1}]: ${resource.name}\n`;
        });
      }
    }
    
    return learnings;
  }

  /**
   * 从内容中提取关键信息点
   */
  private static extractKeyPointsFromContent(content: string): string[] {
    const points: string[] = [];
    
    if (!content || content.trim().length === 0) {
      return points;
    }
    
    // 尝试从内容中提取要点
    const lines = content.split('\n');
    let currentPoint = '';
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过空行和过短的行
      if (!trimmedLine || trimmedLine.length < 15) continue;
      
      // 检查是否是标题
      if (trimmedLine.match(/^#+\s+/)) {
        if (currentPoint) {
          points.push(currentPoint.trim());
          currentPoint = '';
        }
        // 提取标题作为要点（去掉#号）
        const title = trimmedLine.replace(/^#+\s+/, '').trim();
        if (title.length > 10 && title.length < 100) {
          points.push(title);
        }
        continue;
      }
      
      // 检查是否是列表项
      if (trimmedLine.match(/^[*-]\s+/)) {
        if (currentPoint) {
          points.push(currentPoint.trim());
          currentPoint = '';
        }
        currentPoint = trimmedLine.replace(/^[*-]\s+/, '').trim();
        continue;
      }
      
      // 检查是否是编号列表
      if (trimmedLine.match(/^\d+\.\s+/)) {
        if (currentPoint) {
          points.push(currentPoint.trim());
          currentPoint = '';
        }
        currentPoint = trimmedLine.replace(/^\d+\.\s+/, '').trim();
        continue;
      }
      
      // 如果当前有要点正在构建，则继续添加
      if (currentPoint) {
        currentPoint += ' ' + trimmedLine;
        // 如果要点变得太长，截断它
        if (currentPoint.length > 200) {
          points.push(currentPoint.substring(0, 200).trim() + '...');
          currentPoint = '';
        }
      } else if (trimmedLine.length > 30 && trimmedLine.length < 150) {
        // 独立的中等长度段落作为一个要点
        // 过滤掉一些明显不是要点的内容
        if (!trimmedLine.match(/^(图|表|附录|参考|引用)/)) {
          points.push(trimmedLine);
        }
      }
      
      // 限制要点数量
      if (points.length >= 8) break;
    }
    
    // 添加最后一个要点
    if (currentPoint && points.length < 8) {
      if (currentPoint.length > 200) {
        points.push(currentPoint.substring(0, 200).trim() + '...');
    } else {
        points.push(currentPoint.trim());
      }
    }
    
    // 过滤和清理要点
    const cleanedPoints = points
      .filter(point => point.length > 10 && point.length < 250)
      .map(point => {
        // 移除一些常见的无用前缀
        return point.replace(/^(注意|备注|说明|提示)[:：]?\s*/, '').trim();
      })
      .filter(point => point.length > 10)
      .slice(0, 6); // 最多返回6个要点
    
    return cleanedPoints;
  }

  /**
   * 从大纲中提取计划内容
   */
  private static extractPlanFromOutline(chapterSection: any, outline: any): string {
    if (!chapterSection || !outline) {
      return "深入分析项目公司的相关业务领域，提供专业、客观的研究分析。";
    }
    
    let plan = "";
    
    if (chapterSection.description) {
      plan += chapterSection.description + "\n\n";
    }
    
    if (chapterSection.researchPoints && chapterSection.researchPoints.length > 0) {
      plan += "重点研究方向：\n";
      chapterSection.researchPoints.forEach((point: string) => {
        plan += `- ${point}\n`;
      });
    }
    
    return plan || "深入分析项目公司的相关业务领域，提供专业、客观的研究分析。";
  }

  /**
   * 提取项目信息
   */
  private static extractProjectInfo(originalContext: any, researchOutline: any): string {
    let projectInfo = '';
    
    // 从大纲中提取项目信息
    if (researchOutline?.projectInfo) {
      projectInfo = researchOutline.projectInfo;
    } else if (researchOutline?.title) {
      projectInfo = researchOutline.title;
    }
    
    // 从原始上下文中提取项目信息
    if (!projectInfo && originalContext.requirement) {
      // 尝试从requirement中提取公司名或项目名
      const companyMatch = originalContext.requirement.match(/([^，、。？！,?.!]*(?:公司|集团|企业|股份|有限)[^，、。？！,?.!]*)/);
      if (companyMatch) {
        projectInfo = companyMatch[1].trim();
      }
    }
    
    return projectInfo;
  }

  /**
   * 构建网络搜索结果内容
   */
  private static buildNetworkSearchResults(sources: any[]): string {
    if (!sources || sources.length === 0) {
      return "暂无网络搜索结果，请基于本地资源进行分析。";
    }
    
    let results = "";
    
    sources.forEach((source, index) => {
      if (source.title && source.content) {
        results += `${index + 1}. **${source.title}：**\n`;
        results += `   ${source.content.substring(0, 200)}...\n\n`;
      }
    });
    
    return results;
  }

  /**
   * 构建网络来源参考文献列表
   */
  private static buildNetworkSourceReferences(sources: any[]): string {
    if (!sources || sources.length === 0) {
      return "";
    }
    
    let references = "\n\n## 网络来源参考文献\n\n";
    
    sources.forEach((source, index) => {
      const title = source.title || '未知标题';
      const url = source.url || '#';
      references += `[${index + 1}]: ${url}${title ? ` "${title.replaceAll('"', ' ')}"` : ""}\n`;
    });
    
    return references;
  }

  /**
   * 生成完整的写作配置提示词
   */
  private static generateWritingConfigPrompt(writingConfig: any): string {
    const styleMap = {
      academic: '请以学术写作风格撰写：采用严谨的学术语言体系，以连贯的段落形式展开论述，通过逻辑递进的方式构建完整的理论框架。在论证过程中自然融入权威文献引用和数据支撑，运用"首先...其次...再者...最后"等学术连接词构建清晰的逻辑链条，语言表达客观中性，基于实证分析得出结论',
      business: '请以中国官方/正式报告风格撰写：采用整段连贯叙述，避免分项符号和子标题罗列，用连接词自然过渡，确保逻辑严谨、语言精练庄重，将要点融入段落中阐述',
      journalistic: '请以新闻写作风格撰写：以连贯的叙述方式呈现核心事实，通过自然的语言过渡串联关键信息点，避免生硬的条目罗列。运用"据悉"、"与此同时"、"值得注意的是"等新闻常用连接词，保持报道的流畅性和可读性，语言简洁生动，突出新闻价值和时效性',
      technical: '请以技术写作风格撰写：采用逻辑严密的技术叙述方式，通过"基于此"、"进而"、"由此可见"等技术连接词构建清晰的技术路径。将技术细节和数据分析有机融入连贯的段落表述中，避免机械的参数罗列，确保技术逻辑的完整性，语言准确专业'
    };

    let prompt = `**写作风格要求：**
${styleMap[writingConfig.style as keyof typeof styleMap] || '请使用投资研究报告的专业写作风格'}

**语言要求：**
${writingConfig.languageRequirements || '使用中文'}`;

    if (writingConfig.customInstructions && writingConfig.customInstructions.trim()) {
      prompt += `

**自定义指令：**
${writingConfig.customInstructions}`;
    }

    // 添加一些报告特定的要求
    prompt += `

**报告内容风格：**
- 结合以上配置，使用专业的投资研究报告写作风格
- 提供具体的数据、案例、表现和评估结果
- 保持客观分析但得出明确结论
- 从投资价值角度进行内容组织`;

    if (writingConfig.avoidDuplication) {
      prompt += `
- 避免内容重复，确保独特性`;
    }

    return prompt;
  }

  /**
   * 获取写作风格描述（保留用于向后兼容）
   */
  private static getWritingStyleDescription(style?: string): string {
    switch(style) {
      case 'academic':
        return '学术写作风格：采用严谨的学术语言体系，以连贯的段落形式展开论述，通过逻辑递进的方式构建完整的理论框架。在论证过程中自然融入权威文献引用和数据支撑，运用"首先...其次...再者...最后"等学术连接词构建清晰的逻辑链条，语言表达客观中性，基于实证分析得出结论';
      case 'business':
        return '商务写作风格（中国官方/正式报告风格）：采用分层递进的结构逻辑，在需要条理化展示复杂内容时适当使用"1、2、3、4"等数字列举要点。保持段落间的自然连贯，每个要点控制在150-200字，确保表述清晰、逻辑严谨。运用"同时"、"此外"、"另一方面"、"综合而言"等连接词实现段落间的流畅过渡。语言精练庄重，体现正式报告的权威性和专业性，重点突出关键数据和业绩指标的深度分析';
      case 'journalistic':
        return '新闻写作风格：以连贯的叙述方式呈现核心事实，通过自然的语言过渡串联关键信息点，避免生硬的条目罗列。运用"据悉"、"与此同时"、"值得注意的是"等新闻常用连接词，保持报道的流畅性和可读性，语言简洁生动，突出新闻价值和时效性';
      case 'technical':
        return '技术写作风格：采用逻辑严密的技术叙述方式，通过"基于此"、"进而"、"由此可见"等技术连接词构建清晰的技术路径。将技术细节和数据分析有机融入连贯的段落表述中，避免机械的参数罗列，确保技术逻辑的完整性，语言准确专业';
      default:
        return '投资研究报告专业写作风格';
    }
  }
} 