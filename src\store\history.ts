import { create } from "zustand";
import { persist, type StorageValue } from "zustand/middleware";
import type { TaskStore } from "./task";
import { researchStore } from "@/utils/storage";
import { customAlphabet } from "nanoid";
import { clone, pick } from "radash";

export interface ResearchHistory extends TaskStore {
  createdAt: number;
  updatedAt?: number;
}

export interface HistoryStore {
  history: ResearchHistory[];
}

interface HistoryFunction {
  save: (taskStore: TaskStore) => string;
  autoSave: (taskStore: TaskStore) => string;
  load: (id: string) => TaskStore | void;
  update: (id: string, taskStore: TaskStore) => boolean;
  remove: (id: string) => boolean;
}

const nanoid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz", 12);

export const useHistoryStore = create(
  persist<HistoryStore & HistoryFunction>(
    (set, get) => ({
      history: [],
      save: (taskStore) => {
        // 🔥 放宽保存条件：有研究主题即可保存
        const hasResearchTopic = taskStore.question?.trim() || 
                                taskStore.title?.trim() || 
                                taskStore.requirement?.trim();
        
        if (hasResearchTopic) {
          const id = nanoid();
          const newHistory: ResearchHistory = {
            ...clone(taskStore),
            id,
            createdAt: Date.now(),
          };
          set((state) => ({ history: [newHistory, ...state.history] }));
          return id;
        }
        return "";
      },
      
      // 🔥 新增：自动保存中间状态
      autoSave: (taskStore) => {
        const hasMinimalData = taskStore.question?.trim() || 
                              taskStore.tasks.length > 0 ||
                              taskStore.resources.length > 0 ||
                              taskStore.reportPlan?.trim();
        
        if (hasMinimalData) {
          // 实现去重逻辑，避免重复保存相同状态
          const { history } = get();
          const latest = history[0];
          
          // 检查是否需要更新最新记录（5分钟内的记录）
          if (latest && latest.createdAt > Date.now() - 300000) {
            get().update(latest.id, taskStore);
            return latest.id;
          } else {
            return get().save(taskStore);
          }
        }
        return "";
      },
      load: (id) => {
        const current = get().history.find((item) => item.id === id);
        if (current) return clone(current);
      },
      update: (id, taskStore) => {
        const newHistory = get().history.map((item) => {
          if (item.id === id) {
            return {
              ...clone(taskStore),
              updatedAt: Date.now(),
            } as ResearchHistory;
          } else {
            return item;
          }
        });
        set(() => ({ history: [...newHistory] }));
        return true;
      },
      remove: (id) => {
        set((state) => ({
          history: state.history.filter((item) => item.id !== id),
        }));
        return true;
      },
    }),
    {
      name: "historyStore",
      version: 1,
      storage: {
        getItem: async (key: string) => {
          return await researchStore.getItem<
            StorageValue<HistoryStore & HistoryFunction>
          >(key);
        },
        setItem: async (
          key: string,
          store: StorageValue<HistoryStore & HistoryFunction>
        ) => {
          return await researchStore.setItem(key, {
            state: pick(store.state, ["history"]),
            version: store.version,
          });
        },
        removeItem: async (key: string) => await researchStore.removeItem(key),
      },
    }
  )
);
