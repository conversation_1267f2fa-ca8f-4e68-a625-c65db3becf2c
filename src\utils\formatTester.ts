/**
 * 格式处理测试工具
 */

import { fixFormat } from './formatFixer';
import { validateFormat } from './formatValidator';

/**
 * 测试格式处理功能
 */
export function testFormatProcessing() {
  console.log('🧪 开始格式处理功能测试...\n');

  // 测试用例1：标题格式问题
  const testCase1 = `
项目公司商业模式分析
1. 主要业务
2. 收入来源
项目公司经营状况
1. 财务表现
2. 运营指标
`;

  console.log('测试用例1：标题格式修复');
  console.log('原始内容:', testCase1.trim());
  
  const result1 = fixFormat(testCase1, {
    fixTitles: true,
    fixBold: false,
    fixSpacing: false,
    fixCitations: false
  });
  
  console.log('修复后内容:', result1.content.trim());
  console.log('修复的问题数:', result1.fixedIssues.length);
  console.log('---\n');

  // 测试用例2：粗体格式问题
  const testCase2 = `
这是一个包含*不完整粗体*的段落，还有**正确粗体**和**过多的**、**粗体**、**使用**。
另一个段落包含*单星号*问题。
`;

  console.log('测试用例2：粗体格式修复');
  console.log('原始内容:', testCase2.trim());
  
  const result2 = fixFormat(testCase2, {
    fixTitles: false,
    fixBold: true,
    fixSpacing: false,
    fixCitations: false,
    maxBoldPerParagraph: 2
  });
  
  console.log('修复后内容:', result2.content.trim());
  console.log('修复的问题数:', result2.fixedIssues.length);
  console.log('---\n');

  // 测试用例3：引用格式问题
  const testCase3 = `
根据研究报告(1)(2)，公司发展良好【L-3】。
更多信息参见 [4] [5] 和【6】。
`;

  console.log('测试用例3：引用格式修复');
  console.log('原始内容:', testCase3.trim());
  
  const result3 = fixFormat(testCase3, {
    fixTitles: false,
    fixBold: false,
    fixSpacing: false,
    fixCitations: true
  });
  
  console.log('修复后内容:', result3.content.trim());
  console.log('修复的问题数:', result3.fixedIssues.length);
  console.log('---\n');

  // 测试用例4：代码块和特殊内容保护
  const testCase4 = `
项目公司商业模式

公司的**主要技术架构**如下：

\`\`\`mermaid
flowchart TD
    A[项目公司] --> B{*是否盈利*}
    B -->|Yes| C[**继续投资**]
    B -->|No| D[调整策略]
    C --> E[扩大规模]
\`\`\`

代码示例：

\`\`\`javascript
// 这里包含**星号**和*单星号*
const company = {
  name: "项目公司",
  profit: true,
  data: [1, 2, 3]
};
\`\`\`

业务数据如下**重要指标**：
- 营收增长 **15%**
- 利润率 **12%**
`;

  console.log('测试用例4：代码块保护测试');
  console.log('原始内容:', testCase4.trim());
  
  const result4 = fixFormat(testCase4, {
    fixTitles: true,
    fixBold: true,
    fixSpacing: false,
    fixCitations: true,
    maxBoldPerParagraph: 2
  });
  
  console.log('修复后内容:', result4.content.trim());
  console.log('修复的问题数:', result4.fixedIssues.length);
  console.log('---\n');

  // 测试用例5：综合测试
  const testCase5 = `
项目公司商业模式
1. 核心业务分析

公司的*主要业务*包括**软件开发**、**技术咨询**、**系统集成**和**数据分析**等领域。

根据财务报告(1)和年报【2】，公司表现良好 [3] [4]。

项目公司经营状况  
1. 财务指标

营收同比增长**15%**，利润率达到**12.5%**，表现*优异*。
`;

  console.log('测试用例5：综合格式修复');
  console.log('原始内容:', testCase5.trim());
  
  const result5 = fixFormat(testCase5);
  
  console.log('修复后内容:', result5.content.trim());
  console.log('修复的问题数:', result5.fixedIssues.length);
  console.log('剩余问题数:', result5.remainingIssues.length);
  
  console.log('\n✅ 格式处理功能测试完成！');
  
  return {
    testCase1: result1,
    testCase2: result2,
    testCase3: result3,
    testCase4: result4,
    testCase5: result5
  };
}

/**
 * 验证格式问题检测功能
 */
export function testFormatValidation() {
  console.log('🔍 开始格式验证功能测试...\n');

  const testContent = `
项目公司商业模式  
1. 业务模式
这是一个包含**过多**、**粗体**、**使用**的段落。
参考资料(1)(2)和【L-3】。

\`\`\`mermaid
flowchart TD
    A[*这里的星号*] --> B[**不应该被格式化**]
\`\`\`

代码块后的正常内容包含*格式问题*。
`;

  console.log('测试内容:', testContent.trim());
  
  const issues = validateFormat(testContent);
  
  console.log(`\n发现 ${issues.length} 个格式问题:`);
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. 第${issue.line}行 (${issue.type}): ${issue.expected} [${issue.severity}]`);
  });
  
  console.log('\n✅ 格式验证功能测试完成！');
  
  return issues;
}

/**
 * 测试代码块检测功能
 */
export function testCodeBlockDetection() {
  console.log('🔍 开始代码块检测功能测试...\n');

  const testContent = `
# 标题

普通段落内容。

\`\`\`mermaid
flowchart TD
    A[开始] --> B[结束]
\`\`\`

另一个段落。

\`\`\`javascript
const x = 1;
console.log(x);
\`\`\`

结束段落。

\`\`\`
未指定语言的代码块
\`\`\`

最后一段。
`;

  console.log('测试内容:', testContent.trim());
  
  // 因为detectCodeBlocks是私有函数，我们通过格式化结果来验证
  const result = fixFormat(testContent, {
    fixTitles: false,
    fixBold: true,
    fixSpacing: false,
    fixCitations: false
  });
  
  console.log('\n代码块检测通过格式化验证:');
  console.log('- 如果代码块内容保持不变，说明检测正确');
  console.log('- 修复的问题数:', result.fixedIssues.length);
  
  console.log('\n修复后内容:');
  console.log(result.content.trim());
  
  console.log('\n✅ 代码块检测功能测试完成！');
  
  return result;
}

// 导出便捷测试函数
export function runAllTests() {
  console.log('🚀 运行所有格式处理测试...\n');
  
  const formatTests = testFormatProcessing();
  console.log('\n' + '='.repeat(50) + '\n');
  
  const validationTests = testFormatValidation();
  console.log('\n' + '='.repeat(50) + '\n');
  
  const codeBlockTests = testCodeBlockDetection();
  
  console.log('\n🎉 所有测试完成！');
  
  return {
    formatTests,
    validationTests,
    codeBlockTests
  };
} 