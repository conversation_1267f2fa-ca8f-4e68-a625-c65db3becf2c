#!/usr/bin/env node

/**
 * 快速设置章节字数控制配置
 * 使用方法: node scripts/set-word-count.js [模型类型]
 * 模型类型: high | medium | low | custom
 */

const fs = require('fs');
const path = require('path');

// 预设配置
const presets = {
  high: {
    name: '高输出能力模型 (GPT-4, Claude-3.5, Gemini-2.0)',
    config: {
      CHAPTER_1_WORD_MIN: 3500,
      CHAPTER_1_WORD_MAX: 4500,
      CHAPTER_2_WORD_MIN: 4000,
      CHAPTER_2_WORD_MAX: 5000,
      CHAPTER_3_WORD_MIN: 2500,
      CHAPTER_3_WORD_MAX: 3500,
      CHAPTER_4_WORD_MIN: 2200,
      CHAPTER_4_WORD_MAX: 3000,
      CHAPTER_5_WORD_MIN: 3500,
      CHAPTER_5_WORD_MAX: 4500,
      CHAPTER_6_WORD_MIN: 2800,
      CHAPTER_6_WORD_MAX: 3800
    }
  },
  medium: {
    name: '中等输出能力模型 (GPT-3.5, Gemini Pro)',
    config: {
      CHAPTER_1_WORD_MIN: 2200,
      CHAPTER_1_WORD_MAX: 3000,
      CHAPTER_2_WORD_MIN: 2500,
      CHAPTER_2_WORD_MAX: 3200,
      CHAPTER_3_WORD_MIN: 1500,
      CHAPTER_3_WORD_MAX: 2200,
      CHAPTER_4_WORD_MIN: 1200,
      CHAPTER_4_WORD_MAX: 1800,
      CHAPTER_5_WORD_MIN: 2200,
      CHAPTER_5_WORD_MAX: 3000,
      CHAPTER_6_WORD_MIN: 1800,
      CHAPTER_6_WORD_MAX: 2500
    }
  },
  low: {
    name: '低输出能力模型 (较小的开源模型)',
    config: {
      CHAPTER_1_WORD_MIN: 1500,
      CHAPTER_1_WORD_MAX: 2200,
      CHAPTER_2_WORD_MIN: 1800,
      CHAPTER_2_WORD_MAX: 2500,
      CHAPTER_3_WORD_MIN: 1000,
      CHAPTER_3_WORD_MAX: 1500,
      CHAPTER_4_WORD_MIN: 800,
      CHAPTER_4_WORD_MAX: 1200,
      CHAPTER_5_WORD_MIN: 1500,
      CHAPTER_5_WORD_MAX: 2200,
      CHAPTER_6_WORD_MIN: 1200,
      CHAPTER_6_WORD_MAX: 1800
    }
  },
  default: {
    name: '默认配置',
    config: {
      CHAPTER_1_WORD_MIN: 2800,
      CHAPTER_1_WORD_MAX: 3800,
      CHAPTER_2_WORD_MIN: 3000,
      CHAPTER_2_WORD_MAX: 3800,
      CHAPTER_3_WORD_MIN: 2000,
      CHAPTER_3_WORD_MAX: 2800,
      CHAPTER_4_WORD_MIN: 1800,
      CHAPTER_4_WORD_MAX: 2500,
      CHAPTER_5_WORD_MIN: 2800,
      CHAPTER_5_WORD_MAX: 3800,
      CHAPTER_6_WORD_MIN: 2200,
      CHAPTER_6_WORD_MAX: 3000
    }
  }
};

function updateEnvFile(config, presetName) {
  const envPath = path.join(process.cwd(), '.env');
  const envLocalPath = path.join(process.cwd(), '.env.local');
  
  // 寻找现有的 .env 文件
  let targetPath = envPath;
  if (fs.existsSync(envLocalPath)) {
    targetPath = envLocalPath;
  } else if (!fs.existsSync(envPath)) {
    // 如果都不存在，从模板复制
    const templatePath = path.join(process.cwd(), 'env.tpl');
    if (fs.existsSync(templatePath)) {
      fs.copyFileSync(templatePath, envPath);
      console.log('📄 已从模板创建 .env 文件');
    } else {
      console.log('❌ 未找到 env.tpl 模板文件');
      return false;
    }
  }

  try {
    let envContent = fs.readFileSync(targetPath, 'utf8');
    
    // 更新或添加字数控制配置
    Object.entries(config).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      const newLine = `${key}=${value}`;
      
      if (regex.test(envContent)) {
        envContent = envContent.replace(regex, newLine);
      } else {
        // 如果没有找到该配置，添加到文件末尾
        if (!envContent.includes(`# Chapter word count control`)) {
          envContent += `\n# Chapter word count control for different model capabilities\n`;
        }
        envContent += `${newLine}\n`;
      }
    });
    
    fs.writeFileSync(targetPath, envContent);
    console.log(`✅ 已更新 ${path.basename(targetPath)} 文件`);
    console.log(`📊 应用配置: ${presetName}`);
    console.log('🔄 请重启应用以使配置生效');
    return true;
  } catch (error) {
    console.error('❌ 更新配置文件失败:', error.message);
    return false;
  }
}

function showUsage() {
  console.log('📚 章节字数控制配置工具\n');
  console.log('使用方法: node scripts/set-word-count.js [模型类型]\n');
  console.log('可用的模型类型:');
  Object.entries(presets).forEach(([key, preset]) => {
    console.log(`  ${key.padEnd(8)} - ${preset.name}`);
  });
  console.log('\n示例:');
  console.log('  node scripts/set-word-count.js high');
  console.log('  node scripts/set-word-count.js medium');
  console.log('  node scripts/set-word-count.js low');
}

function showCurrentConfig() {
  console.log('\n当前配置预览:');
  console.log('章节                          | 字数范围');
  console.log('------------------------------|----------');
  console.log('第1章：商业模式分析           | ${env.CHAPTER_1_WORD_MIN || 2800}-${env.CHAPTER_1_WORD_MAX || 3800}字');
  console.log('第2章：经营状况分析           | ${env.CHAPTER_2_WORD_MIN || 3000}-${env.CHAPTER_2_WORD_MAX || 3800}字');
  console.log('第3章：管理团队分析           | ${env.CHAPTER_3_WORD_MIN || 2000}-${env.CHAPTER_3_WORD_MAX || 2800}字');
  console.log('第4章：治理结构分析           | ${env.CHAPTER_4_WORD_MIN || 1800}-${env.CHAPTER_4_WORD_MAX || 2500}字');
  console.log('第5章：发展情况与投资回报分析 | ${env.CHAPTER_5_WORD_MIN || 2800}-${env.CHAPTER_5_WORD_MAX || 3800}字');
  console.log('第6章：总结与评价             | ${env.CHAPTER_6_WORD_MIN || 2200}-${env.CHAPTER_6_WORD_MAX || 3000}字');
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showUsage();
    return;
  }
  
  const presetType = args[0].toLowerCase();
  
  if (!presets[presetType]) {
    console.log(`❌ 未知的模型类型: ${presetType}\n`);
    showUsage();
    return;
  }
  
  const preset = presets[presetType];
  console.log(`🎯 正在设置配置: ${preset.name}`);
  
  if (updateEnvFile(preset.config, preset.name)) {
    console.log('\n✨ 配置完成！');
  }
}

main(); 