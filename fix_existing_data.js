// 修复现有数据脚本 - 在浏览器控制台运行
// 这将尝试从taskStore恢复缺失的知识库条目

console.log("开始修复现有数据...");

try {
  // 获取当前数据
  const taskStore = JSON.parse(localStorage.getItem('taskStore') || '{}');
  const knowledgeStore = JSON.parse(localStorage.getItem('knowledgeStore') || '{}');
  
  if (!taskStore.state?.resources || !knowledgeStore.state?.knowledges) {
    console.error("数据结构异常，请使用重置方案");
    throw new Error("数据结构异常");
  }
  
  const resources = taskStore.state.resources;
  const knowledges = knowledgeStore.state.knowledges;
  const existingIds = new Set(knowledges.map(k => k.id));
  
  console.log(`现有资源: ${resources.length}个`);
  console.log(`现有知识库条目: ${knowledges.length}个`);
  
  // 查找缺失的知识库条目
  const missingResources = resources.filter(r => 
    r.status === 'completed' && !existingIds.has(r.id)
  );
  
  console.log(`发现缺失的知识库条目: ${missingResources.length}个`);
  
  if (missingResources.length === 0) {
    console.log("没有发现数据不一致问题");
    return;
  }
  
  // 创建缺失的知识库条目（模拟数据，因为原始内容已丢失）
  const currentTime = Date.now();
  const newKnowledges = missingResources.map(resource => ({
    id: resource.id,
    title: resource.name,
    content: `[数据修复] 此文件内容需要重新上传。原文件: ${resource.name}`,
    type: 'file',
    fileMeta: {
      name: resource.name,
      size: resource.size,
      type: resource.type || 'text/plain',
      lastModified: currentTime
    },
    createdAt: currentTime,
    updatedAt: currentTime
  }));
  
  // 更新知识库
  knowledgeStore.state.knowledges = [...knowledges, ...newKnowledges];
  localStorage.setItem('knowledgeStore', JSON.stringify(knowledgeStore));
  
  console.log(`已修复 ${newKnowledges.length} 个知识库条目`);
  console.log("修复完成！请刷新页面查看结果。");
  console.log("注意：修复的条目内容为占位符，建议重新上传原始文件。");
  
} catch (error) {
  console.error("修复失败:", error);
  console.log("建议使用数据重置方案");
} 