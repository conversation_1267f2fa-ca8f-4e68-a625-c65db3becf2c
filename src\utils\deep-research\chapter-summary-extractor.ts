/**
 * 智能摘要提取器
 * 使用LLM从章节内容中提取关键信息，用于后续章节的上下文参考
 */

import { streamText } from 'ai';
import { ChapterSummary, SubSectionSummary } from './chapter-context-cache';

export interface ExtractionResult {
  keyFindings: string[];
  keyData: Record<string, any>;
  conclusions: string[];
  references: string[];
}

export class ChapterSummaryExtractor {
  
  /**
   * 从章节内容中提取关键信息
   */
  async extractChapterSummary(
    chapterNum: number,
    chapterContent: string,
    chapterTitle: string,
    modelProvider: any  // 由调用方传入已配置的模型提供者
  ): Promise<ChapterSummary> {
    try {
      const extractionPrompt = this.buildExtractionPrompt(chapterContent, chapterTitle, chapterNum);
      
      // 调用LLM进行结构化提取
      const extracted = await this.callLLMForExtraction(extractionPrompt, modelProvider);
      
      return {
        chapterNum,
        chapterTitle,
        keyFindings: extracted.keyFindings || [],
        keyData: extracted.keyData || {},
        conclusions: extracted.conclusions || [],
        references: extracted.references || [],
        generatedAt: new Date()
      };
    } catch (error) {
      console.error(`Error extracting summary for Chapter ${chapterNum}:`, error);
      
      // 返回基础摘要以避免阻塞流程
      return {
        chapterNum,
        chapterTitle,
        keyFindings: this.extractBasicFindings(chapterContent),
        keyData: {},
        conclusions: this.extractBasicConclusions(chapterContent),
        references: this.extractBasicReferences(chapterContent),
        generatedAt: new Date()
      };
    }
  }

  /**
   * 从子章节内容中提取摘要
   */
  async extractSubSectionSummary(
    sectionId: string,
    sectionTitle: string,
    sectionContent: string,
    modelProvider: any
  ): Promise<SubSectionSummary> {
    try {
      const extractionPrompt = this.buildSubSectionExtractionPrompt(sectionContent, sectionTitle);
      const extracted = await this.callLLMForExtraction(extractionPrompt, modelProvider);
      
      return {
        sectionId,
        sectionTitle,
        keyPoints: extracted.keyFindings || [],
        data: extracted.keyData || {}
      };
    } catch (error) {
      console.error(`Error extracting subsection summary for ${sectionId}:`, error);
      
      return {
        sectionId,
        sectionTitle,
        keyPoints: this.extractBasicFindings(sectionContent),
        data: {}
      };
    }
  }

  /**
   * 构建章节摘要提取的提示词
   */
  private buildExtractionPrompt(content: string, title: string, chapterNum: number): string {
    const chapterContext = this.getChapterContext(chapterNum);
    
    return `
你是一个专业的投资分析报告摘要提取专家。请从以下章节内容中提取关键信息，用于后续章节的上下文参考。

章节信息：
- 章节编号：第${chapterNum}章
- 章节标题：${title}
- 章节内容：${content}

${chapterContext}

请提取并返回以下信息的JSON格式：
{
  "keyFindings": ["关键发现1", "关键发现2", "关键发现3"],
  "keyData": {
    "重要指标名": "数值或描述",
    "重要数据点": "值"
  },
  "conclusions": ["主要结论1", "主要结论2"],
  "references": ["重要引用1", "重要引用2"]
}

提取要求：
1. keyFindings: 3-5个最重要的洞察和发现，包括新的信息点、趋势、异常情况
2. keyData: 重要的定量数据，包括财务指标、市场数据、人员数据等，保持数据的原始格式
3. conclusions: 2-3个基于数据分析得出的核心结论
4. references: 章节中提到的重要引用来源编号（如[1], [L-1]等）

请确保提取的信息：
- 精炼准确，避免冗余
- 突出数据驱动的洞察
- 便于后续章节引用和参考
- 保持客观性，避免主观判断

只返回JSON格式的结果，不要其他解释文字。
`;
  }

  /**
   * 构建子章节摘要提取的提示词
   */
  private buildSubSectionExtractionPrompt(content: string, title: string): string {
    return `
请从以下子章节内容中提取关键要点：

子章节标题：${title}
子章节内容：${content}

请提取并返回以下信息的JSON格式：
{
  "keyFindings": ["要点1", "要点2", "要点3"],
  "keyData": {
    "数据点名": "值"
  }
}

要求：
1. keyFindings: 2-4个核心要点
2. keyData: 重要的数据和指标

只返回JSON格式的结果。
`;
  }

  /**
   * 获取不同章节的专门提取重点
   */
  private getChapterContext(chapterNum: number): string {
    const contexts = {
      1: `
重点提取内容：
- 产品核心优势和创新点
- 盈利模式和营销策略
- 目标市场规模数据
- 行业竞争格局
- 战略投资和联盟情况`,
      
      2: `
重点提取内容：
- 关键财务指标（收入、利润、增长率等）
- 预算执行情况
- 营销效果数据
- 内控审计发现
- 外部环境影响`,
      
      3: `
重点提取内容：
- 团队结构和人员配置
- 管理层背景和能力
- 激励机制和稳定性
- 团队变化情况`,
      
      4: `
重点提取内容：
- 组织架构设计
- 管理制度完善情况
- 治理水平评估
- 风险管控机制`,
      
      5: `
重点提取内容：
- 发展轨迹与预期对比
- IPR和市值表现
- 重大事件影响
- 风险因素识别
- 退出机会分析`,
      
      6: `
重点提取内容：
- 综合评价结论
- 投资价值判断
- 具体投资建议
- 监控要点`
    };
    
    return contexts[chapterNum as keyof typeof contexts] || '';
  }

  /**
   * 调用LLM进行信息提取
   */
  private async callLLMForExtraction(prompt: string, modelProvider: any): Promise<ExtractionResult> {
    const result = streamText({
      model: modelProvider,
      system: `你是一个专业的投资分析报告摘要提取专家。请严格按照要求提取信息，并以JSON格式返回结果。`,
      prompt: prompt,
      temperature: 0.1, // 低温度确保稳定性
    });

    let content = '';
    for await (const part of result.fullStream) {
      if (part.type === "text-delta") {
        content += part.textDelta;
      }
    }

    // 解析JSON结果
    try {
      // 清理可能的markdown格式
      const cleanedContent = content.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanedContent);
      
      return {
        keyFindings: Array.isArray(parsed.keyFindings) ? parsed.keyFindings : [],
        keyData: parsed.keyData && typeof parsed.keyData === 'object' ? parsed.keyData : {},
        conclusions: Array.isArray(parsed.conclusions) ? parsed.conclusions : [],
        references: Array.isArray(parsed.references) ? parsed.references : []
      };
    } catch (parseError) {
      console.warn('Failed to parse LLM extraction result:', parseError);
      console.warn('Raw content:', content);
      
      // 尝试基础提取
      return this.fallbackExtraction(content);
    }
  }

  /**
   * 基础文本提取（备用方案）
   */
  private extractBasicFindings(content: string): string[] {
    const findings: string[] = [];
    
    // 查找包含关键词的句子
    const sentences = content.split(/[。！？]/);
    const keywords = ['发现', '显示', '表明', '增长', '下降', '提升', '改善', '问题', '风险', '机会'];
    
    sentences.forEach(sentence => {
      if (sentence.length > 10 && sentence.length < 200) {
        for (const keyword of keywords) {
          if (sentence.includes(keyword)) {
            findings.push(sentence.trim() + '。');
            break;
          }
        }
      }
    });
    
    return findings.slice(0, 5); // 最多5个
  }

  /**
   * 基础结论提取（备用方案）
   */
  private extractBasicConclusions(content: string): string[] {
    const conclusions: string[] = [];
    
    // 查找结论性句子
    const sentences = content.split(/[。！？]/);
    const conclusionKeywords = ['综上', '总的来说', '总体而言', '可以看出', '结论', '总结'];
    
    sentences.forEach(sentence => {
      if (sentence.length > 15 && sentence.length < 300) {
        for (const keyword of conclusionKeywords) {
          if (sentence.includes(keyword)) {
            conclusions.push(sentence.trim() + '。');
            break;
          }
        }
      }
    });
    
    return conclusions.slice(0, 3); // 最多3个
  }

  /**
   * 基础引用提取（备用方案）
   */
  private extractBasicReferences(content: string): string[] {
    const references: string[] = [];
    
    // 匹配引用格式 [数字] 或 [L-数字]
    const refPattern = /\[(?:L-)?(\d+)\]/g;
    const matches = content.match(refPattern);
    
    if (matches) {
      return [...new Set(matches)]; // 去重
    }
    
    return references;
  }

  /**
   * 备用提取方案
   */
  private fallbackExtraction(content: string): ExtractionResult {
    return {
      keyFindings: this.extractBasicFindings(content),
      keyData: {},
      conclusions: this.extractBasicConclusions(content),
      references: this.extractBasicReferences(content)
    };
  }

  /**
   * 验证提取结果的质量
   */
  validateExtractionQuality(result: ExtractionResult): {
    isValid: boolean;
    quality: 'high' | 'medium' | 'low';
    issues: string[];
  } {
    const issues: string[] = [];
    let score = 0;

    // 检查关键发现
    if (result.keyFindings.length === 0) {
      issues.push('未提取到关键发现');
    } else if (result.keyFindings.length >= 3) {
      score += 2;
    } else {
      score += 1;
    }

    // 检查关键数据
    if (Object.keys(result.keyData).length === 0) {
      issues.push('未提取到关键数据');
    } else {
      score += 2;
    }

    // 检查结论
    if (result.conclusions.length === 0) {
      issues.push('未提取到结论');
    } else if (result.conclusions.length >= 2) {
      score += 2;
    } else {
      score += 1;
    }

    // 检查内容质量
    const totalLength = result.keyFindings.join('').length + 
                       result.conclusions.join('').length;
    
    if (totalLength < 50) {
      issues.push('提取内容过短');
    } else if (totalLength > 500) {
      issues.push('提取内容过长');
    } else {
      score += 1;
    }

    const quality = score >= 6 ? 'high' : score >= 4 ? 'medium' : 'low';
    const isValid = score >= 3;

    return { isValid, quality, issues };
  }
} 