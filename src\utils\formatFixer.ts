/**
 * 轻量级格式修复器
 * 自动修正常见的格式问题
 */

import { validateFormat, FormatIssue } from './formatValidator';
import { getFormatConfig, FormatConfig } from './formatConfig';

export interface FixResult {
  content: string;
  fixedIssues: FormatIssue[];
  remainingIssues: FormatIssue[];
}

/**
 * 代码块信息
 */
interface CodeBlock {
  start: number;    // 开始行号（0基索引）
  end: number;      // 结束行号（0基索引）
  language: string; // 语言类型
  content: string;  // 原始内容
}

/**
 * 检测文本中的代码块和特殊内容块
 */
function detectCodeBlocks(lines: string[]): CodeBlock[] {
  const codeBlocks: CodeBlock[] = [];
  let inCodeBlock = false;
  let currentBlock: Partial<CodeBlock> = {};
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 检测代码块开始
    if (!inCodeBlock && line.startsWith('```')) {
      inCodeBlock = true;
      const language = line.slice(3).trim();
      currentBlock = {
        start: i,
        language: language || 'plain',
        content: lines[i]
      };
    }
    // 检测代码块结束
    else if (inCodeBlock && line === '```') {
      if (currentBlock.start !== undefined) {
        codeBlocks.push({
          start: currentBlock.start,
          end: i,
          language: currentBlock.language || 'plain',
          content: lines.slice(currentBlock.start, i + 1).join('\n')
        });
      }
      inCodeBlock = false;
      currentBlock = {};
    }
    // 代码块内容
    else if (inCodeBlock && currentBlock.start !== undefined) {
      currentBlock.content += '\n' + lines[i];
    }
  }
  
  // 处理未闭合的代码块
  if (inCodeBlock && currentBlock.start !== undefined) {
    codeBlocks.push({
      start: currentBlock.start,
      end: lines.length - 1,
      language: currentBlock.language || 'plain',
      content: lines.slice(currentBlock.start).join('\n')
    });
  }
  
  return codeBlocks;
}

/**
 * 检查指定行是否在代码块内
 */
function isLineInCodeBlock(lineIndex: number, codeBlocks: CodeBlock[]): boolean {
  return codeBlocks.some(block => 
    lineIndex >= block.start && lineIndex <= block.end
  );
}

/**
 * 自动修复报告格式问题（支持配置，适配分章节写作）
 */
export function fixFormat(content: string, options: {
  fixTitles?: boolean;
  fixBold?: boolean;
  fixSpacing?: boolean;
  fixCitations?: boolean;
  maxBoldPerParagraph?: number;
  isSubChapter?: boolean; // 新增：是否为子章节内容
  isMergedChapter?: boolean; // 新增：是否为合并后的章节
  config?: FormatConfig;
} = {}): FixResult {
  // 获取配置
  const config = options.config || getFormatConfig();
  
  // 如果格式处理被全局禁用，直接返回原内容
  if (!config.enabled) {
    return {
      content,
      fixedIssues: [],
      remainingIssues: []
    };
  }
  
  const {
    fixTitles = config.titleFormat.enabled,
    fixBold = config.boldFormat.enabled,
    fixSpacing = config.spacingFormat.enabled,
    fixCitations = config.citationFormat.enabled,
    maxBoldPerParagraph = config.boldFormat.maxPerParagraph,
    isSubChapter = false
  } = options;

  // 检测代码块
  const lines = content.split('\n');
  const codeBlocks = detectCodeBlocks(lines);
  
  let fixedContent = content;
  const fixedIssues: FormatIssue[] = [];
  
  // 1. 修复标题格式（适配分章节写作）
  if (fixTitles) {
    const titleResult = fixTitleFormat(fixedContent, config, codeBlocks, isSubChapter);
    fixedContent = titleResult.content;
    fixedIssues.push(...titleResult.fixed);
  }
  
  // 2. 修复粗体使用
  if (fixBold) {
    const boldResult = fixBoldUsage(fixedContent, maxBoldPerParagraph, config, codeBlocks);
    fixedContent = boldResult.content;
    fixedIssues.push(...boldResult.fixed);
  }
  
  // 3. 修复段落间距
  if (fixSpacing) {
    const spacingResult = fixParagraphSpacing(fixedContent, config, codeBlocks);
    fixedContent = spacingResult.content;
    fixedIssues.push(...spacingResult.fixed);
  }
  
  // 4. 修复引用格式（控制引用密度）
  if (fixCitations) {
    const citationResult = fixCitationFormat(fixedContent, config, codeBlocks, isSubChapter);
    fixedContent = citationResult.content;
    fixedIssues.push(...citationResult.fixed);
  }
  
  // 5. 移除AI生成的元信息注释
  const metaInfoResult = removeMetaInfoComments(fixedContent, codeBlocks);
  fixedContent = metaInfoResult.content;
  fixedIssues.push(...metaInfoResult.fixed);
  
  // 6. 修复错误的标题格式（将内容要点恢复为正常格式）
  const titleRevertResult = revertIncorrectTitles(fixedContent, codeBlocks);
  fixedContent = titleRevertResult.content;
  fixedIssues.push(...titleRevertResult.fixed);
  
  // 7. 最终清理：移除任何遗留的think标签内容
  const thinkTagResult = removeThinkTags(fixedContent);
  fixedContent = thinkTagResult.content;
  fixedIssues.push(...thinkTagResult.fixed);
  
  // 验证剩余问题
  const remainingIssues = validateFormat(fixedContent);
  
  // 输出日志
  if (config.logging.enabled) {
    logFormatResults(fixedIssues, remainingIssues, config);
  }
  
  return {
    content: fixedContent,
    fixedIssues,
    remainingIssues
  };
}

/**
 * 输出格式处理日志
 */
function logFormatResults(_fixedIssues: FormatIssue[], _remainingIssues: FormatIssue[], _config: FormatConfig) {
  // 格式处理结果日志已清理
}

/**
 * 修复标题格式（适配分章节写作）
 */
function fixTitleFormat(content: string, config: FormatConfig, codeBlocks: CodeBlock[] = [], isSubChapter: boolean = false): { content: string; fixed: FormatIssue[] } {
  const lines = content.split('\n');
  const fixed: FormatIssue[] = [];
  
  // 章节标题映射
  const chapterMappings = [
    { pattern: /项目公司.*商业模式/, replacement: '一、项目公司的商业模式' },
    { pattern: /项目公司.*经营状况/, replacement: '二、项目公司的经营状况' },
    { pattern: /项目公司.*管理团队/, replacement: '三、项目公司的管理团队' },
    { pattern: /项目公司.*治理结构/, replacement: '四、项目公司的治理结构' },
    { pattern: /项目公司.*发展情况.*投资回报/, replacement: '五、项目公司的发展情况与投资回报' },
    { pattern: /总结.*评价/, replacement: '六、总结与评价' }
  ];
  
  for (let i = 0; i < lines.length; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      continue;
    }
    
    let line = lines[i];
    const originalLine = line;
    
    // 修复主章节标题
    if (config.titleFormat.fixChapterTitles) {
      for (const mapping of chapterMappings) {
        if (mapping.pattern.test(line) && !line.startsWith('一、') && !line.startsWith('二、') && 
            !line.startsWith('三、') && !line.startsWith('四、') && !line.startsWith('五、') && !line.startsWith('六、')) {
          // 完全替换整行内容，避免重复前缀
          const newLine = line.trim().replace(/^#+\s*/, '').replace(/^\*\*|\*\*$/g, '');
          if (mapping.pattern.test(newLine)) {
            line = mapping.replacement;
            if (line !== originalLine) {
              fixed.push({
                type: 'title',
                line: i + 1,
                content: originalLine,
                expected: line,
                severity: 'error'
              });
            }
          }
        }
      }
    }
    
    // 修复子标题格式
    if (config.titleFormat.fixSubTitles) {
      // 修复子标题格式（确保使用中文顿号）
      if (line.match(/^(\d+)[\.。]\s/)) {
        const newLine = line.replace(/^(\d+)[\.。]\s/, '$1、');
        if (newLine !== line) {
          fixed.push({
            type: 'title',
            line: i + 1,
            content: line,
            expected: newLine,
            severity: 'warning'
          });
          line = newLine;
        }
      }
      
      // 修复二级子标题格式
      if (line.match(/^(\d+)\.(\d+)[\.。]\s/)) {
        const newLine = line.replace(/^(\d+)\.(\d+)[\.。]\s/, '$1.$2、');
        if (newLine !== line) {
          fixed.push({
            type: 'title',
            line: i + 1,
            content: line,
            expected: newLine,
            severity: 'warning'
          });
          line = newLine;
        }
      }
    }
    
    // 统一标题格式：确保标题使用正确的Markdown格式和粗体（适配分章节模式）
    const updatedLine = unifyTitleFormat(line, fixed, i + 1, isSubChapter);
    if (updatedLine !== line) {
      line = updatedLine;
    }
    
    lines[i] = line;
  }
  
  return {
    content: lines.join('\n'),
    fixed
  };
}

/**
 * 统一标题格式（适配分章节写作）
 */
function unifyTitleFormat(line: string, fixed: FormatIssue[], lineNumber: number, isSubChapter: boolean = false): string {
  let updatedLine = line;
  const trimmedLine = line.trim();
  
  // 针对不同写作模式使用不同的标题格式策略
  if (isSubChapter) {
    // 子章节写作模式：使用子章节标题格式规范
    
    // 检测三级标题（如 1.2.1、盈利模式剖析）- 优先处理
    const thirdLevelPattern = /^(#+\s*)?(\*\*)?(\d+\.\d+\.\d+)、(.+?)(\*\*)?$/;
    const thirdMatch = trimmedLine.match(thirdLevelPattern);
    
    // 检查是否是真正的三级标题
    const isThirdLevelTitle = thirdMatch && 
                             trimmedLine.length <= 50 && // 标题通常较短
                             !(/[：。！？].*[：。！？]/.test(trimmedLine)) && // 不包含多个句号
                             !/^(\d+\.\d+\.\d+)、[^：。！？]*?[：。！？]/.test(trimmedLine) && // 不是以句号等结尾的描述性内容
                             // 排除以**开头的内容要点（最优先判断）
                             !trimmedLine.startsWith('**') &&
                             // 排除原始内容包含**编号格式
                             !/^\*\*\d+/.test(trimmedLine) &&
                             // 排除具体的业务描述内容
                             !/直销|网络|架构|营销|组合|客户|产品|服务|渠道|策略|方案|技术|实现|标准|制定|任职|周期|变动|特征|团队|高管/.test(trimmedLine);
    
    if (isThirdLevelTitle) {
      const titleText = `${thirdMatch[3]}、${thirdMatch[4]}`;
      const newFormat = `#### **${titleText}**`;
      
      if (updatedLine.trim() !== newFormat) {
        updatedLine = newFormat;
        fixed.push({
          type: 'title',
          line: lineNumber,
          content: line,
          expected: '统一子章节三级标题格式为 #### **X.X.X、标题**',
          severity: 'info'
        });
      }
      return updatedLine;
    }
    
    // 检测二级标题（如 1.2、产品创新性分析）
    const secondLevelPattern = /^(#+\s*)?(\*\*)?(\d+\.\d+)、(.+?)(\*\*)?$/;
    const secondMatch = trimmedLine.match(secondLevelPattern);
    
    // 检查是否是真正的二级标题而不是内容要点
    const isSecondLevelTitle = secondMatch && 
                              trimmedLine.length <= 50 && // 标题通常较短
                              !(/[：。！？].*[：。！？]/.test(trimmedLine)) && // 不包含多个句号
                              !/^(\d+\.\d+)、[^：。！？]*?[：。！？]/.test(trimmedLine) && // 不是以句号等结尾的描述性内容
                              // 排除以**开头的内容要点（最优先判断）
                              !trimmedLine.startsWith('**') &&
                              // 排除原始内容包含**编号格式
                              !/^\*\*\d+/.test(trimmedLine) &&
                              // 排除具体的业务描述内容
                              !/直销|网络|架构|营销|组合|客户|产品|服务|渠道|策略|方案|技术|实现|标准|制定|任职|周期|变动|特征|团队|高管/.test(trimmedLine) &&
                              // 必须包含章节关键词
                              /项目|公司|商业|经营|管理|团队|治理|发展|总结|评价|分析|模式|状况|结构|情况|创新|领先|优势|盈利|市场|行业/.test(trimmedLine);
    
    if (isSecondLevelTitle) {
      const titleText = `${secondMatch[3]}、${secondMatch[4]}`;
      const newFormat = `### **${titleText}**`;
      
      if (updatedLine.trim() !== newFormat) {
        updatedLine = newFormat;
        fixed.push({
          type: 'title',
          line: lineNumber,
          content: line,
          expected: '统一子章节二级标题格式为 ### **X.X、标题**',
          severity: 'info'
        });
      }
      return updatedLine;
    }
    
  } else {
    // 合并章节或完整报告模式：使用传统格式
    
    // 检测是否为大章节标题（一、二、三、四、五、六）
    const chapterTitlePattern = /^(#+\s*)?(一、|二、|三、|四、|五、|六、)/;
    const chapterMatch = trimmedLine.match(chapterTitlePattern);
    
    if (chapterMatch) {
      // 大章节标题：统一为 ## **标题** 格式
      const titleText = trimmedLine.replace(/^#+\s*/, '').replace(/^\*\*|\*\*$/g, '');
      const newFormat = `## **${titleText}**`;
      
      if (updatedLine.trim() !== newFormat) {
        updatedLine = newFormat;
        fixed.push({
          type: 'title',
          line: lineNumber,
          content: line,
          expected: '统一大章节标题格式为 ## **标题**',
          severity: 'info'
        });
      }
      return updatedLine;
    }
    
    // 检测是否为子章节标题（严格匹配真正的章节标题）
    // 只匹配符合章节编号规范的标题，排除内容中的编号要点
    const subChapterPattern = /^(#+\s*)?(\*\*)?(\d+、|\d+\.\d+、|\d+\.\d+\.\d+、)([^：。！？,，.!?]*?)(\*\*)?[：。]?\s*$/;
    const subChapterMatch = trimmedLine.match(subChapterPattern);
    
    if (subChapterMatch) {
      const numberPart = subChapterMatch[3]; // 提取编号部分
      
      // 严格的章节标题判断条件
      const isRealChapterTitle = 
        // 1. 必须是多级编号（如 1.2、1.2.3）或者是在文档开头的单级编号
        (/\d+\.\d+/.test(numberPart)) ||
        // 2. 如果是单级编号，必须包含特定的章节关键词
        (/^(\d+)、$/.test(numberPart) && /项目|公司|商业|经营|管理|团队|治理|发展|总结|评价|分析|模式|状况|结构|情况/.test(trimmedLine)) ||
        // 3. 标题长度在合理范围内（真正的标题通常较短且专业）
        (trimmedLine.length <= 30 && /项目|公司|商业|经营|管理|团队|治理|发展|总结|评价|分析|模式|状况|结构|情况/.test(trimmedLine));
      
      // 排除内容中的编号要点（这些不应该被识别为标题）
      const isContentItem = 
        // 1. 以**开头的绝对是内容要点，不是章节标题（最优先判断）
        trimmedLine.startsWith('**') ||
        // 2. 包含具体的业务描述词汇
        /直销|网络|架构|营销|组合|客户|产品|服务|渠道|策略|方案|技术|实现|标准|制定|任职|周期|变动|特征|团队|高管/.test(trimmedLine) ||
        // 3. 单纯的编号+具体事物名称（如"1、直销网络架构"）
        (/^(\d+)、[^分析评估总结].*[架构|网络|组合|方案|策略|技术|标准|任职|周期|变动|特征|团队|高管]/.test(trimmedLine)) ||
        // 4. 原始内容如果包含**编号格式（如**1、内容），也排除
        /^\*\*\d+、/.test(trimmedLine);
      
      if (isRealChapterTitle && !isContentItem) {
        // 根据编号级别决定标题格式
        const titleText = trimmedLine.replace(/^#+\s*/, '').replace(/^\*\*|\*\*$/g, '');
        let newFormat: string;
        let expectedDesc: string;
        
        // 判断编号级别并应用对应格式
        if (/\d+\.\d+\.\d+、/.test(numberPart)) {
          // 三级编号：1.1.1、1.1.2、1.1.3等
          newFormat = `#### **${titleText}**`;
          expectedDesc = '统一三级标题格式为 #### **X.X.X、标题**';
        } else if (/\d+\.\d+、/.test(numberPart)) {
          // 二级编号：1.1、1.2、1.3等
          newFormat = `### **${titleText}**`;
          expectedDesc = '统一二级标题格式为 ### **X.X、标题**';
        } else {
          // 一级编号：1、2、3等
          newFormat = `### **${titleText}**`;
          expectedDesc = '统一一级标题格式为 ### **X、标题**';
        }
        
        if (updatedLine.trim() !== newFormat) {
          updatedLine = newFormat;
          fixed.push({
            type: 'title',
            line: lineNumber,
            content: line,
            expected: expectedDesc,
            severity: 'info'
          });
        }
        return updatedLine;
      }
    }
  }
  
  return updatedLine;
}

/**
 * 修复粗体使用
 */
function fixBoldUsage(content: string, maxBoldPerParagraph: number, config: FormatConfig, codeBlocks: CodeBlock[] = []): { content: string; fixed: FormatIssue[] } {
  const lines = content.split('\n');
  const fixed: FormatIssue[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      continue;
    }
    
    let line = lines[i];
    const originalLine = line;
    
    // 修复单独的星号为双星号
    if (config.boldFormat.fixIncomplete) {
      line = line.replace(/(?<!\*)\*(?!\*)([^*]+?)(?<!\*)\*(?!\*)/g, '**$1**');
    }
    
    // 如果粗体太多，保留前N个最重要的
    const boldMatches = line.match(/\*\*([^*]+?)\*\*/g);
    if (boldMatches && boldMatches.length > maxBoldPerParagraph) {
      // 按长度和重要性排序，保留最重要的
      const sortedBolds = boldMatches
        .map(match => ({
          text: match,
          content: match.replace(/\*\*/g, ''),
          priority: calculateBoldPriority(match.replace(/\*\*/g, ''))
        }))
        .sort((a, b) => b.priority - a.priority)
        .slice(0, maxBoldPerParagraph);
      
      // 移除多余的粗体
      let newLine = line;
      boldMatches.forEach(bold => {
        if (!sortedBolds.some(sb => sb.text === bold)) {
          newLine = newLine.replace(bold, bold.replace(/\*\*/g, ''));
        }
      });
      
      if (newLine !== line) {
        fixed.push({
          type: 'bold',
          line: i + 1,
          content: originalLine,
          expected: `减少粗体使用，保留${maxBoldPerParagraph}个最重要的`,
          severity: 'warning'
        });
        line = newLine;
      }
    }
    
    if (line !== originalLine && config.boldFormat.fixIncomplete) {
      // 只在修复不完整粗体时添加日志
      const fixedIncomplete = line.replace(/(?<!\*)\*(?!\*)([^*]+?)(?<!\*)\*(?!\*)/g, '**$1**');
      if (fixedIncomplete === line) {
        fixed.push({
          type: 'bold',
          line: i + 1,
          content: originalLine,
          expected: '修复不完整的粗体格式',
          severity: 'info'
        });
      }
    }
    
    lines[i] = line;
  }
  
  return {
    content: lines.join('\n'),
    fixed
  };
}

/**
 * 计算粗体内容的重要性权重
 */
function calculateBoldPriority(text: string): number {
  let priority = 0;
  
  // 数字和百分比权重较高
  if (/\d+%/.test(text)) priority += 10;
  if (/\d+/.test(text)) priority += 5;
  
  // 关键词权重
  const keywords = ['增长', '下降', '亿元', '万元', '核心', '重要', '关键', '主要', '显著'];
  keywords.forEach(keyword => {
    if (text.includes(keyword)) priority += 3;
  });
  
  // 长度权重（适中长度更重要）
  if (text.length >= 4 && text.length <= 10) priority += 2;
  
  return priority;
}

/**
 * 修复段落间距
 */
function fixParagraphSpacing(content: string, config: FormatConfig, codeBlocks: CodeBlock[] = []): { content: string; fixed: FormatIssue[] } {
  const lines = content.split('\n');
  const fixed: FormatIssue[] = [];
  const newLines: string[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const currentLine = lines[i].trim();
    const nextLine = i < lines.length - 1 ? lines[i + 1].trim() : '';
    
    newLines.push(lines[i]);
    
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      continue;
    }
    
    // 检查是否需要添加空行
    if (config.spacingFormat.addAfterLongParagraphs && shouldAddSpacing(currentLine, nextLine, config)) {
      // 检查下一行是否已经是空行或在代码块内
      if (nextLine !== '' && !isLineInCodeBlock(i + 1, codeBlocks)) {
        newLines.push('');
        fixed.push({
          type: 'spacing',
          line: i + 1,
          content: currentLine,
          expected: '在长段落后添加空行',
          severity: 'info'
        });
      }
    }
  }
  
  return {
    content: newLines.join('\n'),
    fixed
  };
}

/**
 * 移除任何遗留的think标签内容
 */
export function removeThinkTags(content: string): { content: string; fixed: FormatIssue[] } {
  const fixed: FormatIssue[] = [];
  let cleanedContent = content;
  
  // 正则表达式匹配完整的<think></think>块（包括跨行）
  const thinkTagRegex = /<think>[\s\S]*?<\/think>/gi;
  
  // 找到所有匹配的think标签
  const matches = cleanedContent.match(thinkTagRegex);
  
  if (matches && matches.length > 0) {
    // 移除所有think标签及其内容
    cleanedContent = cleanedContent.replace(thinkTagRegex, '');
    
    // 清理多余的空行（think标签移除后可能留下的空行）
    cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // 记录修复信息
    matches.forEach((match) => {
      // 计算大概的行号（简化处理）
      const contentBeforeMatch = content.substring(0, content.indexOf(match));
      const lineNumber = (contentBeforeMatch.match(/\n/g) || []).length + 1;
      
      fixed.push({
        type: 'meta-info',
        line: lineNumber,
        content: match.length > 100 ? match.substring(0, 100) + '...' : match,
        expected: '移除think标签内容',
        severity: 'warning'
      });
    });
  }
  
  // 同时处理可能残留的单独标签
  const orphanStartTags = cleanedContent.match(/<think>/gi);
  const orphanEndTags = cleanedContent.match(/<\/think>/gi);
  
  if (orphanStartTags || orphanEndTags) {
    // 移除孤立的开始和结束标签
    cleanedContent = cleanedContent.replace(/<think>/gi, '');
    cleanedContent = cleanedContent.replace(/<\/think>/gi, '');
    
    if (orphanStartTags) {
      orphanStartTags.forEach(() => {
        fixed.push({
          type: 'meta-info',
          line: 0, // 无法准确定位行号时使用0
          content: '<think>',
          expected: '移除孤立的think开始标签',
          severity: 'info'
        });
      });
    }
    
    if (orphanEndTags) {
      orphanEndTags.forEach(() => {
        fixed.push({
          type: 'meta-info',
          line: 0, // 无法准确定位行号时使用0
          content: '</think>',
          expected: '移除孤立的think结束标签',
          severity: 'info'
        });
      });
    }
  }
  
  return {
    content: cleanedContent,
    fixed
  };
}

/**
 * 判断是否应该添加段落间距
 */
function shouldAddSpacing(currentLine: string, nextLine: string, config: FormatConfig): boolean {
  if (!currentLine || !nextLine) return false;
  
  // 标题后不需要额外空行
  if (currentLine.startsWith('#') || nextLine.startsWith('#')) return false;
  
  // 列表项之间不需要空行
  const isCurrentList = currentLine.startsWith('-') || currentLine.match(/^\d+\./);
  const isNextList = nextLine.startsWith('-') || nextLine.match(/^\d+\./);
  if (isCurrentList || isNextList) return false;
  
  // 根据配置的最小长度判断
  return currentLine.length > config.spacingFormat.minParagraphLength && 
         nextLine.length > config.spacingFormat.minParagraphLength;
}

/**
 * 修复引用格式（控制引用密度）
 */
function fixCitationFormat(content: string, config: FormatConfig, codeBlocks: CodeBlock[] = [], isSubChapter: boolean = false): { content: string; fixed: FormatIssue[] } {
  const lines = content.split('\n');
  const fixed: FormatIssue[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      continue;
    }
    
    let line = lines[i];
    const originalLine = line;
    
    // 🔥 移除引用格式处理，交给 processLocalResourceCitations 专门处理
    // 避免功能重复和冲突
    if (config.citationFormat.fixBrackets) {
      // 只处理非本地资源的引用格式错误
      // (1) -> [1]，但不处理 L- 格式
      line = line.replace(/\((\d+)\)(?!\()/g, '[$1]');
      
      // 【1】-> [1]，但不处理 L- 格式
      line = line.replace(/【(\d+)】(?!\()/g, '[$1]');
      
      // 🚫 不处理 L- 格式的引用，交给 processLocalResourceCitations 处理
    }
    
    if (config.citationFormat.mergeConsecutive) {
      // 只合并网络引用，不处理本地资源引用
      // [1] [2] -> [1][2]
      line = line.replace(/\[(\d+)\]\s+\[(\d+)\]/g, '[$1][$2]');
      
      // 🚫 不处理 L- 格式的合并，交给 processLocalResourceCitations 处理
    }
    
    // 控制引用密度（每段最多2-3个引用）
    if (isSubChapter) {
      // 分别匹配网络引用和本地资源引用
      const webCitationMatches = line.match(/\[\d+\]/g) || [];
      const localCitationMatches = line.match(/\[L-\d+\]/g) || [];
      const totalCitations = webCitationMatches.length + localCitationMatches.length;
      
      if (totalCitations > 3) {
        // 保留前3个引用，移除多余的
        let citationCount = 0;
        
        // 先处理网络引用
        line = line.replace(/\[(\d+)\]/g, (match) => {
          citationCount++;
          if (citationCount <= 3) {
            return match; // 保持原格式
          } else {
            return ''; // 移除多余的引用
          }
        });
        
        // 再处理本地资源引用
        line = line.replace(/\[L-(\d+)\]/g, (match) => {
          citationCount++;
          if (citationCount <= 3) {
            return match; // 保持原格式
          } else {
            return ''; // 移除多余的引用
          }
        });
        
        if (totalCitations > 3) {
          fixed.push({
            type: 'citation',
            line: i + 1,
            content: originalLine,
            expected: `控制引用密度，移除超过3个的引用`,
            severity: 'info'
          });
        }
      }
    }
    
    if (line !== originalLine) {
      fixed.push({
        type: 'citation',
        line: i + 1,
        content: originalLine,
        expected: '修复引用格式',
        severity: 'warning'
      });
    }
    
    lines[i] = line;
  }
  
  return {
    content: lines.join('\n'),
    fixed
  };
}

/**
 * 修复错误的标题格式（将内容要点恢复为正常格式）
 */
function revertIncorrectTitles(content: string, codeBlocks: CodeBlock[] = []): { content: string; fixed: FormatIssue[] } {
  const lines = content.split('\n');
  const fixed: FormatIssue[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      continue;
    }
    
    let line = lines[i];
    const originalLine = line;
    const trimmedLine = line.trim();
    
    // 检测错误格式化的内容要点（被错误地转换为标题格式的）
    const incorrectTitlePattern = /^(#{1,4})\s*\*\*(\d+)、([^*]+)\*\*\s*$/;
    const match = trimmedLine.match(incorrectTitlePattern);
    
    if (match) {
      const number = match[2];
      const content = match[3];
      const fullContent = `${number}、${content}`;
      
      // 判断这是否应该是内容要点而不是标题
      const shouldBeContentItem = 
        // 包含具体的业务描述词汇
        /直销|网络|架构|营销|组合|客户|产品|服务|渠道|策略|方案|技术|实现|标准|制定|解决|措施|建议|要求|规范|流程|机制/.test(fullContent) ||
        // 或者是纯数字编号的要点（1、2、3、4...）
        /^[1-9]、/.test(fullContent);
      
      if (shouldBeContentItem) {
        // 恢复为正常的要点格式
        line = `**${number}、${content}**`;
        
        fixed.push({
          type: 'title',
          line: i + 1,
          content: originalLine,
          expected: '将错误的标题格式恢复为内容要点',
          severity: 'info'
        });
      }
    }
    
    lines[i] = line;
  }
  
  return {
    content: lines.join('\n'),
    fixed
  };
}

/**
 * 移除AI生成的元信息注释
 */
function removeMetaInfoComments(content: string, codeBlocks: CodeBlock[] = []): { content: string; fixed: FormatIssue[] } {
  const lines = content.split('\n');
  const fixed: FormatIssue[] = [];
  const newLines: string[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      newLines.push(lines[i]);
      continue;
    }
    
    const line = lines[i].trim();
    
    // 检测并移除AI生成的元信息注释
    const metaInfoPatterns = [
      /^（注：.*?）\s*$/,                           // （注：全文严格遵循格式要求，字数控制在3,800字，...）
      /^（本节共计.*?）\s*$/,                       // （本节共计3,278字，严格控制在指定字数范围，符合深度分析要求）
      /^（本小节共计.*?）\s*$/,                     // （本小节共计3760字，严格控制在规定字数范围）
      /^（本节字数：.*?）\s*$/,                     // （本节字数：2,387）
      /^（全文.*?字.*?满足.*?）\s*$/,               // （全文3,278字，满足格式及引用规范）
      /^（注：全文严格控制在.*?）\s*$/,             // （注：全文严格控制在3,800字，数据均来自企业年报及行业数据库CINNO/DSCC）
      /^（注：本节严格控制在.*?）\s*$/,             // （注：本节严格控制在3870字，所有数据均来自企业年报及公告文件，影响评估采用蒙特卡洛模拟量化分析）
      /^（.*?字数控制.*?）\s*$/,                    // 任何包含"字数控制"的注释
      /^（.*?严格控制.*?）\s*$/,                    // 任何包含"严格控制"的注释
      /^（.*?共计.*?字.*?）\s*$/,                   // 任何包含"共计X字"的注释
      /^（.*?格式要求.*?）\s*$/,                    // 任何包含"格式要求"的注释
      /^（.*?数据均来自.*?）\s*$/,                  // 任何包含"数据均来自"的注释
    ];
    
    let shouldRemove = false;
    for (const pattern of metaInfoPatterns) {
      if (pattern.test(line)) {
        shouldRemove = true;
        fixed.push({
          type: 'meta-info',
          line: i + 1,
          content: lines[i],
          expected: '移除AI生成的元信息注释',
          severity: 'info'
        });
        break;
      }
    }
    
    // 如果不是元信息注释，保留这一行
    if (!shouldRemove) {
      newLines.push(lines[i]);
    }
  }
  
  return {
    content: newLines.join('\n'),
    fixed
  };
}