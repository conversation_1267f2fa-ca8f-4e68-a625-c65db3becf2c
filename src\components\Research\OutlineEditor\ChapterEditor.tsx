"use client";
import { useState } from "react";

import { Save, X, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { OutlineChapter } from "@/types/outline";

interface ChapterEditorProps {
  chapter?: OutlineChapter;
  onSave: (chapter: Omit<OutlineChapter, 'id'>) => void;
  onCancel: () => void;
}

export default function ChapterEditor({ chapter, onSave, onCancel }: ChapterEditorProps) {
  const [formData, setFormData] = useState({
    number: chapter?.number || 1,
    title: chapter?.title || '',
    description: chapter?.description || '',
    wordCount: {
      min: chapter?.wordCount.min || 3000,
      max: chapter?.wordCount.max || 4000,
    },
    researchPoints: chapter?.researchPoints || [''],
    enabled: chapter?.enabled ?? true,
    priority: chapter?.priority || 'medium' as const,
    sections: chapter?.sections || [],
  });

  const handleResearchPointChange = (index: number, value: string) => {
    const newPoints = [...formData.researchPoints];
    newPoints[index] = value;
    setFormData({ ...formData, researchPoints: newPoints });
  };

  const handleAddResearchPoint = () => {
    setFormData({
      ...formData,
      researchPoints: [...formData.researchPoints, '']
    });
  };

  const handleRemoveResearchPoint = (index: number) => {
    const newPoints = formData.researchPoints.filter((_, i) => i !== index);
    setFormData({ ...formData, researchPoints: newPoints });
  };

  const handleSave = () => {
    const cleanedPoints = formData.researchPoints.filter(point => point.trim() !== '');
    
    onSave({
      number: formData.number,
      title: formData.title.trim(),
      description: formData.description.trim(),
      wordCount: formData.wordCount,
      researchPoints: cleanedPoints,
      enabled: formData.enabled,
      priority: formData.priority,
      sections: formData.sections,
    });
  };

  const isValid = formData.title.trim() !== '' && 
                  formData.wordCount.min > 0 && 
                  formData.wordCount.max > formData.wordCount.min;

  return (
    <div className="space-y-4">
      {/* 基本信息 */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">章节编号</label>
          <Input
            type="number"
            value={formData.number}
            onChange={(e) => setFormData({ ...formData, number: parseInt(e.target.value) || 1 })}
            min={1}
          />
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">优先级</label>
          <Select
            value={formData.priority}
            onValueChange={(value: 'high' | 'medium' | 'low') => 
              setFormData({ ...formData, priority: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="medium">中</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 标题 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">章节标题 *</label>
        <Input
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          placeholder="输入章节标题"
        />
      </div>

      {/* 描述 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">章节描述</label>
        <Textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="输入章节描述"
          rows={3}
        />
      </div>

      {/* 字数范围 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">字数范围</label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Input
              type="number"
              value={formData.wordCount.min}
              onChange={(e) => setFormData({
                ...formData,
                wordCount: { ...formData.wordCount, min: parseInt(e.target.value) || 0 }
              })}
              placeholder="最小字数"
              min={0}
            />
          </div>
          <div>
            <Input
              type="number"
              value={formData.wordCount.max}
              onChange={(e) => setFormData({
                ...formData,
                wordCount: { ...formData.wordCount, max: parseInt(e.target.value) || 0 }
              })}
              placeholder="最大字数"
              min={formData.wordCount.min}
            />
          </div>
        </div>
      </div>

      {/* 研究要点 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">研究要点</label>
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddResearchPoint}
          >
            <Plus className="w-4 h-4 mr-1" />
            添加要点
          </Button>
        </div>
        
        <div className="space-y-2">
          {formData.researchPoints.map((point, index) => (
            <div key={index} className="flex items-center space-x-2">
              <Input
                value={point}
                onChange={(e) => handleResearchPointChange(index, e.target.value)}
                placeholder={`研究要点 ${index + 1}`}
              />
              {formData.researchPoints.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveResearchPoint(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center space-x-2 pt-4 border-t">
        <Button
          onClick={handleSave}
          disabled={!isValid}
        >
          <Save className="w-4 h-4 mr-1" />
          保存
        </Button>
        <Button
          variant="outline"
          onClick={onCancel}
        >
          <X className="w-4 h-4 mr-1" />
          取消
        </Button>
      </div>
    </div>
  );
} 