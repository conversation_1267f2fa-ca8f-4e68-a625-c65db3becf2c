/**
 * 轻量级格式验证器
 * 检测报告中的常见格式问题
 */

export interface FormatIssue {
  type: 'title' | 'bold' | 'spacing' | 'citation' | 'meta-info';
  line: number;
  content: string;
  expected: string;
  severity: 'error' | 'warning' | 'info';
}

/**
 * 代码块信息
 */
interface CodeBlock {
  start: number;    // 开始行号（0基索引）
  end: number;      // 结束行号（0基索引）
  language: string; // 语言类型
}

/**
 * 检测文本中的代码块
 */
function detectCodeBlocks(lines: string[]): CodeBlock[] {
  const codeBlocks: CodeBlock[] = [];
  let inCodeBlock = false;
  let currentStart = -1;
  let currentLanguage = '';
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 检测代码块开始
    if (!inCodeBlock && line.startsWith('```')) {
      inCodeBlock = true;
      currentStart = i;
      currentLanguage = line.slice(3).trim() || 'plain';
    }
    // 检测代码块结束
    else if (inCodeBlock && line === '```') {
      if (currentStart !== -1) {
        codeBlocks.push({
          start: currentStart,
          end: i,
          language: currentLanguage
        });
      }
      inCodeBlock = false;
      currentStart = -1;
      currentLanguage = '';
    }
  }
  
  // 处理未闭合的代码块
  if (inCodeBlock && currentStart !== -1) {
    codeBlocks.push({
      start: currentStart,
      end: lines.length - 1,
      language: currentLanguage
    });
  }
  
  return codeBlocks;
}

/**
 * 检查指定行是否在代码块内
 */
function isLineInCodeBlock(lineIndex: number, codeBlocks: CodeBlock[]): boolean {
  return codeBlocks.some(block => 
    lineIndex >= block.start && lineIndex <= block.end
  );
}

/**
 * 验证报告格式，返回发现的问题列表
 */
export function validateFormat(content: string): FormatIssue[] {
  const issues: FormatIssue[] = [];
  const lines = content.split('\n');
  
  // 检测代码块
  const codeBlocks = detectCodeBlocks(lines);

  for (let i = 0; i < lines.length; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks)) {
      continue;
    }
    
    const line = lines[i];
    const lineNumber = i + 1;

    // 检查标题格式
    validateTitleFormat(line, lineNumber, issues);
    
    // 检查粗体使用
    validateBoldUsage(line, lineNumber, issues);
    
    // 检查引用格式
    validateCitationFormat(line, lineNumber, issues);
  }

  // 检查段落间距（跳过代码块）
  validateParagraphSpacing(lines, issues, codeBlocks);

  return issues;
}

/**
 * 验证标题格式
 */
function validateTitleFormat(line: string, lineNumber: number, issues: FormatIssue[]) {
  // 检查中文数字标题格式
  const chineseNumberPattern = /^(一、|二、|三、|四、|五、|六、)/;
  const arabicSubPattern = /^(\d+、|\d+\.\d+、|\d+\.\d+\.\d+、)/;
  
  // 如果是主标题但格式不正确
  if (line.includes('项目公司') && line.includes('商业模式') && !chineseNumberPattern.test(line)) {
    issues.push({
      type: 'title',
      line: lineNumber,
      content: line,
      expected: '应使用"一、"格式开头',
      severity: 'error'
    });
  }
  
  // 检查子标题格式
  if (line.match(/^\d+[、\.]\s/) && !arabicSubPattern.test(line)) {
    issues.push({
      type: 'title',
      line: lineNumber,
      content: line,
      expected: '子标题应使用"1、"或"1.1、"格式',
      severity: 'warning'
    });
  }
}

/**
 * 验证粗体使用
 */
function validateBoldUsage(line: string, lineNumber: number, issues: FormatIssue[]) {
  const boldMatches = line.match(/\*\*([^*]+)\*\*/g);
  if (boldMatches && boldMatches.length > 2) {
    issues.push({
      type: 'bold',
      line: lineNumber,
      content: line,
      expected: '每段粗体不应超过2个',
      severity: 'warning'
    });
  }
  
  // 检查是否有单独的星号（可能是格式错误）
  if (line.includes('*') && !line.includes('**')) {
    const singleStars = line.match(/(?<!\*)\*(?!\*)/g);
    if (singleStars && singleStars.length > 0) {
      issues.push({
        type: 'bold',
        line: lineNumber,
        content: line,
        expected: '可能存在不完整的粗体格式',
        severity: 'info'
      });
    }
  }
}

/**
 * 验证引用格式
 */
function validateCitationFormat(line: string, lineNumber: number, issues: FormatIssue[]) {
  // 检查引用格式 [数字] 或 [L-数字]
  const citationPattern = /\[(\d+|L-\d+)\]/g;
  const invalidCitations = line.match(/\[[^\]]*\]/g);
  
  if (invalidCitations) {
    invalidCitations.forEach(citation => {
      if (!citationPattern.test(citation)) {
        issues.push({
          type: 'citation',
          line: lineNumber,
          content: citation,
          expected: '引用格式应为[1]或[L-1]',
          severity: 'warning'
        });
      }
    });
  }
}

/**
 * 验证段落间距
 */
function validateParagraphSpacing(lines: string[], issues: FormatIssue[], codeBlocks: CodeBlock[]) {
  for (let i = 0; i < lines.length - 1; i++) {
    // 跳过代码块内的行
    if (isLineInCodeBlock(i, codeBlocks) || isLineInCodeBlock(i + 1, codeBlocks)) {
      continue;
    }
    
    const currentLine = lines[i].trim();
    const nextLine = lines[i + 1].trim();
    
    // 如果当前行有内容，下一行也有内容，但中间没有空行
    if (currentLine && nextLine && !currentLine.startsWith('#') && !nextLine.startsWith('#')) {
      // 检查是否是列表项
      const isCurrentList = currentLine.startsWith('-') || currentLine.match(/^\d+\./);
      const isNextList = nextLine.startsWith('-') || nextLine.match(/^\d+\./);
      
      // 如果都不是列表项，且内容较长，建议添加空行
      if (!isCurrentList && !isNextList && currentLine.length > 50 && nextLine.length > 50) {
        issues.push({
          type: 'spacing',
          line: i + 1,
          content: currentLine,
          expected: '建议在段落间添加空行提高可读性',
          severity: 'info'
        });
      }
    }
  }
} 