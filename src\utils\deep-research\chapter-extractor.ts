"use client";

/**
 * 章节提取器 - 使用DOM数据属性提取完整章节内容
 */
export class ChapterExtractor {
  
  /**
   * 从DOM中提取章节内容
   * @param chapterId 章节ID
   * @param chapterTitle 章节标题
   * @returns 完整的章节内容
   */
  static extractChapterContentFromDOM(chapterId: string, chapterTitle: string): string {
    if (typeof document === 'undefined') {
      console.error('DOM不可用，无法提取章节内容');
      return '';
    }

    console.log(`🔍 开始从DOM提取章节内容: "${chapterTitle}" (ID: ${chapterId})`);

    try {
      // 尝试多种方式查找章节标题元素
      const chapterTitleElement = this.findChapterTitleElement(chapterId, chapterTitle);

      if (!chapterTitleElement) {
        console.error(`❌ 无法在DOM中找到章节标题元素: "${chapterTitle}"`);
        return '';
      }

      console.log(`✅ 找到章节标题元素:`, chapterTitleElement);

      // 获取章节类型和级别
      const chapterType = chapterTitleElement.getAttribute('data-chapter-type') || 'sub';
      const chapterLevel = parseInt(chapterTitleElement.getAttribute('data-chapter-level') || '0') || 3;
      
      console.log(`📊 章节信息: 类型=${chapterType}, 级别=${chapterLevel}`);

      // 提取章节内容 - 从当前标题到下一个同级或更高级标题之间的所有内容
      const content = this.extractContentBetweenHeadings(chapterTitleElement, chapterLevel, chapterType);
      
      console.log(`📝 提取到的内容长度: ${content.length} 字符`);
      return content;
    } catch (error) {
      console.error('提取章节内容时出错:', error);
      return '';
    }
  }

  /**
   * 查找章节标题元素，尝试多种匹配方式
   * @param chapterId 章节ID
   * @param chapterTitle 章节标题
   * @returns 找到的标题元素或null
   */
  private static findChapterTitleElement(chapterId: string, chapterTitle: string): Element | null {
    // 1. 首先尝试使用data-chapter-id属性查找章节标题元素
    if (chapterId) {
      const elementById = document.querySelector(`[data-chapter-id="${chapterId}"]`);
      if (elementById) {
        console.log('✅ 通过章节ID找到标题元素');
        return elementById;
      }
    }
    
    // 2. 尝试使用data-chapter-title属性精确匹配
    if (chapterTitle) {
      const elementByExactTitle = document.querySelector(`[data-chapter-title="${chapterTitle}"]`);
      if (elementByExactTitle) {
        console.log('✅ 通过精确标题属性找到标题元素');
        return elementByExactTitle;
      }
    }
    
    // 3. 尝试使用标题文本内容精确匹配
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    for (const heading of Array.from(headings)) {
      if (heading.textContent?.trim() === chapterTitle) {
        console.log('✅ 通过精确文本内容找到标题元素');
        return heading;
      }
    }
    
    // 4. 尝试使用data-chapter-title属性部分匹配
    const allElements = document.querySelectorAll('[data-chapter-title]');
    for (const element of Array.from(allElements)) {
      const titleAttr = element.getAttribute('data-chapter-title');
      if (titleAttr && (
          titleAttr.includes(chapterTitle) || 
          chapterTitle.includes(titleAttr) ||
          this.isSimilarTitle(titleAttr, chapterTitle)
      )) {
        console.log('✅ 通过部分标题属性匹配找到标题元素');
        return element;
      }
    }
    
    // 5. 尝试使用标题文本内容部分匹配
    for (const heading of Array.from(headings)) {
      const headingText = heading.textContent?.trim() || '';
      if (headingText && (
          headingText.includes(chapterTitle) || 
          chapterTitle.includes(headingText) ||
          this.isSimilarTitle(headingText, chapterTitle)
      )) {
        console.log('✅ 通过部分文本内容匹配找到标题元素');
        return heading;
      }
    }
    
    // 6. 尝试提取章节编号进行匹配
    const chapterNumMatch = chapterTitle.match(/^(\d+\.\d+、|\d+、|第[一二三四五六七八九十\d]+章)/);
    if (chapterNumMatch) {
      const chapterNum = chapterNumMatch[1];
      for (const heading of Array.from(headings)) {
        const headingText = heading.textContent?.trim() || '';
        if (headingText.startsWith(chapterNum)) {
          console.log('✅ 通过章节编号匹配找到标题元素');
          return heading;
        }
      }
    }
    
    // 7. 尝试使用关键词匹配
    const keywords = this.extractKeywords(chapterTitle);
    if (keywords.length > 0) {
      for (const heading of Array.from(headings)) {
        const headingText = heading.textContent?.trim() || '';
        const matchCount = keywords.filter(keyword => 
          headingText.toLowerCase().includes(keyword.toLowerCase())
        ).length;
        
        // 如果匹配了超过一半的关键词，认为是相同标题
        if (matchCount >= Math.ceil(keywords.length / 2)) {
          console.log('✅ 通过关键词匹配找到标题元素');
          return heading;
        }
      }
    }
    
    // 没有找到匹配的标题元素
    return null;
  }

  /**
   * 判断两个标题是否相似
   */
  private static isSimilarTitle(title1: string, title2: string): boolean {
    // 移除所有空白字符和标点符号
    const normalize = (text: string) => {
      return text.replace(/[\s\p{P}]/gu, '').toLowerCase();
    };
    
    const normalized1 = normalize(title1);
    const normalized2 = normalize(title2);
    
    // 如果标题完全相同，返回true
    if (normalized1 === normalized2) return true;
    
    // 如果一个标题是另一个的子字符串，返回true
    if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) return true;
    
    // 计算字符重叠率
    const chars1 = new Set(normalized1.split(''));
    const chars2 = new Set(normalized2.split(''));
    
    let commonChars = 0;
    for (const char of chars1) {
      if (chars2.has(char)) commonChars++;
    }
    
    const overlapRatio = commonChars / Math.max(chars1.size, chars2.size);
    
    // 如果字符重叠率超过70%，认为是相似标题
    return overlapRatio > 0.7;
  }

  /**
   * 从标题中提取关键词
   */
  private static extractKeywords(title: string): string[] {
    // 移除章节编号
    const cleanTitle = title.replace(/^\d+\.\d+、|\d+、|第[一二三四五六七八九十\d]+章/, '').trim();
    
    // 移除常见的无意义词
    const stopWords = ['的', '与', '和', '及', '、', '是', '在', '对', '等', '中', '为'];
    
    // 分词（简单实现，实际应用可能需要更复杂的分词算法）
    const words = cleanTitle.split(/[\s、，,。.；;：:（）()【】\[\]]/g)
      .filter(word => word.length > 0 && !stopWords.includes(word));
    
    return words;
  }

  /**
   * 提取两个标题之间的内容
   * @param startHeading 开始的标题元素
   * @param headingLevel 标题级别
   * @param chapterType 章节类型
   * @returns 提取的内容
   */
  private static extractContentBetweenHeadings(
    startHeading: Element, 
    headingLevel: number,
    chapterType: string
  ): string {
    // 获取所有内容元素
    const allElements: Element[] = Array.from(document.querySelectorAll('*'));
    
    // 找到开始标题的索引
    const startIndex = allElements.indexOf(startHeading);
    if (startIndex === -1) {
      return '';
    }

    let content = startHeading.outerHTML + '\n'; // 包含标题本身
    let endIndex = allElements.length;

    // 查找结束位置 - 下一个同级或更高级标题
    for (let i = startIndex + 1; i < allElements.length; i++) {
      const element = allElements[i];
      
      // 检查是否为标题元素
      const tagName = element.tagName.toLowerCase();
      if (tagName.match(/^h[1-6]$/)) {
        const currentLevel = parseInt(tagName.substring(1));
        
        // 获取章节类型（如果有）
        const elementChapterType = element.getAttribute('data-chapter-type');
        
        // 如果是主章节，则在遇到下一个主章节时停止
        if (chapterType === 'main' && elementChapterType === 'main') {
          endIndex = i;
          break;
        }
        
        // 如果是子章节，则在遇到同级或更高级标题时停止
        if (chapterType === 'sub') {
          // 使用data-chapter-level属性（如果有）
          const dataLevel = element.getAttribute('data-chapter-level');
          const elementLevel = dataLevel ? parseInt(dataLevel) : currentLevel;
          
          if (elementLevel <= headingLevel) {
            endIndex = i;
            break;
          }
        }
      }
    }

    // 收集开始和结束之间的所有内容
    for (let i = startIndex + 1; i < endIndex; i++) {
      content += allElements[i].outerHTML + '\n';
    }

    return content;
  }

  /**
   * 从DOM中获取纯文本章节内容
   * @param chapterId 章节ID
   * @param chapterTitle 章节标题
   * @returns 纯文本章节内容
   */
  static extractPlainTextContent(chapterId: string, chapterTitle: string): string {
    const htmlContent = this.extractChapterContentFromDOM(chapterId, chapterTitle);
    
    // 创建一个临时元素来解析HTML
    if (typeof document !== 'undefined') {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      
      // 🔥 先移除不需要的UI元素
      this.removeUIElements(tempDiv);
      
      // 递归提取文本内容
      const extractText = (element: Element): string => {
        let text = '';
        
        // 处理所有子节点
        for (const child of Array.from(element.childNodes)) {
          if (child.nodeType === Node.TEXT_NODE) {
            const textContent = child.textContent?.trim() || '';
            if (textContent) {
              text += textContent + ' ';
            }
          } else if (child.nodeType === Node.ELEMENT_NODE) {
            const childElement = child as Element;
            
            // 跳过UI元素
            if (this.isUIElement(childElement)) {
              continue;
            }
            
            // 对于段落和标题，添加额外的换行
            const tagName = childElement.tagName.toLowerCase();
            const childText = extractText(childElement);
            
            if (['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div', 'li', 'blockquote'].includes(tagName)) {
              if (childText.trim()) {
                text += childText.trim() + '\n\n';
              }
            } else if (['br'].includes(tagName)) {
              text += '\n';
            } else if (childText.trim()) {
              text += childText.trim() + ' ';
            }
          }
        }
        
        return text;
      };
      
      const result = extractText(tempDiv).trim();
      
      // 🔥 清理多余的空行和空格
      return result
        .replace(/\n{3,}/g, '\n\n') // 合并多个空行为两个
        .replace(/[ \t]+/g, ' ') // 合并多个空格为一个
        .replace(/^\s*\n+/gm, '') // 移除行首的空白
        .trim();
    }
    
    return '';
  }

  /**
   * 🔥 新增：移除UI元素
   */
  private static removeUIElements(container: Element): void {
    const uiSelectors = [
      'button', // 按钮
      '.edit-button', // 编辑按钮
      '.chapter-edit-button', // 章节编辑按钮
      '[data-chapter-edit]', // 带有编辑数据属性的元素
      '.lucide', // Lucide图标
      '.icon', // 通用图标类
      'svg', // SVG图标
      '.prose-edit', // 编辑相关的类
      '.chapter-controls', // 章节控制元素
      '.floating-menu', // 浮动菜单
      '.toolbar', // 工具栏
      'script', // 脚本标签
      'style', // 样式标签
      'noscript', // noscript标签
    ];
    
    uiSelectors.forEach(selector => {
      const elements = container.querySelectorAll(selector);
      elements.forEach(element => element.remove());
    });
  }

  /**
   * 🔥 新增：判断是否为UI元素
   */
  private static isUIElement(element: Element): boolean {
    const tagName = element.tagName.toLowerCase();
    
    // 明显的UI元素标签
    if (['button', 'input', 'select', 'textarea', 'form', 'script', 'style', 'svg'].includes(tagName)) {
      return true;
    }
    
    // 检查类名
    const className = element.className || '';
    const uiClassPatterns = [
      /edit/i,
      /button/i,
      /btn/i,
      /icon/i,
      /lucide/i,
      /control/i,
      /toolbar/i,
      /menu/i,
      /floating/i,
      /hover/i,
    ];
    
    if (uiClassPatterns.some(pattern => pattern.test(className))) {
      return true;
    }
    
    // 检查数据属性
    const attributes = element.attributes;
    for (let i = 0; i < attributes.length; i++) {
      const attr = attributes[i];
      if (attr.name.startsWith('data-') && 
          (attr.name.includes('edit') || attr.name.includes('button') || attr.name.includes('control'))) {
        return true;
      }
    }
    
    // 检查文本内容是否像按钮文字
    const textContent = element.textContent?.trim() || '';
    const buttonTextPatterns = [
      /^编辑$/,
      /^修改$/,
      /^删除$/,
      /^保存$/,
      /^取消$/,
      /^确认$/,
      /^Edit$/i,
      /^Delete$/i,
      /^Save$/i,
      /^Cancel$/i,
    ];
    
    if (buttonTextPatterns.some(pattern => pattern.test(textContent))) {
      return true;
    }
    
    return false;
  }
} 