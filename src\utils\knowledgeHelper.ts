/**
 * 知识库查找工具函数
 * 用于处理拆分文件的查找逻辑
 */

import { useKnowledgeStore } from '@/store/knowledge';
// Knowledge类型在types.d.ts中定义为全局类型，无需导入

/**
 * 根据资源ID查找对应的知识库记录
 * 自动处理拆分文件的查找逻辑
 * @param resourceId 资源ID
 * @param knowledgeStore 知识库store实例（可选，如果不传则自动获取）
 * @returns 找到的知识库记录，如果没找到返回null
 */
export function findKnowledgeByResourceId(
  resourceId: string, 
  knowledgeStore?: ReturnType<typeof useKnowledgeStore.getState>
): Knowledge | null {
  if (!knowledgeStore) {
    knowledgeStore = useKnowledgeStore.getState();
  }

  // 首先尝试直接查找
  let resource = knowledgeStore.get(resourceId);
  
  // 如果直接查找失败，可能是拆分文件，尝试查找原始文件
  if (!resource && resourceId.includes('_')) {
    // 拆分文件的ID格式：原始ID_数字，提取原始ID
    const originalId = resourceId.split('_')[0];
    resource = knowledgeStore.get(originalId);
    
    if (resource) {
      console.log(`🔍 拆分文件查找成功: ${resourceId} -> 原始文件: ${resource.title}`);
    }
  }
  
  if (!resource) {
    console.warn(`⚠️ 无法找到资源内容: ID=${resourceId}`);
  }
  
  return resource;
}

/**
 * 批量查找知识库记录
 * @param resources 资源列表
 * @param knowledgeStore 知识库store实例（可选）
 * @returns 找到的知识库记录数组
 */
export function findKnowledgesByResources(
  resources: Array<{ id: string; status: string }>,
  knowledgeStore?: ReturnType<typeof useKnowledgeStore.getState>
): Knowledge[] {
  if (!knowledgeStore) {
    knowledgeStore = useKnowledgeStore.getState();
  }

  const knowledges: Knowledge[] = [];
  
  for (const item of resources) {
    if (item.status === "completed") {
      const resource = findKnowledgeByResourceId(item.id, knowledgeStore);
      if (resource) {
        knowledges.push(resource);
      }
    }
  }
  
  console.log(`📚 批量查找知识库: 找到 ${knowledges.length} 个有效资源`);
  return knowledges;
}

/**
 * 生成本地知识库内容字符串
 * 用于AI验证时的上下文准备
 * @param resources 资源列表
 * @param knowledgeStore 知识库store实例（可选）
 * @returns 格式化的本地知识库内容字符串
 */
export function generateLocalKnowledgeContent(
  resources: Array<{ id: string; status: string }>,
  knowledgeStore?: ReturnType<typeof useKnowledgeStore.getState>
): string {
  const knowledges = findKnowledgesByResources(resources, knowledgeStore);
  
  return knowledges
    .map(resource => `${resource.title}:\n${resource.content}`)
    .join('\n\n---\n\n');
} 