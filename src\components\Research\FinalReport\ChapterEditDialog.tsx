"use client";

import React, { useState, useEffect } from "react";
import { useTaskStore } from "@/store/task";
import { useChapterEdit } from "@/hooks/useChapterEdit";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { FileText, Send, LoaderCircle } from "lucide-react";
import { toast } from "sonner";
import ChapterResourceUpload from "./ChapterResourceUpload";
import dynamic from "next/dynamic";

// 动态导入Markdown组件，避免SSR问题
const MagicDownView = dynamic(() => import("@/components/MagicDown/View"), { ssr: false });

interface ChapterEditDialogProps {
  open: boolean;
  onClose: () => void;
  chapterTitle: string;
  chapterContent: string;
}

export default function ChapterEditDialog({
  open,
  onClose,
  chapterTitle,
  chapterContent,
}: ChapterEditDialogProps) {

  const taskStore = useTaskStore();
  const { editChapter, confirmReplace, isEditing, editProgress, editedContent } = useChapterEdit();
  const [description, setDescription] = useState("");
  const [generatedContent, setGeneratedContent] = useState("");
  const [showPreview, setShowPreview] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("edit");
  const [originalContent, setOriginalContent] = useState<string>("");

  // 在对话框打开时提取章节内容
  useEffect(() => {
    if (open) {
      getOriginalContent();
    }
  }, [open, chapterTitle]);

  // 获取原始章节内容  
  const getOriginalContent = () => {
    console.log('=== Chapter Content Extraction ===');
    console.log('chapterTitle:', chapterTitle);
    console.log('chapterContent length:', chapterContent?.length || 0);
    
    // 设置默认内容，避免显示空白
    setOriginalContent(chapterContent || "正在提取章节内容...");
    
    // 1. 首先尝试从DOM中提取完整内容
    try {
      if (typeof window !== 'undefined') {
        // 动态导入ChapterExtractor
        import('@/utils/deep-research/chapter-extractor').then(module => {
          if (module && module.ChapterExtractor) {
            const ChapterExtractor = module.ChapterExtractor;
            
            try {
              // 🔥 增强的DOM提取策略
              let domContent = '';
              
              // 策略1: 使用原有的提取方法
              domContent = ChapterExtractor.extractPlainTextContent('', chapterTitle);
              
              // 策略2: 如果提取失败，尝试直接查找章节元素
              if (!domContent || domContent.length < 200) {
                console.log('尝试策略2: 直接查找章节元素');
                domContent = extractFromChapterElement(chapterTitle);
              }
              
              // 策略3: 如果仍然失败，尝试查找数据属性标记的元素
              if (!domContent || domContent.length < 200) {
                console.log('尝试策略3: 查找数据属性标记的元素');
                domContent = extractFromDataAttributes(chapterTitle);
              }
              
              // 验证提取的内容质量
              if (domContent && validateExtractedContent(domContent, chapterTitle)) {
                console.log('✅ 成功从DOM提取内容，长度:', domContent.length);
                setOriginalContent(domContent);
              } else {
                console.log('❌ 从DOM提取内容失败或内容质量不佳');
                fallbackToProvidedContent();
              }
            } catch (extractError) {
              console.error('从DOM提取内容时出错:', extractError);
              fallbackToProvidedContent();
            }
          } else {
            console.error('ChapterExtractor模块不存在');
            fallbackToProvidedContent();
          }
        }).catch(error => {
          console.error('导入ChapterExtractor失败:', error);
          fallbackToProvidedContent();
        });
      } else {
        fallbackToProvidedContent();
      }
    } catch (error) {
      console.error('从DOM提取内容时出错:', error);
      fallbackToProvidedContent();
    }
    
    // 2. 如果DOM提取失败，使用提供的chapterContent
    function fallbackToProvidedContent() {
    if (chapterContent && chapterContent.trim() && chapterContent.length > 100) {
        console.log('✅ 使用提供的chapterContent，长度:', chapterContent.length);
        setOriginalContent(chapterContent);
      } else {
        console.log('❌ 提供的chapterContent太短或为空，尝试使用ChapterRegenerator');
        fallbackToRegenerator();
      }
    }
    
    // 3. 最后尝试使用ChapterRegenerator提取
    function fallbackToRegenerator() {
    const { finalReport, researchOutline } = taskStore;
    if (!finalReport) {
        console.error('❌ 未找到finalReport');
        setOriginalContent("未找到原文内容");
        return;
    }
    
    console.log('finalReport length:', finalReport.length);
    console.log('researchOutline available:', !!researchOutline);
      
      // 使用ChapterRegenerator的提取逻辑
      try {
        // 动态导入ChapterRegenerator
        import('@/utils/deep-research/chapter-regenerator').then(module => {
          if (module && module.ChapterRegenerator) {
            try {
              const ChapterRegenerator = module.ChapterRegenerator;
      const extractedContent = ChapterRegenerator.extractChapterContentWithOutline(
        finalReport,
        { title: chapterTitle, type: 'single_sub' }, // 简化的scope对象
        researchOutline
      );
      
              console.log('✅ 使用ChapterRegenerator提取内容，长度:', extractedContent.length);
              setOriginalContent(extractedContent);
            } catch (extractError) {
              console.error('ChapterRegenerator提取内容失败:', extractError);
              fallbackToSimpleExtract();
            }
          } else {
            console.error('ChapterRegenerator模块不存在');
            fallbackToSimpleExtract();
          }
        }).catch(error => {
          console.error('导入ChapterRegenerator失败:', error);
          fallbackToSimpleExtract();
        });
      } catch (error) {
        console.error('使用ChapterRegenerator时出错:', error);
        fallbackToSimpleExtract();
      }
    }
    
    // 4. 最后的备用方案：简单文本搜索
    function fallbackToSimpleExtract() {
      console.log('⚠️ 所有高级提取方法都失败，使用简单文本搜索');
      const { finalReport } = taskStore;
      if (!finalReport) {
        setOriginalContent("未找到原文内容");
        return;
      }
      
      const simpleContent = simpleExtractChapter(finalReport, chapterTitle);
      console.log('✅ 使用简单提取方法，提取内容长度:', simpleContent.length);
      setOriginalContent(simpleContent);
    }
  };

  // 🔥 新增：从章节元素直接提取内容
  const extractFromChapterElement = (title: string): string => {
    try {
      // 查找包含章节标题的所有标题元素
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let targetHeading: Element | null = null;
      
      for (const heading of headings) {
        const headingText = heading.textContent?.trim() || '';
        if (headingText.includes(title) || title.includes(headingText)) {
          targetHeading = heading;
          break;
        }
      }
      
      if (!targetHeading) {
        console.log('未找到匹配的章节标题元素');
        return '';
      }
      
      // 确定标题级别
      const tagName = targetHeading.tagName.toLowerCase();
      const startLevel = parseInt(tagName.charAt(1));
      
      // 从该标题开始收集内容，直到下一个同级或更高级标题
      let content = targetHeading.textContent?.trim() + '\n\n';
      let nextElement = targetHeading.nextElementSibling;
      
      while (nextElement) {
        // 如果遇到同级或更高级的标题，停止
        if (nextElement.tagName.match(/^H[1-6]$/)) {
          const nextLevel = parseInt(nextElement.tagName.charAt(1));
          if (nextLevel <= startLevel) {
            break;
          }
        }
        
        // 🔥 跳过UI元素
        if (isUIElement(nextElement)) {
          nextElement = nextElement.nextElementSibling;
          continue;
        }
        
        // 收集文本内容，但过滤掉按钮等UI元素
        const elementText = extractCleanText(nextElement);
        if (elementText) {
          content += elementText + '\n\n';
        }
        
        nextElement = nextElement.nextElementSibling;
        
        // 防止内容过长
        if (content.length > 50000) {
          break;
        }
      }
      
      return cleanExtractedContent(content);
    } catch (error) {
      console.error('从章节元素提取内容失败:', error);
      return '';
    }
  };

  // 🔥 新增：从数据属性标记的元素提取内容
  const extractFromDataAttributes = (title: string): string => {
    try {
      // 查找具有章节数据属性的元素
      const chapterElements = document.querySelectorAll('[data-chapter-title]');
      let targetElement: Element | null = null;
      
      for (const element of chapterElements) {
        const chapterTitle = element.getAttribute('data-chapter-title');
        if (chapterTitle && (chapterTitle.includes(title) || title.includes(chapterTitle))) {
          targetElement = element;
          break;
        }
      }
      
      if (!targetElement) {
        console.log('未找到具有数据属性的章节元素');
        return '';
      }
      
      // 从该元素开始收集内容
      let content = extractCleanText(targetElement) + '\n\n';
      let nextElement = targetElement.nextElementSibling;
      
      while (nextElement) {
        // 如果遇到下一个章节标记，停止
        if (nextElement.hasAttribute('data-chapter-title')) {
          break;
        }
        
        // 🔥 跳过UI元素
        if (isUIElement(nextElement)) {
          nextElement = nextElement.nextElementSibling;
          continue;
        }
        
        // 收集文本内容
        const elementText = extractCleanText(nextElement);
        if (elementText) {
          content += elementText + '\n\n';
        }
        
        nextElement = nextElement.nextElementSibling;
        
        // 防止内容过长
        if (content.length > 50000) {
          break;
        }
      }
      
      return cleanExtractedContent(content);
    } catch (error) {
      console.error('从数据属性提取内容失败:', error);
      return '';
    }
  };

  // 🔥 新增：判断是否为UI元素
  const isUIElement = (element: Element): boolean => {
    const tagName = element.tagName.toLowerCase();
    
    // 明显的UI元素标签
    if (['button', 'input', 'select', 'textarea', 'form', 'script', 'style', 'svg'].includes(tagName)) {
      return true;
    }
    
    // 检查类名
    const className = element.className || '';
    const uiClassPatterns = [
      /edit/i,
      /button/i,
      /btn/i,
      /icon/i,
      /lucide/i,
      /control/i,
      /toolbar/i,
      /menu/i,
      /floating/i,
      /hover/i,
    ];
    
    if (uiClassPatterns.some(pattern => pattern.test(className))) {
      return true;
    }
    
    // 检查数据属性
    const attributes = element.attributes;
    for (let i = 0; i < attributes.length; i++) {
      const attr = attributes[i];
      if (attr.name.startsWith('data-') && 
          (attr.name.includes('edit') || attr.name.includes('button') || attr.name.includes('control'))) {
        return true;
      }
    }
    
    // 检查文本内容是否像按钮文字
    const textContent = element.textContent?.trim() || '';
    const buttonTextPatterns = [
      /^编辑$/,
      /^修改$/,
      /^删除$/,
      /^保存$/,
      /^取消$/,
      /^确认$/,
      /^Edit$/i,
      /^Delete$/i,
      /^Save$/i,
      /^Cancel$/i,
    ];
    
    if (buttonTextPatterns.some(pattern => pattern.test(textContent))) {
      return true;
    }
    
    return false;
  };

  // 🔥 新增：提取干净的文本内容
  const extractCleanText = (element: Element): string => {
    // 创建元素的副本以避免修改原DOM
    const clone = element.cloneNode(true) as Element;
    
    // 移除所有UI元素
    const uiSelectors = [
      'button', '.edit-button', '.chapter-edit-button', '[data-chapter-edit]',
      '.lucide', '.icon', 'svg', '.prose-edit', '.chapter-controls',
      '.floating-menu', '.toolbar', 'script', 'style', 'noscript'
    ];
    
    uiSelectors.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
    
    // 递归移除UI元素
    const removeUIRecursively = (node: Element) => {
      const children = Array.from(node.children);
      children.forEach(child => {
        if (isUIElement(child)) {
          child.remove();
        } else {
          removeUIRecursively(child);
        }
      });
    };
    
    removeUIRecursively(clone);
    
    return clone.textContent?.trim() || '';
  };

  // 🔥 新增：清理提取的内容
  const cleanExtractedContent = (content: string): string => {
    return content
      .replace(/\n{3,}/g, '\n\n') // 合并多个空行
      .replace(/[ \t]+/g, ' ') // 合并多个空格
      .replace(/^\s*\n+/gm, '') // 移除行首空白
      .trim();
  };

  // 🔥 新增：验证提取内容的质量
  const validateExtractedContent = (content: string, title: string): boolean => {
    if (!content || content.trim().length < 100) {
      console.log('内容太短，质量不合格');
      return false;
    }
    
    // 检查内容是否包含章节标题（去除格式标记）
    const cleanTitle = title.replace(/[#*\[\]]/g, '').trim();
    const cleanContent = content.replace(/[#*\[\]]/g, '').trim();
    
    if (!cleanContent.includes(cleanTitle)) {
      console.log('内容不包含章节标题，可能提取错误');
      return false;
    }
    
    // 检查内容是否有合理的长度（至少500字符）
    if (content.length < 500) {
      console.log('内容长度不足500字符，可能不完整');
      return false;
    }
    
    // 检查内容是否看起来像是章节内容（包含段落）
    const paragraphCount = (content.match(/\n\n/g) || []).length;
    if (paragraphCount < 2) {
      console.log('内容段落数太少，可能不是完整章节');
      return false;
    }
    
    console.log('内容质量验证通过');
    return true;
  };

  // 简单的章节提取方法（备用）
  const simpleExtractChapter = (fullText: string, title: string) => {
    console.log('=== Simple Extraction Fallback ===');
    console.log('Title:', title);
    console.log('Full text length:', fullText.length);
    
    // 🔥 增强的章节标题匹配策略
    let titleStart = -1;
    let actualTitle = title;
    
    // 1. 精确匹配标题
    titleStart = fullText.indexOf(title);
    console.log('Exact title search result:', titleStart);
    
    // 2. 如果精确匹配失败，尝试匹配去除格式符号的标题
    if (titleStart === -1) {
      const cleanTitle = title.replace(/[#*\[\]]/g, '').trim();
      titleStart = fullText.indexOf(cleanTitle);
      if (titleStart !== -1) {
        actualTitle = cleanTitle;
        console.log('Clean title search for', cleanTitle, ':', titleStart);
      }
    }
    
    // 3. 如果仍然失败，尝试匹配章节编号
    if (titleStart === -1) {
      const chapterNum = title.match(/\d+\.\d+、|\d+、|第[一二三四五六七八九十\d]+章/);
      if (chapterNum) {
        const chapterPattern = chapterNum[0]; // 例如 "1.1、"
        titleStart = fullText.indexOf(chapterPattern);
        if (titleStart !== -1) {
          actualTitle = chapterPattern;
        console.log('Chapter pattern search for', chapterPattern, ':', titleStart);
        }
      }
    }
    
    // 4. 尝试部分匹配（关键词匹配）
    if (titleStart === -1) {
      const keywords = title.split(/[、，。：\s]+/).filter(word => word.length > 2);
      for (const keyword of keywords) {
        const keywordIndex = fullText.indexOf(keyword);
        if (keywordIndex !== -1) {
          // 向前查找可能的标题开始位置
          const beforeKeyword = fullText.substring(Math.max(0, keywordIndex - 100), keywordIndex);
          const headerMatch = beforeKeyword.match(/\n(#{1,6}\s*.*|第[一二三四五六七八九十\d]+章.*|\d+\.\d*、.*)/);
          if (headerMatch) {
            titleStart = keywordIndex - beforeKeyword.length + beforeKeyword.lastIndexOf(headerMatch[1]);
            actualTitle = keyword;
            console.log('Keyword search for', keyword, ':', titleStart);
            break;
          }
        }
      }
    }
    
    if (titleStart === -1) {
      console.log('⚠️ 无法找到章节标题，返回空内容');
      return "未能找到指定章节内容，请检查章节标题是否正确。";
    }
    
    // 🔥 增强的内容提取逻辑
    console.log('Found title at position:', titleStart);
    
    // 寻找章节结束位置
    let nextChapterStart = fullText.length;
    
    // 查找下一个章节的开始位置
    const remainingText = fullText.substring(titleStart + actualTitle.length);
    
    // 多种下一章节的匹配模式
    const nextChapterPatterns = [
      /\n#{1,6}\s+.{5,}/g,  // Markdown标题
      /\n第[一二三四五六七八九十\d]+章/g, // 第X章
      /\n\d+\.\d+、.{3,}/g, // 数字编号子章节
      /\n\d+、.{3,}/g,      // 数字编号章节
      /\n[一二三四五六七八九十]+、.{3,}/g // 中文编号章节
    ];
    
    for (const pattern of nextChapterPatterns) {
      const matches = Array.from(remainingText.matchAll(pattern));
      if (matches.length > 0) {
        const firstMatch = matches[0];
        const matchPosition = titleStart + actualTitle.length + firstMatch.index!;
        if (matchPosition < nextChapterStart) {
          nextChapterStart = matchPosition;
          console.log('Found next chapter at:', matchPosition, 'with pattern:', pattern);
        }
      }
    }
    
    // 提取章节内容
    let chapterContent = fullText.substring(titleStart, nextChapterStart).trim();
    
    // 🔥 设置最大提取长度为10000字符，确保内容完整
    const maxLength = 10000;
    if (chapterContent.length > maxLength) {
      console.log(`内容长度 ${chapterContent.length} 超过最大限制，截取前 ${maxLength} 字符`);
      chapterContent = chapterContent.substring(0, maxLength) + '\n\n[内容过长，已截取...]';
    }
    
    // 清理内容
    chapterContent = chapterContent
      .replace(/\n{3,}/g, '\n\n') // 删除多余空行
      .replace(/^\s+|\s+$/g, '') // 删除首尾空白
      .replace(/\[(\d+)\]/g, '[$1]'); // 确保引用格式正确
    
    console.log('Extracted content length:', chapterContent.length);
    console.log('Content preview:', chapterContent.substring(0, 200));
    
    // 🔥 内容质量检查
    if (chapterContent.length < 200) {
      console.log('⚠️ 提取的内容太短，可能不完整');
      return `提取的章节内容较短，可能不完整：\n\n${chapterContent}`;
    }
    
    return chapterContent;
  };

  const handleSubmit = async () => {
    if (!description.trim()) {
      toast.error("请描述您希望对此章节进行的修改");
      return;
    }

    try {
      const resources = taskStore.chapterEditState.editingResources.map(resource => ({
        id: resource.id || Date.now().toString(),
        title: resource.name,
        type: resource.type as 'text' | 'file',
        content: resource.content
      }));

      const result = await editChapter(
        chapterTitle,
        description,
        resources,
        originalContent // 🔥 传递DOM提取的原文内容
      );
      
      if (result.success && result.editedContent) {
        setGeneratedContent(result.editedContent);
        setShowPreview(true);
        setActiveTab("preview");
        toast.success("章节内容生成完成！请预览确认。");
      } else {
        toast.error(`重新撰写失败: ${result.error}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`重新撰写失败: ${errorMessage}`);
    }
  };

  const handleConfirmReplace = async () => {
    try {
      const contentToReplace = generatedContent || editedContent;
      
      console.log('🔄 开始替换章节内容:', chapterTitle);
      console.log('📝 新内容长度:', contentToReplace.length);
      console.log('📝 新内容预览:', contentToReplace.substring(0, 200) + '...');
      
      // 🔥 简化验证逻辑，只做基本检查
      if (!contentToReplace || contentToReplace.trim().length === 0) {
        toast.warning("替换内容不能为空");
        return;
      }
      
      // 🔥 放宽验证条件 - 只做警告不阻止替换
      const markdownValid = validateMarkdownFormat(contentToReplace);
      const titleValid = contentIncludesChapterTitle(contentToReplace, chapterTitle);
      
      if (!markdownValid) {
        console.warn('⚠️ 内容可能不是标准Markdown格式，但仍允许替换');
        toast.info("检测到内容可能不是标准Markdown格式，但将继续替换");
      }
      
      if (!titleValid) {
        console.warn('⚠️ 内容可能缺少章节标题，但仍允许替换');
        toast.info("检测到内容可能缺少章节标题，但将继续替换");
      }
      
      console.log('🚀 执行章节替换...');
      
      // 🔥 记录替换前的报告状态
      const beforeReplace = useTaskStore.getState().finalReport;
      console.log('📊 替换前报告长度:', beforeReplace.length);
      console.log('📊 替换前内容特征:', beforeReplace.substring(0, 50) + '...');
      
      const result = await confirmReplace(contentToReplace);
      
      if (result.success) {
        // 🔥 验证替换是否真正生效
        setTimeout(() => {
          const afterReplace = useTaskStore.getState().finalReport;
          console.log('📊 替换后报告长度:', afterReplace.length);
          console.log('📊 替换后内容特征:', afterReplace.substring(0, 50) + '...');
          
          if (afterReplace === beforeReplace) {
            console.error('❌ 替换后报告内容没有变化，可能替换失败');
            toast.error("替换失败：报告内容没有变化");
            return;
          }
          
          // 🔥 检查新内容的前100个字符是否在报告中
          const contentSample = contentToReplace.trim().substring(0, 100);
          if (afterReplace.includes(contentSample)) {
            console.log('✅ 替换成功：在报告中找到了新内容');
            toast.success("章节替换完成！");
            
            // 🔥 强制触发界面重新渲染
            const currentStore = useTaskStore.getState();
            currentStore.updateFinalReport(afterReplace + ''); // 强制触发状态更新
            
            // 清空所有状态并关闭对话框
            setDescription("");
            setGeneratedContent("");
            setShowPreview(false);
            setActiveTab("edit");
            setOriginalContent("");
            
            // 延迟关闭对话框，确保用户能看到成功提示
            setTimeout(() => {
              handleClose();
            }, 1000);
          } else {
            console.warn('⚠️ 替换完成但在报告中未找到新内容片段');
            console.log('🔍 查找的内容片段:', contentSample);
            // 🔥 即使没找到内容片段，只要长度变化了就认为替换成功
            if (afterReplace.length !== beforeReplace.length) {
              console.log('✅ 报告长度发生变化，认为替换成功');
              toast.success("章节替换完成！");
              
              // 清空所有状态并关闭对话框
              setDescription("");
              setGeneratedContent("");
              setShowPreview(false);
              setActiveTab("edit");
              setOriginalContent("");
              
              setTimeout(() => {
                handleClose();
              }, 1000);
            } else {
              toast.warning("替换完成但可能存在问题，请检查报告内容");
            }
          }
        }, 200);
      } else {
        throw new Error('替换操作返回失败状态');
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('❌ 章节替换失败:', error);
      toast.error(`替换失败: ${errorMessage}`);
    }
  };
  
  // 验证内容是否为Markdown格式
  const validateMarkdownFormat = (content: string): boolean => {
    if (!content || content.trim().length === 0) {
      return false;
    }
    
    // 检查基本的Markdown特征
    const markdownFeatures = [
      /^#+\s+/m,  // 标题
      /\n\n/,     // 段落分隔
      /-\s+/m,    // 列表项
      /\*\*.*?\*\*/  // 粗体
    ];
    
    // 如果至少有2个Markdown特征，认为是有效的Markdown
    const markdownScore = markdownFeatures.filter(pattern => pattern.test(content)).length;
    return markdownScore >= 2;
  };
  
  // 验证内容是否包含章节标题
  const contentIncludesChapterTitle = (content: string, title: string): boolean => {
    // 提取章节标题的关键部分
    const titleParts = title.split(/[、：]/);
    const mainPart = titleParts[0];
    
    // 检查内容是否包含章节标题或其关键部分
    return content.includes(title) || 
           content.includes(mainPart) || 
           // 检查Markdown标题格式
           content.match(new RegExp(`^#+\\s+.*${escapeRegExp(mainPart)}.*$`, 'm')) !== null;
  };
  
  // 转义正则表达式特殊字符
  const escapeRegExp = (string: string): string => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  const handleReject = () => {
    // 拒绝替换，清空生成内容，返回编辑状态
    setGeneratedContent("");
    setShowPreview(false);
    setActiveTab("edit");
    toast.info("已取消替换，您可以重新修改要求。");
  };

  const handleClose = () => {
    if (isEditing) {
      toast.warning("章节正在重新撰写中，请稍候...");
      return;
    }
    
    // 🔥 保存当前研究主题，以便在关闭对话框后恢复
    const currentQuestion = useTaskStore.getState().question;
    
    taskStore.stopChapterEdit();
    setDescription("");
    setGeneratedContent("");
    setShowPreview(false);
    setActiveTab("edit");
    setOriginalContent("");
    
    // 🔥 确保研究主题不会被清空
    if (currentQuestion) {
      useTaskStore.getState().setQuestion(currentQuestion);
    }
    
    onClose();
  };

  // 处理标签页切换
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            重新撰写章节
            {isEditing && <LoaderCircle className="w-4 h-4 animate-spin" />}
          </DialogTitle>
          <DialogDescription>
            重新撰写章节：{chapterTitle}
          </DialogDescription>
        </DialogHeader>

        {/* 重写进度显示 */}
        {isEditing && (
          <div className="px-6 py-4 bg-blue-50 dark:bg-blue-950/20 border-l-4 border-blue-400">
            <div className="flex items-center gap-2 mb-2">
              <LoaderCircle className="w-4 h-4 animate-spin" />
              <span className="font-medium">正在重新撰写章节</span>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{editProgress}</p>
            <Progress value={75} className="w-full" />
          </div>
        )}

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="h-full flex flex-col">
            {!showPreview && (
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="edit" disabled={isEditing}>撰写要求</TabsTrigger>
                <TabsTrigger value="resources" disabled={isEditing}>专用资源</TabsTrigger>
              </TabsList>
            )}

            <TabsContent value="edit" className="flex-1 overflow-hidden">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle>撰写要求</CardTitle>
                  <CardDescription>
                    请详细描述您希望对此章节进行的重新撰写要求，包括内容调整、风格要求、重点补充等
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col">
                  <Label htmlFor="description" className="mb-2">
                    撰写要求
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="例如：请重点分析竞争对手的市场策略，补充2024年最新的财务数据，调整语言风格更加客观专业..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="flex-1 min-h-[200px] resize-none"
                    disabled={isEditing}
                  />
                  <div className="mt-4 flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      字数: {description.length}
                      {taskStore.chapterEditState.editingResources.length > 0 && (
                        <span className="ml-4">
                          已添加资源: {taskStore.chapterEditState.editingResources.length} 个
                        </span>
                      )}
                    </span>
                      <Button 
                        onClick={handleSubmit}
                      disabled={!description.trim() || isEditing}
                      className="flex items-center gap-1"
                    >
                      {isEditing ? <LoaderCircle className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
                      {isEditing ? "生成中..." : "开始生成"}
                      </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="resources" className="flex-1 overflow-hidden">
              <ChapterResourceUpload />
            </TabsContent>

            <TabsContent value="preview" className="flex-1 overflow-hidden">
                <Card className="h-full flex flex-col">
                  <CardHeader>
                    <CardTitle>内容对比预览</CardTitle>
                    <CardDescription>
                    左侧为原文内容，右侧为重新生成的内容，确认无误后点击&quot;确认替换&quot;
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex-1 flex flex-col min-h-0">
                    <div className="flex-1 grid grid-cols-2 gap-4 min-h-0">
                    {/* 原文内容 - 使用Markdown渲染 */}
                      <div className="flex flex-col min-h-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium">原文内容</h4>
                          <span className="text-xs text-muted-foreground">
                          {originalContent.length} 字符
                          </span>
                        </div>
                        <div 
                        className="border rounded-md bg-gray-50 dark:bg-gray-900"
                          style={{ 
                            height: '400px', 
                          overflowY: 'auto',
                            overflowX: 'hidden',
                          maxHeight: '400px',
                          padding: '0.5rem'
                        }}
                      >
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          <MagicDownView>{originalContent}</MagicDownView>
                        </div>
                        </div>
                      </div>
                      
                    {/* 生成内容 - 使用Markdown渲染 */}
                      <div className="flex flex-col min-h-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium">重新生成内容</h4>
                          <span className="text-xs text-muted-foreground">
                            {(generatedContent || editedContent).length} 字符
                          </span>
                        </div>
                        <div 
                        className="border rounded-md bg-blue-50 dark:bg-blue-950/20"
                          style={{ 
                            height: '400px', 
                          overflowY: 'auto',
                            overflowX: 'hidden',
                          maxHeight: '400px',
                          padding: '0.5rem'
                        }}
                      >
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          <MagicDownView>{generatedContent || editedContent}</MagicDownView>
                        </div>
                        </div>
                      </div>
                    </div>
                    
                  <div className="flex justify-end items-center mt-4 pt-4 border-t">
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          onClick={handleReject}
                        >
                        重新编辑
                        </Button>
                        <Button 
                          onClick={handleConfirmReplace}
                        >
                          确认替换
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
          </Dialog>
    );
  } 