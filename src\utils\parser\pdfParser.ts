import * as pdfjsLib from "pdfjs-dist";

async function extractTextFromPDF(file: string | ArrayBuffer) {
  try {
    // 加载 PDF 文件
    const loadingTask = pdfjsLib.getDocument(file);
    const pdfDocument = await loadingTask.promise;

    let fullText = "";

    // 循环处理每一页
    for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
      const page = await pdfDocument.getPage(pageNum);
      const textContent = await page.getTextContent();

      // 将文本内容组合起来
      const pageText = textContent.items
        .filter((item) => "str" in item)
        .map((item) => item.str)
        .join(" ");
      fullText += pageText + "\n"; // 可以添加换行符来区分页面
    }

    return fullText;
  } catch (error) {
    console.error("Error extracting text:", error);
    throw new Error("Error extracting text");
  }
}

export async function getTextContent(file: File): Promise<string> {
  // 动态设置 worker 路径
  if (typeof window !== 'undefined') {
    // 在浏览器环境中
    const protocol = window.location.protocol;
    const host = window.location.host;
    const baseUrl = `${protocol}//${host}`;
    
    // 尝试不同的 worker 路径
    const workerPaths = [
      `${baseUrl}/scripts/pdf.worker.min.js`,
      `${baseUrl}/_next/static/scripts/pdf.worker.min.js`,
      './scripts/pdf.worker.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
    ];
    
    // 设置 worker 路径（使用第一个作为默认）
    pdfjsLib.GlobalWorkerOptions.workerSrc = workerPaths[0];
  } else {
    // 服务器环境的fallback
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  }

  if (!file) {
    throw new Error("No file provided");
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async () => {
      if (reader.result) {
        try {
          const text = await extractTextFromPDF(reader.result);
          resolve(text);
        } catch (error) {
          console.error("Error processing PDF:", error);
          reject(new Error("Error processing PDF"));
        }
      } else {
        reject(new Error("File reading failed"));
      }
    };

    reader.onerror = () => {
      reject(new Error("Error reading file"));
    };

    reader.readAsArrayBuffer(file);
  });
}
