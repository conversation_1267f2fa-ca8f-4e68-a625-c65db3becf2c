"use client";

import React, { useState, useCallback } from "react";
import { useTaskStore } from "@/store/task";
import { useKnowledgeStore } from "@/store/knowledge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Upload, FileText, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { formatSize, getTextByteSize } from "@/utils/file";
import { fileParser } from "@/utils/parser";

export default function ChapterResourceUpload() {
  const taskStore = useTaskStore();
  const [uploadMode, setUploadMode] = useState<"file" | "text">("file");
  const [textInput, setTextInput] = useState("");
  const [textTitle, setTextTitle] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  const generateResourceId = () => {
    return `chapter_resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const handleFileUpload = useCallback(async (files: FileList) => {
    if (!files.length) return;

    setIsUploading(true);
    try {
      const knowledgeStore = useKnowledgeStore.getState();
      
      for (const file of Array.from(files)) {
        const id = generateResourceId();
        const content = await fileParser(file);
        
        // 保存到知识库
        const knowledgeId = `chapter_${id}`;
        knowledgeStore.save({
          id: knowledgeId,
          title: `[章节专用] ${file.name}`,
          content,
          type: "file",
          createdAt: Date.now(),
          updatedAt: Date.now(),
          fileMeta: {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
          }
        });
        
        // 添加到章节编辑资源
        const resource = {
          id: knowledgeId,
          name: file.name,
          content,
          type: 'file' as const,
          size: file.size,
          createdAt: Date.now(),
        };

        taskStore.addChapterEditResource(resource);
        toast.success(`已添加资源到知识库：${file.name}`);
      }
    } catch (error) {
      console.error("文件上传失败:", error);
      toast.error("文件上传失败，请重试");
    } finally {
      setIsUploading(false);
    }
  }, [taskStore]);

  const handleTextSubmit = () => {
    if (!textInput.trim() || !textTitle.trim()) {
      toast.error("请填写标题和内容");
      return;
    }

    const knowledgeStore = useKnowledgeStore.getState();
    const id = generateResourceId();
    const knowledgeId = `chapter_text_${id}`;
    
    // 保存到知识库
    knowledgeStore.save({
      id: knowledgeId,
      title: `[章节专用] ${textTitle}`,
      content: textInput,
      type: "knowledge",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    const resource = {
      id: knowledgeId,
      name: textTitle,
      content: textInput,
      type: 'text' as const,
      size: getTextByteSize(textInput),
      createdAt: Date.now(),
    };

    taskStore.addChapterEditResource(resource);
    toast.success(`已添加文本资源到知识库：${textTitle}`);
    
    // 清空输入
    setTextInput("");
    setTextTitle("");
  };

  const handleRemoveResource = (resourceId: string) => {
    taskStore.removeChapterEditResource(resourceId);
    toast.success("资源已删除");
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle>专用研究资源</CardTitle>
        <CardDescription>
          为此次章节编辑上传专门的研究资料，这些资源仅用于本次修改，不会影响主要研究资源
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col overflow-hidden">
        <Tabs value={uploadMode} onValueChange={(value) => setUploadMode(value as "file" | "text")} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="file">文件上传</TabsTrigger>
            <TabsTrigger value="text">文本输入</TabsTrigger>
          </TabsList>

          <TabsContent value="file" className="flex-1 flex flex-col">
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.multiple = true;
                input.accept = '.txt,.md,.pdf,.doc,.docx';
                input.onchange = (e) => {
                  const files = (e.target as HTMLInputElement).files;
                  if (files) handleFileUpload(files);
                };
                input.click();
              }}
            >
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">
                {isUploading ? "上传中..." : "点击或拖拽上传文件"}
              </p>
              <p className="text-sm text-gray-500">
                支持 TXT、MD、PDF、DOC、DOCX 等格式
              </p>
            </div>
          </TabsContent>

          <TabsContent value="text" className="flex-1 flex flex-col">
            <div className="space-y-4 flex-1 flex flex-col">
              <div>
                <Label htmlFor="text-title">资源标题</Label>
                <Input
                  id="text-title"
                  placeholder="例如：竞争对手分析补充资料"
                  value={textTitle}
                  onChange={(e) => setTextTitle(e.target.value)}
                />
              </div>
              <div className="flex-1 flex flex-col">
                <Label htmlFor="text-content">文本内容</Label>
                <Textarea
                  id="text-content"
                  placeholder="请输入相关的研究资料或参考信息..."
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  className="flex-1 min-h-[200px] resize-none"
                />
              </div>
              <Button 
                onClick={handleTextSubmit}
                disabled={!textInput.trim() || !textTitle.trim()}
                className="w-full"
              >
                <Plus className="w-4 h-4 mr-2" />
                添加文本资源
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {/* 已添加的资源列表 */}
        {taskStore.chapterEditState.editingResources.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium mb-2">已添加的资源</h4>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead className="w-20">大小</TableHead>
                    <TableHead className="w-20">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {taskStore.chapterEditState.editingResources.map((resource) => (
                    <TableRow key={resource.id}>
                      <TableCell className="flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        <span className="truncate">{resource.name}</span>
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatSize(resource.size)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveResource(resource.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 