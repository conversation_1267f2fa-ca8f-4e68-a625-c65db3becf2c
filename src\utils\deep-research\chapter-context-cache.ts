/**
 * 章节上下文缓存器
 * 用于管理章节间的关键信息传递，确保万字长文的逻辑一致性
 */

export interface ChapterSummary {
  chapterNum: number;
  chapterTitle: string;
  keyFindings: string[];        // 关键发现
  keyData: Record<string, any>; // 关键数据点
  conclusions: string[];        // 主要结论
  references: string[];         // 重要引用
  generatedAt: Date;
  sectionSummaries?: SubSectionSummary[]; // 子章节摘要
}

export interface SubSectionSummary {
  sectionId: string;
  sectionTitle: string;
  keyPoints: string[];
  data: Record<string, any>;
}

export interface CrossChapterData {
  financialMetrics: Record<string, number>;  // 财务指标
  marketData: Record<string, any>;          // 市场数据
  competitorInfo: any[];                    // 竞争对手信息
  teamInfo: any[];                         // 团队信息
  riskFactors: string[];                   // 风险因素
  companyBasics: Record<string, any>;      // 公司基本信息
  industryContext: Record<string, any>;    // 行业背景
}

export interface ContextRelevanceRule {
  targetChapter: number;
  targetSection?: string;
  relevantChapters: number[];
  relevantDataKeys: string[];
  description: string;
}

export class ChapterContextCache {
  private chapterSummaries: Map<number, ChapterSummary> = new Map();
  private crossChapterData: CrossChapterData = {
    financialMetrics: {},
    marketData: {},
    competitorInfo: [],
    teamInfo: [],
    riskFactors: [],
    companyBasics: {},
    industryContext: {}
  };
  
  private storageKey = 'deep_research_chapter_context';
  
  // 关联规则：定义哪些章节/数据与当前章节相关
  private relevanceRules: ContextRelevanceRule[] = [
    {
      targetChapter: 2,
      relevantChapters: [1],
      relevantDataKeys: ['companyBasics', 'marketData'],
      description: '第2章经营状况需要第1章的商业模式基础'
    },
    {
      targetChapter: 3,
      relevantChapters: [1, 2],
      relevantDataKeys: ['companyBasics', 'financialMetrics'],
      description: '第3章管理团队需要了解业务和财务背景'
    },
    {
      targetChapter: 4,
      relevantChapters: [2, 3],
      relevantDataKeys: ['financialMetrics', 'teamInfo'],
      description: '第4章治理结构需要经营和团队信息'
    },
    {
      targetChapter: 5,
      relevantChapters: [1, 2, 3, 4],
      relevantDataKeys: ['financialMetrics', 'marketData', 'riskFactors'],
      description: '第5章发展情况需要综合前面所有信息'
    },
    {
      targetChapter: 6,
      relevantChapters: [1, 2, 3, 4, 5],
      relevantDataKeys: ['financialMetrics', 'marketData', 'riskFactors', 'companyBasics'],
      description: '第6章总结需要所有前面章节的核心信息'
    }
  ];

  /**
   * 保存章节摘要
   */
  saveChapterSummary(summary: ChapterSummary): void {
    this.chapterSummaries.set(summary.chapterNum, summary);
    
    // 自动更新跨章节数据
    this.updateCrossChapterDataFromSummary(summary);
    
    // 持久化
    this.persist();
  }

  /**
   * 获取前面章节的摘要
   */
  getPreviousChaptersSummary(currentChapter: number): ChapterSummary[] {
    const summaries: ChapterSummary[] = [];
    
    for (let i = 1; i < currentChapter; i++) {
      const summary = this.chapterSummaries.get(i);
      if (summary) {
        summaries.push(summary);
      }
    }
    
    return summaries;
  }

  /**
   * 获取与当前章节相关的上下文摘要
   */
  getRelevantChaptersSummary(currentChapter: number, sectionId?: string): ChapterSummary[] {
    const rule = this.relevanceRules.find(r => 
      r.targetChapter === currentChapter && 
      (!r.targetSection || r.targetSection === sectionId)
    );
    
    if (!rule) {
      return this.getPreviousChaptersSummary(currentChapter);
    }

    const relevantSummaries: ChapterSummary[] = [];
    rule.relevantChapters.forEach(chapterNum => {
      const summary = this.chapterSummaries.get(chapterNum);
      if (summary) {
        relevantSummaries.push(summary);
      }
    });

    return relevantSummaries;
  }

  /**
   * 更新跨章节数据
   */
  updateCrossChapterData(key: keyof CrossChapterData, data: any): void {
    if (key in this.crossChapterData) {
      switch (key) {
        case 'financialMetrics':
        case 'marketData':
        case 'companyBasics':
        case 'industryContext':
          // 对象类型的合并
          this.crossChapterData[key] = { 
            ...this.crossChapterData[key] as Record<string, any>, 
            ...data 
          };
          break;
          
        case 'competitorInfo':
        case 'teamInfo':
        case 'riskFactors':
          // 数组类型的追加
          const currentArray = this.crossChapterData[key] as any[];
          if (Array.isArray(data)) {
            currentArray.push(...data);
          } else {
            currentArray.push(data);
          }
          break;
      }
    }
    this.persist();
  }

  /**
   * 获取跨章节数据
   */
  getCrossChapterData(): CrossChapterData {
    return this.crossChapterData;
  }

  /**
   * 获取与当前章节相关的跨章节数据
   */
  getRelevantCrossChapterData(currentChapter: number, sectionId?: string): Partial<CrossChapterData> {
    const rule = this.relevanceRules.find(r => 
      r.targetChapter === currentChapter && 
      (!r.targetSection || r.targetSection === sectionId)
    );
    
    if (!rule) {
      return this.crossChapterData;
    }

    const relevantData: Partial<CrossChapterData> = {};
    rule.relevantDataKeys.forEach(key => {
      if (key in this.crossChapterData) {
        relevantData[key as keyof CrossChapterData] = this.crossChapterData[key as keyof CrossChapterData];
      }
    });

    return relevantData;
  }

  /**
   * 获取相关的上下文信息（格式化为文本）
   */
  getRelevantContext(chapterNum: number, sectionId?: string): string {
    const relevantSummaries = this.getRelevantChaptersSummary(chapterNum, sectionId);
    const relevantData = this.getRelevantCrossChapterData(chapterNum, sectionId);
    
    if (relevantSummaries.length === 0 && Object.keys(relevantData).length === 0) {
      return '';
    }

    let context = '## 前面章节关键信息摘要\n\n';
    
    // 添加章节摘要
    relevantSummaries.forEach(summary => {
      context += `### 第${summary.chapterNum}章：${summary.chapterTitle}\n\n`;
      
      if (summary.keyFindings.length > 0) {
        context += `**关键发现：**\n${summary.keyFindings.map(f => `- ${f}`).join('\n')}\n\n`;
      }
      
      if (summary.conclusions.length > 0) {
        context += `**主要结论：**\n${summary.conclusions.map(c => `- ${c}`).join('\n')}\n\n`;
      }
      
      if (Object.keys(summary.keyData).length > 0) {
        context += `**关键数据：**\n`;
        Object.entries(summary.keyData).forEach(([key, value]) => {
          context += `- ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
        });
        context += '\n';
      }
    });

    // 添加跨章节数据
    if (Object.keys(relevantData).length > 0) {
      context += `## 重要跨章节数据\n\n`;
      
      if (relevantData.financialMetrics && Object.keys(relevantData.financialMetrics).length > 0) {
        context += `**关键财务指标：**\n`;
        Object.entries(relevantData.financialMetrics).forEach(([key, value]) => {
          context += `- ${key}: ${value}\n`;
        });
        context += '\n';
      }

      if (relevantData.marketData && Object.keys(relevantData.marketData).length > 0) {
        context += `**市场数据：**\n`;
        Object.entries(relevantData.marketData).forEach(([key, value]) => {
          context += `- ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
        });
        context += '\n';
      }

      if (relevantData.riskFactors && relevantData.riskFactors.length > 0) {
        context += `**已识别风险因素：**\n${relevantData.riskFactors.map(r => `- ${r}`).join('\n')}\n\n`;
      }
    }

    return context;
  }

  /**
   * 从章节摘要自动更新跨章节数据
   */
  private updateCrossChapterDataFromSummary(summary: ChapterSummary): void {
    // 根据章节内容自动分类和存储关键数据
    Object.entries(summary.keyData).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      
      // 识别财务指标
      if (lowerKey.includes('收入') || lowerKey.includes('利润') || lowerKey.includes('营收') || 
          lowerKey.includes('毛利') || lowerKey.includes('净利') || lowerKey.includes('roe') || 
          lowerKey.includes('roa') || lowerKey.includes('debt') || lowerKey.includes('ratio')) {
        this.crossChapterData.financialMetrics[key] = value;
      }
      
      // 识别市场数据
      else if (lowerKey.includes('市场') || lowerKey.includes('规模') || lowerKey.includes('份额') || 
               lowerKey.includes('增长') || lowerKey.includes('竞争')) {
        this.crossChapterData.marketData[key] = value;
      }
      
      // 识别公司基本信息
      else if (lowerKey.includes('公司') || lowerKey.includes('产品') || lowerKey.includes('业务') || 
               lowerKey.includes('成立') || lowerKey.includes('注册')) {
        this.crossChapterData.companyBasics[key] = value;
      }
    });

    // 提取风险因素
    summary.keyFindings.forEach(finding => {
      if (finding.includes('风险') || finding.includes('挑战') || finding.includes('问题') || 
          finding.includes('不足') || finding.includes('威胁')) {
        if (!this.crossChapterData.riskFactors.includes(finding)) {
          this.crossChapterData.riskFactors.push(finding);
        }
      }
    });
  }

  /**
   * 持久化到本地存储
   */
  persist(): void {
    try {
      const data = {
        chapterSummaries: Array.from(this.chapterSummaries.entries()),
        crossChapterData: this.crossChapterData,
        lastUpdated: new Date().toISOString()
      };
      
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem(this.storageKey, JSON.stringify(data));
      }
    } catch (error) {
      console.warn('Failed to persist chapter context cache:', error);
    }
  }

  /**
   * 从本地存储恢复
   */
  restore(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem(this.storageKey);
        if (stored) {
          const data = JSON.parse(stored);
          
          // 恢复章节摘要
          this.chapterSummaries.clear();
          if (data.chapterSummaries) {
            data.chapterSummaries.forEach(([key, value]: [number, ChapterSummary]) => {
              // 转换日期字符串回Date对象
              value.generatedAt = new Date(value.generatedAt);
              this.chapterSummaries.set(key, value);
            });
          }
          
          // 恢复跨章节数据
          if (data.crossChapterData) {
            this.crossChapterData = { ...this.crossChapterData, ...data.crossChapterData };
          }
        }
      }
    } catch (error) {
      console.warn('Failed to restore chapter context cache:', error);
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.chapterSummaries.clear();
    this.crossChapterData = {
      financialMetrics: {},
      marketData: {},
      competitorInfo: [],
      teamInfo: [],
      riskFactors: [],
      companyBasics: {},
      industryContext: {}
    };
    
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem(this.storageKey);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    cachedChapters: number;
    totalKeyFindings: number;
    totalConclusions: number;
    crossDataKeys: number;
  } {
    const summaries = Array.from(this.chapterSummaries.values());
    
    return {
      cachedChapters: summaries.length,
      totalKeyFindings: summaries.reduce((sum, s) => sum + s.keyFindings.length, 0),
      totalConclusions: summaries.reduce((sum, s) => sum + s.conclusions.length, 0),
      crossDataKeys: Object.keys(this.crossChapterData.financialMetrics).length + 
                    Object.keys(this.crossChapterData.marketData).length +
                    Object.keys(this.crossChapterData.companyBasics).length
    };
  }
} 