/**
 * 上下文注入器
 * 负责将前面章节的关键信息注入到新章节的生成过程中，确保逻辑连贯性
 */

import { ChapterContextCache } from './chapter-context-cache';

export interface ContextInjectionConfig {
  enableCrossReference: boolean;     // 启用跨章节引用
  enableDataConsistency: boolean;    // 启用数据一致性检查
  maxContextLength: number;          // 最大上下文长度
  prioritizeRecentChapters: boolean; // 优先使用最近章节信息
}

export class ContextInjector {
  private contextCache: ChapterContextCache;
  private config: ContextInjectionConfig;

  constructor(
    contextCache: ChapterContextCache, 
    config: Partial<ContextInjectionConfig> = {}
  ) {
    this.contextCache = contextCache;
    this.config = {
      enableCrossReference: true,
      enableDataConsistency: true,
      maxContextLength: 2000, // 限制上下文长度避免token超限
      prioritizeRecentChapters: true,
      ...config
    };
  }

  /**
   * 为子章节生成增强的上下文信息
   */
  injectContextForSubChapter(
    chapterNum: number,
    sectionIndex: number,
    originalPrompt: string
  ): string {
    const relevantContext = this.buildRelevantContext(chapterNum, sectionIndex);
    
    if (!relevantContext || relevantContext.trim().length === 0) {
      return originalPrompt;
    }

    // 检查上下文长度，如果过长则截断
    const trimmedContext = this.trimContextToLimit(relevantContext);

    return `${originalPrompt}

<PREVIOUS_CHAPTERS_CONTEXT>
${trimmedContext}
</PREVIOUS_CHAPTERS_CONTEXT>

**上下文使用指南：**
- 可以引用上述前面章节的关键发现和数据，使用格式：[前述第X章]
- 避免重复详细分析已在前面章节深入讨论的内容
- 可以基于前面章节的结论进行逻辑延续和深化
- 确保数据引用的一致性，如有冲突请说明原因
- 重点关注本章节特有的分析角度，与前面章节形成互补
`;
  }

  /**
   * 为章节总结生成上下文信息
   */
  injectContextForChapterSummary(chapterNum: number): string {
    const allPreviousChapters = this.contextCache.getPreviousChaptersSummary(chapterNum);
    
    if (allPreviousChapters.length === 0) {
      return '';
    }

    let context = '## 前面章节核心摘要\n\n';
    
    allPreviousChapters.forEach(summary => {
      context += `### 第${summary.chapterNum}章核心结论\n`;
      if (summary.conclusions.length > 0) {
        context += summary.conclusions.map(c => `- ${c}`).join('\n') + '\n\n';
      }
    });

    return context;
  }

  /**
   * 构建相关上下文信息
   */
  private buildRelevantContext(chapterNum: number, sectionIndex: number): string {
    // 获取章节ID用于更精确的相关性判断
    const sectionId = this.getSectionId(chapterNum, sectionIndex);
    
    const relevantSummaries = this.contextCache.getRelevantChaptersSummary(chapterNum, sectionId);
    const relevantData = this.contextCache.getRelevantCrossChapterData(chapterNum, sectionId);
    
    if (relevantSummaries.length === 0 && Object.keys(relevantData).length === 0) {
      return '';
    }

    let context = '';

    // 添加章节摘要信息
    if (relevantSummaries.length > 0) {
      context += this.buildChapterSummariesContext(relevantSummaries, chapterNum);
    }

    // 添加跨章节数据
    if (Object.keys(relevantData).length > 0) {
      context += this.buildCrossDataContext(relevantData, chapterNum);
    }

    // 添加特定章节的联系提示
    context += this.buildChapterSpecificContext(chapterNum);

    return context;
  }

  /**
   * 构建章节摘要上下文
   */
  private buildChapterSummariesContext(summaries: any[], currentChapter: number): string {
    let context = '## 前面章节关键信息\n\n';
    
    // 如果配置为优先最近章节，则按时间排序
    const sortedSummaries = this.config.prioritizeRecentChapters 
      ? summaries.sort((a, b) => b.chapterNum - a.chapterNum)
      : summaries;

    sortedSummaries.forEach(summary => {
      context += `### 第${summary.chapterNum}章：${summary.chapterTitle}\n\n`;
      
      // 添加关键发现（限制数量）
      if (summary.keyFindings && summary.keyFindings.length > 0) {
        const limitedFindings = summary.keyFindings.slice(0, 3); // 最多3个
        context += `**关键发现：**\n${limitedFindings.map((f: string) => `- ${f}`).join('\n')}\n\n`;
      }
      
      // 添加关键结论
      if (summary.conclusions && summary.conclusions.length > 0) {
        const limitedConclusions = summary.conclusions.slice(0, 2); // 最多2个
        context += `**主要结论：**\n${limitedConclusions.map((c: string) => `- ${c}`).join('\n')}\n\n`;
      }
      
      // 添加关键数据（筛选相关的）
      if (summary.keyData && Object.keys(summary.keyData).length > 0) {
        const relevantData = this.filterRelevantDataForChapter(summary.keyData, currentChapter);
        if (Object.keys(relevantData).length > 0) {
          context += `**相关数据：**\n`;
          Object.entries(relevantData).slice(0, 4).forEach(([key, value]) => {
            context += `- ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
          });
          context += '\n';
        }
      }
    });

    return context;
  }

  /**
   * 构建跨章节数据上下文
   */
  private buildCrossDataContext(relevantData: any, currentChapter: number): string {
    let context = '## 重要数据汇总\n\n';
    
    // 添加财务数据
    if (relevantData.financialMetrics && Object.keys(relevantData.financialMetrics).length > 0) {
      context += `**关键财务指标：**\n`;
      Object.entries(relevantData.financialMetrics).slice(0, 5).forEach(([key, value]) => {
        context += `- ${key}: ${value}\n`;
      });
      context += '\n';
    }

    // 添加市场数据
    if (relevantData.marketData && Object.keys(relevantData.marketData).length > 0) {
      context += `**市场数据：**\n`;
      Object.entries(relevantData.marketData).slice(0, 4).forEach(([key, value]) => {
        context += `- ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
      });
      context += '\n';
    }

    // 添加风险因素（如果相关）
    if (relevantData.riskFactors && relevantData.riskFactors.length > 0 && 
        (currentChapter >= 4 || currentChapter === 6)) { // 治理、发展和总结章节关注风险
      context += `**已识别风险：**\n`;
      relevantData.riskFactors.slice(0, 3).forEach((risk: string) => {
        context += `- ${risk}\n`;
      });
      context += '\n';
    }

    return context;
  }

  /**
   * 构建特定章节的上下文提示
   */
  private buildChapterSpecificContext(chapterNum: number): string {
    const contextPrompts: Record<number, string> = {
      2: `
**第2章分析重点：**
- 基于第1章的商业模式分析，深入财务和经营数据
- 重点关注盈利模式的实际执行效果
- 分析数据变化趋势，不重复商业模式描述`,

      3: `
**第3章分析重点：**
- 结合前面章节的业务和财务状况，分析管理团队适配性
- 重点评估团队能力与公司发展阶段的匹配度
- 避免重复前面的业务描述，聚焦人员分析`,

      4: `
**第4章分析重点：**
- 基于前面的经营和团队分析，评估治理结构合理性
- 重点关注制度建设与实际执行的匹配情况
- 分析治理水平对风险控制的有效性`,

      5: `
**第5章分析重点：**
- 综合前面所有章节信息，评估整体发展轨迹
- 重点分析实际表现与投资预期的偏差
- 基于已有信息评估投资回报和退出前景`,

      6: `
**第6章分析重点：**
- 综合总结前面所有章节的核心发现
- 重点提供投资决策建议，不重复前面的详细分析
- 基于全面信息给出监控要点和操作建议`
    };

    return contextPrompts[chapterNum] || '';
  }

  /**
   * 筛选与当前章节相关的数据
   */
  private filterRelevantDataForChapter(data: Record<string, any>, currentChapter: number): Record<string, any> {
    const relevantData: Record<string, any> = {};
    
    Object.entries(data).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      
      switch (currentChapter) {
        case 2: // 经营状况章节
          if (lowerKey.includes('收入') || lowerKey.includes('利润') || 
              lowerKey.includes('预算') || lowerKey.includes('营销') ||
              lowerKey.includes('成本') || lowerKey.includes('费用')) {
            relevantData[key] = value;
          }
          break;
          
        case 3: // 管理团队章节
          if (lowerKey.includes('人员') || lowerKey.includes('团队') || 
              lowerKey.includes('管理') || lowerKey.includes('员工') ||
              lowerKey.includes('激励') || lowerKey.includes('薪酬')) {
            relevantData[key] = value;
          }
          break;
          
        case 4: // 治理结构章节
          if (lowerKey.includes('制度') || lowerKey.includes('控制') || 
              lowerKey.includes('审计') || lowerKey.includes('风险') ||
              lowerKey.includes('合规') || lowerKey.includes('治理')) {
            relevantData[key] = value;
          }
          break;
          
        case 5: // 发展情况章节
          if (lowerKey.includes('增长') || lowerKey.includes('发展') || 
              lowerKey.includes('市值') || lowerKey.includes('估值') ||
              lowerKey.includes('ipo') || lowerKey.includes('退出')) {
            relevantData[key] = value;
          }
          break;
          
        case 6: // 总结章节
          // 总结章节可以使用所有关键数据
          relevantData[key] = value;
          break;
          
        default:
          relevantData[key] = value;
      }
    });
    
    return relevantData;
  }

  /**
   * 限制上下文长度
   */
  private trimContextToLimit(context: string): string {
    if (context.length <= this.config.maxContextLength) {
      return context;
    }

    // 按段落截断，保持内容完整性
    const paragraphs = context.split('\n\n');
    let trimmedContext = '';
    
    for (const paragraph of paragraphs) {
      if ((trimmedContext + paragraph).length > this.config.maxContextLength) {
        break;
      }
      trimmedContext += paragraph + '\n\n';
    }
    
    return trimmedContext.trim() + '\n\n[注：上下文已截断以控制长度]';
  }

  /**
   * 获取章节ID
   */
  private getSectionId(chapterNum: number, sectionIndex: number): string {
    return `${chapterNum}.${sectionIndex + 1}`;
  }

  /**
   * 验证上下文质量
   */
  validateContextQuality(context: string): {
    isValid: boolean;
    quality: 'high' | 'medium' | 'low';
    warnings: string[];
  } {
    const warnings: string[] = [];
    let score = 0;

    // 检查长度
    if (context.length === 0) {
      warnings.push('上下文为空');
      return { isValid: false, quality: 'low', warnings };
    }

    if (context.length > this.config.maxContextLength) {
      warnings.push('上下文过长，可能影响生成质量');
    } else {
      score += 1;
    }

    // 检查内容结构
    if (context.includes('## 前面章节关键信息') || context.includes('## 重要数据汇总')) {
      score += 2;
    }

    // 检查数据密度
    const dataPatterns = /\d+[%万亿千百]|\d+\.\d+|[0-9]+/g;
    const dataMatches = context.match(dataPatterns);
    if (dataMatches && dataMatches.length >= 3) {
      score += 1;
    }

    // 检查结构化信息
    if (context.includes('**') && context.includes('- ')) {
      score += 1;
    }

    const quality = score >= 4 ? 'high' : score >= 2 ? 'medium' : 'low';
    const isValid = score >= 1;

    return { isValid, quality, warnings };
  }

  /**
   * 生成数据一致性检查提示
   */
  generateConsistencyCheckPrompt(): string {
    if (!this.config.enableDataConsistency) {
      return '';
    }

    const crossData = this.contextCache.getCrossChapterData();
    
    if (Object.keys(crossData.financialMetrics).length === 0) {
      return '';
    }

    return `
**数据一致性检查：**
请确保本章节中引用的以下数据与前面章节保持一致：
${Object.entries(crossData.financialMetrics).map(([key, value]) => 
  `- ${key}: ${value}`
).join('\n')}

如发现数据冲突，请：
1. 说明差异原因（如统计口径不同、时间跨度不同等）
2. 明确指出使用哪个数据并解释选择原因
3. 确保后续章节使用统一的数据标准
`;
  }
} 