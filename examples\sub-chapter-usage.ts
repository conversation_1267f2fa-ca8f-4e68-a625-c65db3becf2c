/**
 * 子章节生成器使用示例
 * 演示如何使用新的子章节拆分功能来应对token限制
 */

import { SubChapterGenerator, createSubChapterWorkflow } from '@/utils/deep-research/sub-chapter-generator';

// 模拟研究数据
const mockResearchData = {
  plan: "分析XX公司的整体情况，重点关注商业模式、经营状况、管理团队等",
  learnings: [
    "公司在AI领域有重要技术突破",
    "财务状况稳健，收入持续增长",
    "管理团队经验丰富"
  ],
  sources: [
    { title: "公司2024年年报", url: "https://example.com/report" },
    { title: "行业分析报告", url: "https://example.com/industry" }
  ],
  localResources: [
    { title: "内部调研资料", content: "详细的内部分析..." }
  ],
  images: [
    { description: "公司组织架构图", url: "https://example.com/org-chart.png" }
  ],
  requirement: "生成专业的企业管理报告，注重数据分析和投资建议"
};

/**
 * 基础使用示例
 */
async function basicUsageExample() {
  console.log("=== 基础使用示例 ===");
  
  // 创建生成器
  const generator = new SubChapterGenerator(mockResearchData);
  
  // 查看所有待生成的子章节
  const allSubChapters = generator.getAllSubChapterInfo();
  console.log(`总共需要生成 ${allSubChapters.length} 个子章节`);
  
  // 生成第1章第1节的提示词
  const prompt1_1 = generator.generateSubChapterPrompt(1, 0);
  console.log("第1章第1节提示词生成完成");
  
  // 模拟生成的内容
  const mockContent1_1 = `
## 1.1、产品的创新性、领先性与核心优势

### 1.1.1、产品核心描述
XX公司的核心产品是基于AI的智能分析平台...

### 1.1.2、技术创新分析  
公司在机器学习算法方面取得重要突破...

### 1.1.3、竞争优势识别
相比竞争对手，公司的核心优势体现在...
  `;
  
  // 保存子章节内容
  generator.saveSubChapterContent(1, 0, mockContent1_1);
  console.log("第1章第1节内容已保存");
  
  // 查看进度
  const progress = generator.getProgress();
  console.log(`当前进度: ${progress.completedSubChapters}/${progress.totalSubChapters} (${progress.progressPercentage}%)`);
}

/**
 * 批量生成示例
 */
async function batchGenerationExample() {
  console.log("\n=== 批量生成示例 ===");
  
  const { generator, generateAllSubChapterPrompts } = createSubChapterWorkflow(mockResearchData);
  
  // 生成所有子章节的提示词
  const allPrompts = generateAllSubChapterPrompts();
  console.log(`生成了 ${allPrompts.length} 个子章节提示词`);
  
  // 模拟批量处理
  allPrompts.forEach((promptInfo, index) => {
    console.log(`${index + 1}. [${promptInfo.key}] ${promptInfo.title}`);
    
    // 这里你可以：
    // 1. 将提示词发送给大模型API
    // 2. 收集生成的内容
    // 3. 调用 generator.saveSubChapterContent() 保存
  });
}

/**
 * 章节合并示例
 */
async function chapterMergeExample() {
  console.log("\n=== 章节合并示例 ===");
  
  const generator = new SubChapterGenerator(mockResearchData);
  
  // 模拟第1章所有子章节都已生成
  const mockSubChapterContents = [
    "## 1.1、产品的创新性、领先性与核心优势\n产品分析内容...",
    "## 1.2、项目公司的盈利模式、营销策略、下年度商业计划\n盈利模式分析...",
    "## 1.3、目标市场的有效规模\n市场规模分析...",
    "## 1.4、行业情况跟踪\n行业分析内容...",
    "## 1.5、引入战略投资人、战略联盟\n战略投资分析..."
  ];
  
  // 保存所有子章节
  mockSubChapterContents.forEach((content, index) => {
    generator.saveSubChapterContent(1, index, content);
  });
  
  // 生成合并提示词
  const mergePrompt = generator.generateChapterMergePrompt(1);
  console.log("第1章合并提示词生成完成");
  
  // 模拟合并后的内容
  const mergedChapter1 = `
# 第1章、项目公司的商业模式

${mockSubChapterContents.join('\n\n')}
  `;
  
  // 保存合并后的章节
  generator.saveChapterContent(1, mergedChapter1);
  console.log("第1章合并完成");
}

/**
 * 最终报告生成示例
 */
async function finalReportExample() {
  console.log("\n=== 最终报告生成示例 ===");
  
  const generator = new SubChapterGenerator(mockResearchData);
  
  // 模拟所有章节都已生成并合并
  for (let chapterNum = 1; chapterNum <= 6; chapterNum++) {
    const mockChapterContent = `# 第${chapterNum}章、章节标题\n章节内容...`;
    generator.saveChapterContent(chapterNum, mockChapterContent);
  }
  
  // 生成报告标题
  const titlePrompt = generator.generateReportTitlePrompt();
  console.log("报告标题提示词生成完成");
  
  // 模拟生成的标题
  const reportTitle = "XX公司 2025年度企业管理报告";
  
  // 生成最终合并提示词
  const finalPrompt = generator.generateFinalReportMergePrompt(reportTitle);
  console.log("最终报告合并提示词生成完成");
  
  console.log("整个报告生成流程完成！");
}

/**
 * 状态保存和恢复示例
 */
async function stateManagementExample() {
  console.log("\n=== 状态管理示例 ===");
  
  const generator = new SubChapterGenerator(mockResearchData);
  
  // 生成一些内容
  generator.saveSubChapterContent(1, 0, "第1.1节内容");
  generator.saveSubChapterContent(1, 1, "第1.2节内容");
  
  // 导出状态
  const state = generator.exportState();
  console.log(`导出状态: ${state.subChapters.length} 个子章节, ${state.chapters.length} 个章节`);
  
  // 创建新的生成器并恢复状态
  const newGenerator = new SubChapterGenerator(mockResearchData);
  newGenerator.importState(state);
  
  const restoredProgress = newGenerator.getProgress();
  console.log(`恢复后进度: ${restoredProgress.completedSubChapters}/${restoredProgress.totalSubChapters}`);
}

/**
 * 完整工作流程示例
 */
async function completeWorkflowExample() {
  console.log("\n=== 完整工作流程示例 ===");
  
  const generator = new SubChapterGenerator(mockResearchData);
  
  console.log("第1步: 查看所有待生成的子章节");
  const allInfo = generator.getAllSubChapterInfo();
  allInfo.forEach(info => {
    console.log(`  ${info.sectionId}: ${info.title} (${info.wordCount})`);
  });
  
  console.log("\n第2步: 按顺序生成子章节");
  let nextSubChapter = generator.getNextSubChapter();
  let generatedCount = 0;
  
  while (nextSubChapter && generatedCount < 3) { // 只演示生成3个
    console.log(`  正在生成: ${nextSubChapter.sectionId} - ${nextSubChapter.title}`);
    
    // 生成提示词
    const prompt = generator.generateSubChapterPrompt(
      nextSubChapter.chapterNum, 
      nextSubChapter.sectionIndex
    );
    
    // 模拟调用AI生成内容
    const mockContent = `## ${nextSubChapter.sectionId}、${nextSubChapter.title}\n模拟生成的内容...`;
    
    // 保存内容
    generator.saveSubChapterContent(
      nextSubChapter.chapterNum,
      nextSubChapter.sectionIndex,
      mockContent
    );
    
    generatedCount++;
    nextSubChapter = generator.getNextSubChapter();
  }
  
  console.log("\n第3步: 查看进度");
  const finalProgress = generator.getProgress();
  console.log(`  完成进度: ${finalProgress.progressPercentage}%`);
}

// 运行所有示例
async function runAllExamples() {
  try {
    await basicUsageExample();
    await batchGenerationExample();
    await chapterMergeExample();
    await finalReportExample();
    await stateManagementExample();
    await completeWorkflowExample();
    
    console.log("\n✅ 所有示例运行完成！");
  } catch (error) {
    console.error("❌ 示例运行出错:", error);
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runAllExamples();
}

export {
  basicUsageExample,
  batchGenerationExample,
  chapterMergeExample,
  finalReportExample,
  stateManagementExample,
  completeWorkflowExample
}; 