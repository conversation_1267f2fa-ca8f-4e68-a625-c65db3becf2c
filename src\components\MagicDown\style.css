.magicdown-editor {
  .cm-line {
    @apply leading-8 empty:my-0;
  }
  .cm-editor {
    @apply bg-transparent dark:text-slate-200;
    &.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
    .cm-selectionBackground,
    .cm-content ::selection {
      @apply bg-blue-400/40;
    }
  }
  .cm-tooltips {
    @apply text-slate-800 dark:text-slate-200 dark:bg-slate-800;
    & > span {
      @apply hover:bg-slate-200 hover:dark:bg-slate-700 p-0.5;
      & > svg {
        @apply w-5 h-5;
      }
    }
  }
  .cm-slash-command-list {
    @apply text-slate-800 dark:text-slate-200 dark:bg-slate-800;
    .cm-slash-command-item {
      @apply hover:bg-slate-200 hover:dark:bg-slate-700;
    }
    .cm-slash-label-item {
      @apply text-slate-500;
    }
  }
}

.magicdown-view {
  .hljs {
    background-color: transparent;
  }
  ul {
    @apply ps-4;
  }
  hr {
    @apply mt-6 mb-6;
  }
  
  /* 段落首行缩进 - 中文文章传统格式 */
  p {
    text-indent: 2em; /* 首行缩进2个字符 */
    margin-bottom: 1em; /* 段落间距 */
    line-height: 1.8; /* 行高设置 */
  }
  
  /* 排除不需要首行缩进的特殊段落 */
  p:first-child,
  li > p,
  blockquote p,
  .katex-display + p,
  table + p {
    text-indent: 0; /* 这些情况不需要首行缩进 */
  }
  
  .katex-display {
    overflow-y: auto;
    overflow-x: auto;
    min-height: 3rem;
    max-height: 80vh;
    padding: 1rem;
    margin: 1rem 0;
    background: transparent;
    border-radius: 0.375rem;
  }
  
  /* 移动端数学公式优化 */
  @media (max-width: 640px) {
    .katex-display {
      max-height: 60vh;
      padding: 0.75rem;
      margin: 0.75rem 0;
      font-size: 0.9em;
    }
    
    /* 移动端首行缩进适配 */
    p {
      text-indent: 1.5em; /* 移动端略微减少缩进 */
      line-height: 1.7;
    }
  }
  
  a.reference {
    @apply text-xs before:content-["["] after:content-["]"];
  }
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in;
  }
  .react-transform-wrapper {
    @apply w-auto h-auto;
  }
  
  /* 🔥 章节编辑按钮样式 - 鼠标悬停显示版本 */
  .chapter-edit-button-fixed {
    opacity: 0; /* 默认隐藏 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-size: 0.75rem; /* 调小字体 */
    font-weight: 500;
    height: 1.5rem; /* 稍微降低高度 */
    padding: 0 0.375rem; /* 左右padding适应文字 */
    background-color: rgb(239, 246, 255); /* 淡蓝色背景 */
    border: 1px solid rgb(147, 197, 253); /* 蓝色边框 */
    color: rgb(59, 130, 246); /* 蓝色文字 */
    margin-left: 0.5rem;
    white-space: nowrap; /* 防止文字换行 */
    cursor: pointer;
    transition: all 0.2s ease;
  }

  /* 鼠标悬停在标题容器上时显示编辑按钮 */
  .chapter-title-container:hover .chapter-edit-button-fixed {
    opacity: 1;
  }
  
  .chapter-edit-button-fixed:hover {
    background-color: rgb(219, 234, 254);
    border-color: rgb(96, 165, 250);
    color: rgb(37, 99, 235);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }
  
  .dark .chapter-edit-button-fixed {
    background-color: rgb(30, 58, 138);
    border-color: rgb(59, 130, 246);
    color: rgb(147, 197, 253);
  }
  
  .dark .chapter-edit-button-fixed:hover {
    background-color: rgb(37, 99, 235);
    border-color: rgb(96, 165, 250);
    color: rgb(219, 234, 254);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  }
  
  /* 章节标题容器样式 - 支持悬停显示编辑按钮 */
  .chapter-title-container {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  &.mermaid-view {
    .react-transform-wrapper {
      @apply w-auto;
      min-height: 20rem;
      max-height: 80vh;
      height: auto;
    }
    
    /* 移动端优化 */
    @media (max-width: 640px) {
      .react-transform-wrapper {
        min-height: 16rem;
        max-height: 70vh;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
