import * as React from "react";

// 简化的cn函数
function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary" | "destructive" | "outline";
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = "default", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          variant === "default" && "border-transparent bg-blue-600 text-white hover:bg-blue-700",
          variant === "secondary" && "border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",
          variant === "destructive" && "border-transparent bg-red-600 text-white hover:bg-red-700",
          variant === "outline" && "text-gray-900 border-gray-300",
          className
        )}
        {...props}
      />
    );
  }
);
Badge.displayName = "Badge";

export { Badge };