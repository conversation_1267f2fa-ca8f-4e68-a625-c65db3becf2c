events {
    worker_connections 1024;
}

http {
    # 自定义 MIME 类型
    types {
        text/html                             html htm shtml;
        text/css                              css;
        text/xml                              xml;
        image/gif                             gif;
        image/jpeg                            jpeg jpg;
        image/png                             png;
        image/svg+xml                         svg svgz;
        image/webp                            webp;
        application/javascript                js mjs;  # 添加 .mjs 支持
        application/json                      json;
        font/woff                             woff;
        font/woff2                            woff2;
        font/ttf                              ttf;
        font/eot                              eot;
    }
    
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        
        # 设置根目录
        root D:/nginx-1.21.6/html/deepresearch;
        index index.html;

        # 处理静态文件
        location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # SPA 路由处理
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 安全头部
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
} 