/**
 * 集成长程上下文管理的深度研究Hook
 * 在原有分章节写作基础上增加上下文记忆和注入功能
 */

import { useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { streamText } from "ai";
import { useTaskStore } from "@/store/task";
import { useKnowledgeStore } from "@/store/knowledge";
import { useHistoryStore } from "@/store/history";
import useModelProvider from "@/hooks/useAiProvider";
import { LongContextManager } from "@/utils/deep-research/long-context-manager";
import { 
  generateSubChapterPrompt,
  getSubChapterCount,
  getSubChapterTitle,
  getChapterMainTitle,
  outputGuidelinesPrompt,
} from "@/constants/prompts";
import { writeReportTitlePrompt } from "@/utils/deep-research/prompts";
import { fixFormat, removeThinkTags } from "@/utils/formatFixer";
import { ThinkTagStreamProcessor } from "@/utils/text";
import { validateContentWithAI, isChapterValidationEnabled, isFullTextValidationEnabled } from "@/utils/aiValidator";
import { validateContentWithDualModel, isDualModelValidationEnabled, isValidationNetworkingEnabled } from "@/utils/dualModelValidator";

// 原生JS实现pick函数
function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

// 添加响应语言提示函数
function getResponseLanguagePrompt() {
  return `**全部使用简体中文返回**`;
}

// 添加系统提示函数
function getSystemPrompt() {
  return `You are a professional investment analyst with deep expertise in financial analysis, market research, and strategic planning.`;
}

// 使用原有的Hook逻辑，但集成长程上下文管理
export function useDeepResearchWithContext() {
  const [status, setStatus] = useState<string>("");
  const [id, setId] = useState<string | null>(null);
  const contextManagerRef = useRef<LongContextManager | null>(null);
  const { createModelProvider, getModel } = useModelProvider();
  const { t } = useTranslation();

  // 初始化上下文管理器
  const initializeContextManager = (reportTitle: string) => {
    if (!contextManagerRef.current) {
      contextManagerRef.current = new LongContextManager();
    }
    contextManagerRef.current.initializeNewReport(reportTitle);
  };

  // 上下文感知的报告构建器
  class ContextAwareReportBuilder {
    private content = '';
    private currentChapterContent = '';
    private updateCallback: (content: string) => void;
    private updateTimer: NodeJS.Timeout | null = null;

    constructor(onUpdate: (content: string) => void) {
      this.updateCallback = onUpdate;
    }

    append(text: string) {
      this.content += text;
      this.scheduleUpdate();
    }

    updateCurrentChapter(chapterContent: string) {
      this.currentChapterContent = chapterContent;
      this.scheduleRealTimeUpdate();
    }

    private scheduleRealTimeUpdate() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
      this.updateTimer = setTimeout(() => {
        const fullContent = this.content + this.currentChapterContent;
        this.updateCallback(fullContent);
      }, 200); // 200ms 防抖
    }

    private scheduleUpdate() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
      this.updateTimer = setTimeout(() => {
        this.updateCallback(this.content);
      }, 100);
    }

    toString() {
      return this.content;
    }

    destroy() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
    }
  }

  /**
   * 增强版的写作最终报告函数，集成长程上下文管理
   * 🔥 修改：支持大纲驱动的分章节写作
   */
  async function writeFinalReportWithContext() {
    const {
      reportPlan,
      tasks: learnings,
      sources,
      resources,
      requirement,
      researchOutline,
      question,
    } = useTaskStore.getState();
    
    const originalQuestion = question;
    
    let effectiveRequirement = requirement;
    if (!requirement && question) {
      effectiveRequirement = question;
      useTaskStore.getState().setRequirement(question);
    }
    
    const { save } = useHistoryStore.getState();
    const { thinkingModel } = getModel();

    try {
      setStatus("开始生成具有上下文记忆的报告...");

      // 创建内存优化器和标签处理器
      const memoryOptimizer = new MemoryOptimizer();
      const thinkTagStreamProcessor = new ThinkTagStreamProcessor();

      // 报告构建器
      const reportBuilder = new ContextAwareReportBuilder((content) => {
        useTaskStore.getState().updateFinalReport(content);
      });

      // 初始化清理
      useTaskStore.getState().updateFinalReport("");
      useTaskStore.getState().setSources([]);

      // 首先生成报告标题（参考原版方式）
      setStatus(t("research.common.writingTitle"));
      const { generateText } = await import("ai");
      
      const titleResult = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          writeReportTitlePrompt(reportPlan, learnings.map(task => task.learning || ''), question, requirement),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
      });
      
      // 清理标题（参考原版的处理方式）
      const rawTitle = titleResult.text.trim();
      const cleanedTitleResult = removeThinkTags(rawTitle);
      const cleanTitle = cleanedTitleResult.content.trim();
      
      // 设置标题到store
      useTaskStore.getState().setTitle(cleanTitle);
      
      // 设置基础内容（参考原版useDeepResearch的格式）
      const baseContent = `# ${cleanTitle}\n\n` +
        `**报告日期：** ${new Date().toLocaleDateString('zh-CN')}\n\n` +
        `**目标读者：** 投资决策机构\n\n` +
        `**测试模式：** 仅生成第三章 - 项目公司的管理团队\n\n` +
        `---\n\n`;
      
      reportBuilder.append(baseContent);

      // 初始化上下文管理器
      initializeContextManager(cleanTitle);

      // 存储各章节内容
      const chapterContents: { [key: number]: string[] } = {};
      const completedChapters: { [key: number]: string } = {};

      // 🔥 修改：支持大纲驱动的分章节写作
      if (researchOutline && researchOutline.chapters.length > 0) {
        // 使用用户自定义大纲
        const enabledChapters = researchOutline.chapters.filter((chapter: any) => chapter.enabled);
        
        for (let i = 0; i < enabledChapters.length; i++) {
          const chapter = enabledChapters[i];
          const chapterIndex = researchOutline.chapters.findIndex((c: any) => c.id === chapter.id);
          
          chapterContents[chapterIndex] = [];

          // 添加章节标题
          const chapterTitleContent = `\n\n## 第${chapterIndex + 1}章：${chapter.title}\n\n`;
          reportBuilder.append(chapterTitleContent);

          setStatus(`正在生成第${chapterIndex + 1}章：${chapter.title}... (${i + 1}/${enabledChapters.length})`);

          // 🔥 关键：获取章节特定的研究内容
          const chapterSpecificTasks = learnings.filter((task: any) => 
            task.query && (
              task.query.includes(`[第${chapterIndex + 1}章:`) ||
              task.query.includes(`第${chapterIndex + 1}章`) ||
              task.query.includes(chapter.title)
            )
          );
          
          const relevantTasks = chapterSpecificTasks.length > 0 ? chapterSpecificTasks : learnings;
          const chapterLearnings = relevantTasks.map((task: any) => task.learning || '');

          // 构建章节特定的提示词
          const chapterPrompt = `
请为研究报告撰写第${chapterIndex + 1}章内容：

**章节标题：** ${chapter.title}
**章节描述：** ${chapter.description}

**研究要点：**
${chapter.researchPoints.map((point: string) => `- ${point}`).join('\n')}

**字数要求：** ${chapter.wordCount.min}-${chapter.wordCount.max}字

**章节特定研究内容：**
${chapterLearnings.length > 0 ? chapterLearnings.join('\n\n---\n\n') : '使用通用研究内容，请重点关注与本章节相关的信息'}

**报告计划：**
${reportPlan}

**研究需求：**
${effectiveRequirement}

**写作要求：**
1. 严格按照章节大纲结构组织内容
2. 重点使用与本章节相关的研究发现
3. 确保内容与章节标题和描述高度匹配
4. 保持逻辑清晰，论证充分
5. 字数控制在${chapter.wordCount.min}-${chapter.wordCount.max}字范围内
6. 使用专业的学术写作风格
`;

          // 添加上下文信息（如果有已完成的章节）
          let enhancedPrompt = chapterPrompt;
          if (contextManagerRef.current && Object.keys(completedChapters).length > 0) {
            enhancedPrompt = contextManagerRef.current.enhanceSubChapterPrompt(
              chapterIndex,
              0, // 子章节索引，这里简化为0
              chapterPrompt
            );
          }

          // 生成章节内容  
          const chapterResult = streamText({
            model: await createModelProvider(thinkingModel),
            system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
            prompt: [enhancedPrompt, getResponseLanguagePrompt()].join("\n\n"),
            onError: (error) => {
              console.error("生成章节时出错:", error);
              throw error;
            },
          });

          let chapterContent = "";
          let reasoning = "";
          
          // 处理流式输出
          for await (const part of chapterResult.fullStream) {
            if (part.type === "text-delta") {
              thinkTagStreamProcessor.processChunk(
                part.textDelta,
                (data) => {
                  chapterContent += data;
                  reportBuilder.updateCurrentChapter(chapterContent + "\n\n");
                },
                (data) => {
                  reasoning += data;
                }
              );
            } else if (part.type === "reasoning") {
              reasoning += part.textDelta;
            }
          }

          // 格式化章节内容
          const formattedChapter = fixFormat(chapterContent, {
            fixTitles: false,
            fixBold: true,
            fixSpacing: false,
            fixCitations: false,
            maxBoldPerParagraph: 2
          }).content;

          // 存储章节内容
          chapterContents[chapterIndex] = [formattedChapter];
          completedChapters[chapterIndex] = formattedChapter;
          
          // 添加到报告构建器
          reportBuilder.append(formattedChapter + "\n\n");

          // 清空当前章节内容，避免重复显示
          reportBuilder.updateCurrentChapter("");

          // 使用长程上下文管理器处理章节完成
          if (contextManagerRef.current) {
            try {
              const modelProvider = await createModelProvider(thinkingModel);
              await contextManagerRef.current.processChapterCompletion(
                chapterIndex,
                chapter.title,
                formattedChapter,
                modelProvider
              );
            } catch (error) {
              console.warn(`处理第${chapterIndex + 1}章摘要提取时出错:`, error);
            }
          }

          // 重置状态
          thinkTagStreamProcessor.end();
          reasoning = "";
          chapterContent = "";
          memoryOptimizer.scheduleCleanup();

          // 章节间延迟
          if (i < enabledChapters.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      } else {
        // 🔥 降级：如果没有大纲，使用原来的固定章节结构
        for (let chapterNum = 3; chapterNum <= 3; chapterNum++) {
          const subChapterCount = getSubChapterCount(chapterNum);
          const chapterMainTitle = getChapterMainTitle(chapterNum);
          
          chapterContents[chapterNum] = [];

          // 添加章节标题
          const chineseNumber = ['一', '二', '三', '四', '五', '六'][chapterNum - 1];
          const chapterTitleContent = `\n\n## ${chineseNumber}、${chapterMainTitle}\n\n`;
          reportBuilder.append(chapterTitleContent);

        // 生成子章节
        for (let sectionIndex = 0; sectionIndex < subChapterCount; sectionIndex++) {
          const subChapterTitle = getSubChapterTitle(chapterNum, sectionIndex);
          
          setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：${subChapterTitle}...`);

          // 获取上下文增强的提示词
          let enhancedPrompt = generateSubChapterPrompt(
            chapterNum,
            sectionIndex,
            reportPlan,
            learnings.map(task => task.learning || ''),
            sources.map((item) => pick(item, ["title", "url"])),
            resources,
            [], // images - 暂时使用空数组
            effectiveRequirement
          );

          // 添加上下文信息
          if (contextManagerRef.current && Object.keys(completedChapters).length > 0) {
            // 使用长程上下文管理器增强提示词
            enhancedPrompt = contextManagerRef.current.enhanceSubChapterPrompt(
              chapterNum,
              sectionIndex,
              enhancedPrompt
            );
          }

          // 生成子章节内容  
          const subChapterResult = streamText({
            model: await createModelProvider(thinkingModel),
            system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
            prompt: [enhancedPrompt, getResponseLanguagePrompt()].join("\n\n"),
            onError: (error) => {
              console.error("生成子章节时出错:", error);
              throw error;
            },
          });

          let subChapterContent = "";
          let reasoning = "";
          
          // 处理流式输出（使用ThinkTagStreamProcessor）
          for await (const part of subChapterResult.fullStream) {
            if (part.type === "text-delta") {
              thinkTagStreamProcessor.processChunk(
                part.textDelta,
                (data) => {
                  subChapterContent += data;
                  reportBuilder.updateCurrentChapter(subChapterContent + "\n\n");
                },
                (data) => {
                  reasoning += data;
                }
              );
            } else if (part.type === "reasoning") {
              reasoning += part.textDelta;
            }
          }

          // 格式化子章节内容（不处理引用格式，留到最后统一处理）
          const formattedSubChapter = fixFormat(subChapterContent, {
            fixTitles: false,  // 🔥 禁用标题修复，避免与手动添加的章节标题冲突
            fixBold: true,
            fixSpacing: false,
            fixCitations: false,  // 不在子章节阶段处理引用格式，提高效率
            maxBoldPerParagraph: 2
          }).content;

          // 🔥 不在子章节阶段处理本地资源引用，留到最后统一处理
          const processedSubChapter = formattedSubChapter;

          // 🔥 新增：章节级AI验证（智能上下文分章模式）
          let validatedSubChapter = processedSubChapter;
          
          // 优先使用双模型验证，如果未启用则使用单模型验证
          if (isDualModelValidationEnabled()) {
            const networkingStatus = isValidationNetworkingEnabled() ? "（联网搜索已启用）" : "（仅本地验证）";
            setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：🔍 验证模型正在检查内容...${networkingStatus}`);
            
            // 🔥 修复：准备本地知识库内容，正确处理拆分文件并去重
            const knowledgeStore = useKnowledgeStore.getState();
            const addedKnowledgeIds = new Set<string>();
            const validKnowledges: Knowledge[] = [];
            
            for (const item of resources) {
              if (item.status === "completed") {
                let resource = knowledgeStore.get(item.id);
                
                // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
                if (!resource) {
                  // 方式1：ID包含下划线，提取原始ID
                  if (item.id.includes('_')) {
                    const originalId = item.id.split('_')[0];
                    resource = knowledgeStore.get(originalId);
                  }
                  
                  // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
                  if (!resource && item.name.match(/-\d+\./)) {
                    const originalFileName = item.name.replace(/-\d+\./, '.');
                    resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
                  }
                }
                
                // 去重逻辑
                if (resource && !addedKnowledgeIds.has(resource.id)) {
                  validKnowledges.push(resource);
                  addedKnowledgeIds.add(resource.id);
                }
              }
            }
            
            const localKnowledge = validKnowledges
              .map(resource => `${resource.title}:\n${resource.content}`)
              .join('\n\n---\n\n');
            
            try {
              const dualValidationResult = await validateContentWithDualModel(processedSubChapter, {
                mode: 'chapter',
                localKnowledge: localKnowledge,
                chapterContext: `智能上下文分章模式 - 第${chapterNum}章第${sectionIndex + 1}节：${subChapterTitle}`,
                originalWritingModel: thinkingModel // 🔥 传递第一次写作时使用的模型
              });
              
              if (dualValidationResult.hasChanges) {
                validatedSubChapter = dualValidationResult.validatedContent;

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✏️ 写作模型正在应用建议...`);
                await new Promise(resolve => setTimeout(resolve, 800));
                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 双模型验证完成，内容已优化`);
                await new Promise(resolve => setTimeout(resolve, 800));
              } else {

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 双模型验证完成，未发现问题`);
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            } catch (error) {
              console.warn('双模型验证失败，使用原内容:', error);
              setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：⚠️ 双模型验证失败，使用原内容`);
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          } else if (isChapterValidationEnabled()) {
            setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：🔍 正在验证内容准确性...`);
            
            // 🔥 修复：准备本地知识库内容，正确处理拆分文件并去重
            const knowledgeStore = useKnowledgeStore.getState();
            const addedKnowledgeIds = new Set<string>();
            const validKnowledges: Knowledge[] = [];
            
            for (const item of resources) {
              if (item.status === "completed") {
                let resource = knowledgeStore.get(item.id);
                
                // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
                if (!resource) {
                  // 方式1：ID包含下划线，提取原始ID
                  if (item.id.includes('_')) {
                    const originalId = item.id.split('_')[0];
                    resource = knowledgeStore.get(originalId);
                  }
                  
                  // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
                  if (!resource && item.name.match(/-\d+\./)) {
                    const originalFileName = item.name.replace(/-\d+\./, '.');
                    resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
                  }
                }
                
                // 去重逻辑
                if (resource && !addedKnowledgeIds.has(resource.id)) {
                  validKnowledges.push(resource);
                  addedKnowledgeIds.add(resource.id);
                }
              }
            }
            
            const localKnowledge = validKnowledges
              .map(resource => `${resource.title}:\n${resource.content}`)
              .join('\n\n---\n\n');
            
            try {
              const validationResult = await validateContentWithAI(processedSubChapter, {
                mode: 'chapter',
                localKnowledge: localKnowledge,
                chapterContext: `智能上下文分章模式 - 第${chapterNum}章第${sectionIndex + 1}节：${subChapterTitle}`
              });
              
              if (validationResult.hasChanges) {
                validatedSubChapter = validationResult.validatedContent;

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 验证完成，准确性已提升`);
                await new Promise(resolve => setTimeout(resolve, 800));
              } else {

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 验证完成，未发现问题`);
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            } catch (error) {
              console.warn('章节验证失败，使用原内容:', error);
              setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：⚠️ 验证失败，使用原内容`);
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }

          // 存储子章节内容
          chapterContents[chapterNum].push(validatedSubChapter);
          
          // 添加到报告构建器
          reportBuilder.append(validatedSubChapter + "\n\n");

          // 🔥 修复：清空当前章节内容，避免流式更新重复显示
          reportBuilder.updateCurrentChapter("");

          // 重置ThinkTagStreamProcessor状态
          thinkTagStreamProcessor.end();
          
          // 内存优化
          reasoning = "";
          subChapterContent = "";
          memoryOptimizer.scheduleCleanup();

          // 子章节间延迟
          if (sectionIndex < subChapterCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        // 章节完成后，合并所有子章节内容并提取摘要
        const fullChapterContent = chapterContents[chapterNum].join("\n\n");
        completedChapters[chapterNum] = fullChapterContent;

        // 使用长程上下文管理器处理章节完成
        if (contextManagerRef.current) {
          try {
            const modelProvider = await createModelProvider(thinkingModel);
            await contextManagerRef.current.processChapterCompletion(
              chapterNum,
              chapterMainTitle,
              fullChapterContent,
              modelProvider
            );
          } catch (error) {
            console.warn(`处理第${chapterNum}章摘要提取时出错:`, error);
          }
        }

        // 章节间延迟（测试模式下只有一章，无需延迟）
        // if (chapterNum < 6) {
        //   await new Promise(resolve => setTimeout(resolve, 1000));
        // }
        }
      }

      // 在所有章节完成后，统一添加本地资源引用
      await addLocalResourceReferences(reportBuilder, resources);

      // 🔥 移除参考文献部分，避免文章尾部多余内容
      // 网络资源引用已通过正文中的引用格式处理，无需额外添加参考文献列表

      // 设置sources
      useTaskStore.getState().setSources(sources);
      
      // 保存
      const id = save(useTaskStore.getState().backup());
      setId(id);

      // 获取当前内容并进行最终格式处理
      const currentContent = reportBuilder.toString();
      
      // 页面级格式自动纠正（参考原版）

      setStatus("🔧 正在优化报告格式和引用格式...");
      const formatFixResult = fixFormat(currentContent, {
        fixTitles: false,  // 🔥 禁用标题修复，避免重复添加章节标题
        fixBold: false,  // 子章节已经处理过粗体了
        fixSpacing: true, // 最终统一处理段落间距
        fixCitations: true, // 🔥 改为在最后统一处理引用格式，提高效率
        maxBoldPerParagraph: 2
      });
      
      // 🔥 统一处理本地资源引用，为引用添加可点击链接
      let finalContent = await processLocalResourceCitations(formatFixResult.content, resources);

      // 🔥 新增：全文级AI验证（智能上下文分章模式）
      // 优先使用双模型验证，如果未启用则使用单模型验证
      // 注意：双模型验证也需要遵循全文验证开关的设置
      if (isDualModelValidationEnabled() && isFullTextValidationEnabled()) {
        const networkingStatus = isValidationNetworkingEnabled() ? "（联网搜索已启用）" : "（仅本地验证）";
        setStatus(`🔍 验证模型正在检查全文逻辑一致性...${networkingStatus}`);
        
        try {
          // 准备章节上下文信息
          const chapterContext = Object.keys(completedChapters)
            .map(chapterNum => `第${chapterNum}章: ${getChapterMainTitle(parseInt(chapterNum))}`)
            .join('\n');
          
          const dualFullTextValidationResult = await validateContentWithDualModel(finalContent, {
            mode: 'fulltext',
            chapterContext: `智能上下文分章模式 - 完整报告\n报告标题: ${cleanTitle}\n${chapterContext}`,
            originalWritingModel: thinkingModel // 🔥 传递第一次写作时使用的模型
          });
          
          if (dualFullTextValidationResult.hasChanges) {
            finalContent = dualFullTextValidationResult.validatedContent;

            setStatus("✏️ 写作模型正在应用全文优化建议...");
            await new Promise(resolve => setTimeout(resolve, 1000));
            setStatus("✅ 双模型全文验证完成，逻辑一致性已提升");
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus("✅ 双模型全文验证完成，逻辑一致性良好");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('双模型全文验证失败，使用原内容:', error);
          setStatus("⚠️ 双模型全文验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } else if (isFullTextValidationEnabled()) {
        setStatus("🔍 正在进行全文验证，检查逻辑一致性...");
        
        try {
          // 准备章节上下文信息
          const chapterContext = Object.keys(completedChapters)
            .map(chapterNum => `第${chapterNum}章: ${getChapterMainTitle(parseInt(chapterNum))}`)
            .join('\n');
          
          const fullTextValidationResult = await validateContentWithAI(finalContent, {
            mode: 'fulltext',
            chapterContext: `智能上下文分章模式 - 完整报告\n报告标题: ${cleanTitle}\n${chapterContext}`
          });
          
          if (fullTextValidationResult.hasChanges) {
            finalContent = fullTextValidationResult.validatedContent;

            setStatus("✅ 全文验证完成，逻辑一致性已提升");
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus("✅ 全文验证完成，逻辑一致性良好");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('全文验证失败，使用原内容:', error);
          setStatus("⚠️ 全文验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 生成一致性报告（调试信息）
      if (contextManagerRef.current) {
        const consistencyReport = contextManagerRef.current.generateConsistencyReport();

        // 如果一致性评分较低，在控制台输出建议
        if (consistencyReport.overallScore < 70) {
          console.warn("报告一致性评分较低:", consistencyReport.overallScore);
          console.warn("发现的问题:", consistencyReport.issues);
        }
      }

      // 最终完整更新
      useTaskStore.getState().updateFinalReport(finalContent);

      setStatus("报告生成完成！已集成长程上下文管理。");
      
      // 清理资源
      memoryOptimizer.destroy();
      reportBuilder.destroy();

      if (originalQuestion) {
        useTaskStore.getState().setQuestion(originalQuestion);
      }

      return finalContent;

    } catch (error) {
      console.error("生成报告时出错:", error);
      setStatus(`生成报告失败: ${error}`);
      throw error;
    }
  }

  /**
   * 获取上下文管理统计信息
   */
  const getContextStats = () => {
    return contextManagerRef.current?.getContextStats() || {
      totalChapters: 0,
      totalSections: 0,
      averageContextLength: 0,
      consistencyScore: 0
    };
  };

  /**
   * 获取章节关联分析
   */
  const getChapterConnectionAnalysis = () => {
    return contextManagerRef.current?.getChapterConnectionAnalysis() || {};
  };

  /**
   * 导出上下文数据
   */
  const exportContextData = () => {
    return contextManagerRef.current?.exportContextData() || {};
  };

  /**
   * 清理上下文缓存
   */
  const clearContextCache = () => {
    contextManagerRef.current?.clearContext();
  };

  // 内存优化器类（参考原版）
  class MemoryOptimizer {
    private gcTimer: NodeJS.Timeout | null = null;

    scheduleCleanup() {
      if (this.gcTimer) {
        clearTimeout(this.gcTimer);
      }
      this.gcTimer = setTimeout(() => {
        this.performCleanup();
      }, 5000);
    }

    private performCleanup() {
      if (typeof global !== 'undefined' && global.gc) {
        global.gc();
      }
    }

    destroy() {
      if (this.gcTimer) {
        clearTimeout(this.gcTimer);
        this.gcTimer = null;
      }
    }
  }

  /**
   * 🔥 新增：大纲驱动的上下文感知写作
   */
  async function writeOutlineDrivenReportWithContext() {
    const {
      reportPlan,
      tasks: learnings,
      sources,
      resources,
      requirement,
      researchOutline,
    } = useTaskStore.getState();
    
    if (!researchOutline || researchOutline.chapters.length === 0) {
      // 如果没有大纲，降级到普通的上下文写作
      return await writeFinalReportWithContext();
    }

    const { save } = useHistoryStore.getState();
    const { thinkingModel } = getModel();

    try {
      setStatus("开始生成大纲驱动的上下文感知报告...");

      // 创建内存优化器和标签处理器
      const memoryOptimizer = new MemoryOptimizer();
      const thinkTagStreamProcessor = new ThinkTagStreamProcessor();

      // 报告构建器
      const reportBuilder = new ContextAwareReportBuilder((content) => {
        useTaskStore.getState().updateFinalReport(content);
      });

      // 初始化清理
      useTaskStore.getState().updateFinalReport("");
      useTaskStore.getState().setSources([]);

      // 生成报告标题
      setStatus("生成报告标题...");
      const { generateText } = await import("ai");
      
      const titleResult = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          writeReportTitlePrompt(reportPlan, learnings.map(task => task.learning || ''), question, requirement),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
      });
      
      const rawTitle = titleResult.text.trim();
      const cleanedTitleResult = removeThinkTags(rawTitle);
      const cleanTitle = cleanedTitleResult.content.trim();
      
      useTaskStore.getState().setTitle(cleanTitle);
      
      // 设置基础内容
      const baseContent = `# ${cleanTitle}\n\n` +
        `**报告日期：** ${new Date().toLocaleDateString('zh-CN')}\n\n` +
        `**研究大纲：** ${researchOutline.title}\n\n` +
        `**目标读者：** 投资决策机构\n\n` +
        `---\n\n`;
      
      reportBuilder.append(baseContent);

      // 初始化上下文管理器
      initializeContextManager(cleanTitle);

      // 存储各章节内容
      const completedChapters: { [key: number]: string } = {};

      // 🔥 使用用户自定义大纲进行分章节写作
      const enabledChapters = researchOutline.chapters.filter((chapter: any) => chapter.enabled);
      
      for (let i = 0; i < enabledChapters.length; i++) {
        const chapter = enabledChapters[i];
        const chapterIndex = researchOutline.chapters.findIndex((c: any) => c.id === chapter.id);
        
        // 添加章节标题
        const chapterTitleContent = `\n\n## 第${chapterIndex + 1}章：${chapter.title}\n\n`;
        reportBuilder.append(chapterTitleContent);

        setStatus(`正在生成第${chapterIndex + 1}章：${chapter.title}... (${i + 1}/${enabledChapters.length})`);

        // 🔥 关键：获取章节特定的研究内容
        const chapterSpecificTasks = learnings.filter((task: any) => 
          task.query && (
            task.query.includes(`[第${chapterIndex + 1}章:`) ||
            task.query.includes(`第${chapterIndex + 1}章`) ||
            task.query.includes(chapter.title)
          )
        );
        
        const relevantTasks = chapterSpecificTasks.length > 0 ? chapterSpecificTasks : learnings;
        const chapterLearnings = relevantTasks.map((task: any) => task.learning || '');

        // 构建章节特定的提示词
        const chapterPrompt = `
请为研究报告撰写第${chapterIndex + 1}章内容：

**章节标题：** ${chapter.title}
**章节描述：** ${chapter.description}

**研究要点：**
${chapter.researchPoints.map((point: string) => `- ${point}`).join('\n')}

**字数要求：** ${chapter.wordCount.min}-${chapter.wordCount.max}字

**章节特定研究内容：**
${chapterLearnings.length > 0 ? chapterLearnings.join('\n\n---\n\n') : '使用通用研究内容，请重点关注与本章节相关的信息'}

**报告计划：**
${reportPlan}

**研究需求：**
${requirement}

**写作要求：**
1. 严格按照章节大纲结构组织内容
2. 重点使用与本章节相关的研究发现
3. 确保内容与章节标题和描述高度匹配
4. 保持逻辑清晰，论证充分
5. 字数控制在${chapter.wordCount.min}-${chapter.wordCount.max}字范围内
6. 使用专业的学术写作风格
`;

        // 添加上下文信息（如果有已完成的章节）
        let enhancedPrompt = chapterPrompt;
        if (contextManagerRef.current && Object.keys(completedChapters).length > 0) {
          enhancedPrompt = contextManagerRef.current.enhanceSubChapterPrompt(
            chapterIndex,
            0, // 子章节索引，这里简化为0
            chapterPrompt
          );
        }

        // 生成章节内容  
        const chapterResult = streamText({
          model: await createModelProvider(thinkingModel),
          system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
          prompt: [enhancedPrompt, getResponseLanguagePrompt()].join("\n\n"),
          onError: (error) => {
            console.error("生成章节时出错:", error);
            throw error;
          },
        });

        let chapterContent = "";
        let reasoning = "";
        
        // 处理流式输出
        for await (const part of chapterResult.fullStream) {
          if (part.type === "text-delta") {
            thinkTagStreamProcessor.processChunk(
              part.textDelta,
              (data) => {
                chapterContent += data;
                reportBuilder.updateCurrentChapter(chapterContent + "\n\n");
              },
              (data) => {
                reasoning += data;
              }
            );
          } else if (part.type === "reasoning") {
            reasoning += part.textDelta;
          }
        }

        // 格式化章节内容
        const formattedChapter = fixFormat(chapterContent, {
          fixTitles: false,
          fixBold: true,
          fixSpacing: false,
          fixCitations: false,
          maxBoldPerParagraph: 2
        }).content;

        // 存储章节内容
        completedChapters[chapterIndex] = formattedChapter;
        
        // 添加到报告构建器
        reportBuilder.append(formattedChapter + "\n\n");

        // 清空当前章节内容，避免重复显示
        reportBuilder.updateCurrentChapter("");

        // 使用长程上下文管理器处理章节完成
        if (contextManagerRef.current) {
          try {
            const modelProvider = await createModelProvider(thinkingModel);
            await contextManagerRef.current.processChapterCompletion(
              chapterIndex,
              chapter.title,
              formattedChapter,
              modelProvider
            );
          } catch (error) {
            console.warn(`处理第${chapterIndex + 1}章摘要提取时出错:`, error);
          }
        }

        // 重置状态
        thinkTagStreamProcessor.end();
        reasoning = "";
        chapterContent = "";
        memoryOptimizer.scheduleCleanup();

        // 章节间延迟
        if (i < enabledChapters.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 在所有章节完成后，统一添加本地资源引用
      await addLocalResourceReferences(reportBuilder, resources);

      // 设置sources
      useTaskStore.getState().setSources(sources);
      
      // 保存
      const id = save(useTaskStore.getState().backup());
      setId(id);

      // 获取当前内容并进行最终格式处理
      const currentContent = reportBuilder.toString();
      
      setStatus("🔧 正在优化报告格式和引用格式...");
      const formatFixResult = fixFormat(currentContent, {
        fixTitles: false,
        fixBold: false,
        fixSpacing: true,
        fixCitations: true,
        maxBoldPerParagraph: 2
      });
      
      // 统一处理本地资源引用
      const finalContent = await processLocalResourceCitations(formatFixResult.content, resources);

      // 更新最终内容
      useTaskStore.getState().updateFinalReport(finalContent);

      // 清理
      reportBuilder.destroy();
      memoryOptimizer.destroy();

      setStatus("🎉 大纲驱动的上下文感知报告生成完成！");

    } catch (error) {
      console.error("生成大纲驱动报告时出错:", error);
      setStatus(`生成报告失败: ${error}`);
      throw error;
    }
  }

  return {
    // 原有功能
    status,
    id,
    
    // 增强的写作功能
    writeFinalReportWithContext,
    writeOutlineDrivenReportWithContext,
    
    // 上下文管理功能
    getContextStats,
    getChapterConnectionAnalysis,
    exportContextData,
    clearContextCache,
    
    // 上下文管理器实例（用于高级操作）
    contextManager: contextManagerRef.current
  };
}

/**
 * 处理本地资源引用的辅助函数（完全按照原版逻辑）
 */
async function processLocalResourceCitations(content: string, resources: any[]): Promise<string> {
  if (!resources || resources.length === 0) {
    return content;
  }

  // 🔥 移除预检查机制，让 processLocalResourceCitations 专门负责所有本地资源引用处理
  // 现在 fixCitationFormat 不再处理 L- 格式，避免了重复处理问题

  try {
    // 获取配置信息
    const configResponse = await fetch('/api/config');
    let locationKnowledgePath = '';
    
    if (configResponse.ok) {
      const config = await configResponse.json();
      locationKnowledgePath = config.locationKnowledgePath || '';
    }

    if (!locationKnowledgePath) {
      return content; // 如果没有配置路径，返回原内容
    }

    // 确保基础URL以斜杠结尾
    const normalizedBaseUrl = locationKnowledgePath.endsWith('/') 
      ? locationKnowledgePath 
      : locationKnowledgePath + '/';

    // 从拆分的文件名中提取原始文件名的函数
    function getOriginalFileName(fileName: string): string {
      // 处理拆分文件名格式：原始名称-数字.扩展名
      const match = fileName.match(/^(.+)-\d+\.(.+)$/);
      if (match) {
        return `${match[1]}.${match[2]}`; // 返回原始文件名
      }
      return fileName; // 如果不是拆分文件格式，返回原名
    }

    // 首先处理多引用格式 [L-1, L-2, L-9, L-14, L-16]
    let processedContent = content.replace(/\[L-(\d+(?:,\s*L-\d+)*)\]/g, (match: string, citationsStr: string) => {
      // 提取所有的引用编号
      const citations = citationsStr.split(/,\s*/).map((part: string) => part.replace(/L-/, ''));
      
      // 转换每个引用为链接格式
      const processedCitations = citations.map((indexStr: string) => {
        const index = parseInt(indexStr) - 1; // 转换为0基索引
        
        if (index >= 0 && index < resources.length) {
          const resource = resources[index];
          const originalFileName = getOriginalFileName(resource.name);
          const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
          // 🔥 保持L-索引格式显示，链接指向原文件
          return `[[L-${indexStr}]](${resourceUrl})`;
        }
        
        return `[L-${indexStr}]`; // 如果索引无效，保持原格式
      });
      
      // 返回处理后的引用，用逗号分隔
      return processedCitations.join(', ');
    });

    // 再处理单方括号格式 [L-数字]（避免被双方括号处理结果影响，也避免处理已有链接）
    processedContent = processedContent.replace(/(?<!\[)\[L-(\d+)\](?!\]|\()/g, (match: string, indexStr: string) => {
      const index = parseInt(indexStr) - 1; // 转换为0基索引
      
      if (index >= 0 && index < resources.length) {
        const resource = resources[index];
        const originalFileName = getOriginalFileName(resource.name);
        const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);

        // 🔥 保持L-索引格式显示，链接指向原文件
        return `[[L-${indexStr}]](${resourceUrl})`;
      }
      
      // 🔍 调试信息：索引无效的情况
      console.warn(`⚠️ 单方括号本地资源引用索引无效: [L-${indexStr}], 索引=${index}, 资源总数=${resources.length}`);
      return match; // 如果索引无效，保持原样
    });

    // 最后处理双方括号格式 [[L-数字]]
    processedContent = processedContent.replace(/\[\[L-(\d+)\]\](?!\()/g, (match: string, indexStr: string) => {
      const index = parseInt(indexStr) - 1; // 转换为0基索引
      
      if (index >= 0 && index < resources.length) {
        const resource = resources[index];
        const originalFileName = getOriginalFileName(resource.name);
        const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
        // 🔥 保持L-索引格式显示，链接指向原文件
        return `[[L-${indexStr}]](${resourceUrl})`;
      }
      
      // 🔍 调试信息：索引无效的情况
      console.warn(`⚠️ 本地资源引用索引无效: L-${indexStr}, 索引=${index}, 资源总数=${resources.length}`);
      return match; // 如果索引无效，保持原样
    });

    return processedContent;
  } catch (error) {
    console.warn('处理本地资源引用失败:', error);
    return content; // 如果处理失败，返回原内容
  }
}

/**
 * 添加本地资源引用的辅助函数
 */
async function addLocalResourceReferences(
  reportBuilder: any, 
  resources: any[]
): Promise<void> {
  if (resources.length > 0) {
    try {
      // 🔍 添加调试信息

      // 🔥 修复：直接使用TaskStore中的resources信息，不依赖knowledgeStore
      // 因为knowledgeStore中缺少拆分文件的记录，但TaskStore中有完整信息
      const validResources = resources.filter(item => item.status === "completed");

      // 打印每个资源的信息
      validResources.forEach((resource, index) => {

      });
      
      if (validResources.length === 0) {
        console.warn('🚨 没有有效的本地资源可显示！');
        return;
      }
      
      // 获取配置信息
      let locationKnowledgePath = '';
      try {
        const configResponse = await fetch('/api/config');
        if (configResponse.ok) {
          const config = await configResponse.json();
          locationKnowledgePath = config.locationKnowledgePath || '';
        }
      } catch (error) {
        console.warn('Failed to fetch config:', error);
      }
      
      // 从拆分的文件名中提取原始文件名用于链接
      function getOriginalFileName(fileName: string): string {
        // 处理拆分文件名格式：原始名称-数字.扩展名
        const match = fileName.match(/^(.+)-\d+\.(.+)$/);
        if (match) {
          return `${match[1]}.${match[2]}`; // 返回原始文件名
        }
        return fileName; // 如果不是拆分文件格式，返回原名
      }
      
      const localResourcesContent =
        "\n\n---\n\n" +
        `## 已研究 ${validResources.length} 个本地资源\n\n` +
        validResources
          .map((resource, idx) => {
            if (locationKnowledgePath) {
              // 确保基础URL以斜杠结尾
              const normalizedBaseUrl = locationKnowledgePath.endsWith('/') 
                ? locationKnowledgePath 
                : locationKnowledgePath + '/';
              
              const originalFileName = getOriginalFileName(resource.name);
              const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
              // 显示名称使用TaskStore中的文件名，链接指向原始文件
              return `[L-${idx + 1}]: [${resource.name}](${resourceUrl})`;
            } else {
              return `[L-${idx + 1}]: ${resource.name}`;
            }
          })
          .join("\n");
      
      reportBuilder.append(localResourcesContent);
    } catch (error) {
      console.error('添加本地资源引用时出错:', error);
    }
  }
}

// 🔥 已移除 addWebSourceReferences 函数
// 网络资源引用现在通过正文中的引用格式处理，不再需要单独的参考文献列表 