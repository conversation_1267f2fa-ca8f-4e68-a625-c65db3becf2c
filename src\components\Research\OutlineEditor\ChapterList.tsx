"use client";
import { useState } from "react";

import { 
  Plus, 
  Edit2, 
  Trash2, 
  GripVertical, 
  ChevronDown, 
  ChevronRight,
  Eye,
  EyeOff
} from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useOutlineStore } from "@/store/outline";
import { ResearchOutline } from "@/types/outline";
import ChapterEditor from "./ChapterEditor";
import SectionList from "./SectionList";

interface ChapterListProps {
  outline: ResearchOutline;
  showPreview?: boolean;
}

export default function ChapterList({ outline, showPreview = false }: ChapterListProps) {
  const [expandedChapters, setExpandedChapters] = useState<Set<string>>(new Set());
  const [editingChapter, setEditingChapter] = useState<string | null>(null);
  const [showAddChapter, setShowAddChapter] = useState(false);
  
  const outlineStore = useOutlineStore();

  const toggleChapterExpanded = (chapterId: string) => {
    const newExpanded = new Set(expandedChapters);
    if (newExpanded.has(chapterId)) {
      newExpanded.delete(chapterId);
    } else {
      newExpanded.add(chapterId);
    }
    setExpandedChapters(newExpanded);
  };

  const handleAddChapter = () => {
    setShowAddChapter(true);
    setEditingChapter(null);
  };

  const handleEditChapter = (chapterId: string) => {
    setEditingChapter(chapterId);
    setShowAddChapter(false);
  };

  const handleDeleteChapter = (chapterId: string) => {
    if (confirm('确定要删除这个章节吗？')) {
      outlineStore.deleteChapter(chapterId);
    }
  };

  const handleToggleChapterEnabled = (chapterId: string, enabled: boolean) => {
    outlineStore.updateChapter(chapterId, { enabled });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '未知';
    }
  };

  if (showPreview) {
    // 预览模式
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-semibold">大纲预览</h4>
          <Badge variant="outline">预览模式</Badge>
        </div>
        
        <div className="prose prose-slate dark:prose-invert max-w-none">
          <h1>{outline.title}</h1>
          <p className="lead">{outline.description}</p>
          
          {outline.chapters.map((chapter: any) => (
            <div key={chapter.id} className="mb-6">
              <h2>
                {chapter.number}、{chapter.title}
                {!chapter.enabled && <span className="text-gray-400 ml-2">(已禁用)</span>}
              </h2>
              
              <p>{chapter.description}</p>
              
              <div className="text-sm text-gray-600 mb-2">
                                    <span>字数范围: {chapter.wordCount.min}-{chapter.wordCount.max}字</span>
                    <span className="ml-4">优先级: 中</span>
              </div>
              
              {chapter.researchPoints.length > 0 && (
                <div>
                  <h4>研究要点:</h4>
                  <ul>
                    {chapter.researchPoints.map((point: string, index: number) => (
                      <li key={index}>{point}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {chapter.sections.length > 0 && (
                <div>
                  <h4>子章节:</h4>
                  {chapter.sections.map((section: any) => (
                    <div key={section.id} className="ml-4 mb-2">
                      <h5>
                        {section.number}、{section.title}
                        {!section.enabled && <span className="text-gray-400 ml-2">(已禁用)</span>}
                      </h5>
                      <p className="text-sm">{section.description}</p>
                      <div className="text-xs text-gray-500">
                        字数: {section.wordCount.min}-{section.wordCount.max}字
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 编辑模式
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold">章节列表</h4>
        <Button
          variant="outline"
          size="sm"
          onClick={handleAddChapter}
        >
          <Plus className="w-4 h-4 mr-1" />
          添加章节
        </Button>
      </div>

      {/* 添加章节表单 */}
      {showAddChapter && (
        <Card>
          <CardHeader>
            <CardTitle>添加新章节</CardTitle>
          </CardHeader>
          <CardContent>
            <ChapterEditor
              onSave={(chapter) => {
                outlineStore.addChapter(chapter);
                setShowAddChapter(false);
              }}
              onCancel={() => setShowAddChapter(false)}
            />
          </CardContent>
        </Card>
      )}

      {/* 章节列表 */}
      <div className="space-y-3">
        {outline.chapters.map((chapter: any) => (
          <Card key={chapter.id} className={!chapter.enabled ? 'opacity-60' : ''}>
            <Collapsible
              open={expandedChapters.has(chapter.id)}
              onOpenChange={() => toggleChapterExpanded(chapter.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
                    
                    <CollapsibleTrigger asChild>
                      <Button variant="ghost" size="sm">
                        {expandedChapters.has(chapter.id) ? (
                          <ChevronDown className="w-4 h-4" />
                        ) : (
                          <ChevronRight className="w-4 h-4" />
                        )}
                      </Button>
                    </CollapsibleTrigger>
                    
                    <div>
                      <div className="flex items-center space-x-2">
                        <h5 className="font-semibold">
                          第{chapter.number}章、{chapter.title}
                        </h5>
                        <Badge className={getPriorityColor(chapter.priority)}>
                          {getPriorityLabel(chapter.priority)}
                        </Badge>
                        {chapter.sections.length > 0 && (
                          <Badge variant="outline">
                            {chapter.sections.length}个子章节
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {chapter.description}
                      </p>
                      <div className="text-xs text-muted-foreground mt-1">
                        字数: {chapter.wordCount.min}-{chapter.wordCount.max}字
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleChapterEnabled(chapter.id, !chapter.enabled)}
                      title={chapter.enabled ? '禁用章节' : '启用章节'}
                    >
                      {chapter.enabled ? (
                        <Eye className="w-4 h-4" />
                      ) : (
                        <EyeOff className="w-4 h-4" />
                      )}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditChapter(chapter.id)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteChapter(chapter.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CollapsibleContent>
                <CardContent className="pt-0">
                  {editingChapter === chapter.id ? (
                    <ChapterEditor
                      chapter={chapter}
                      onSave={(updatedChapter) => {
                        outlineStore.updateChapter(chapter.id, updatedChapter);
                        setEditingChapter(null);
                      }}
                      onCancel={() => setEditingChapter(null)}
                    />
                  ) : (
                    <div className="space-y-4">
                      {/* 研究要点 */}
                      {chapter.researchPoints.length > 0 && (
                        <div>
                          <h6 className="font-medium mb-2">研究要点:</h6>
                          <ul className="list-disc list-inside space-y-1 text-sm">
                            {chapter.researchPoints.map((point: string, index: number) => (
                              <li key={index}>{point}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {/* 子章节 */}
                      <SectionList 
                        chapterId={chapter.id}
                        sections={chapter.sections}
                      />
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        ))}
      </div>

      {outline.chapters.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">
              还没有章节。点击&ldquo;添加章节&rdquo;开始创建大纲。
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 