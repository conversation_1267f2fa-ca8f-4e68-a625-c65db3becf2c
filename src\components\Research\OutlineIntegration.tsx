"use client";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/Internal/Button";
import { FileText, Edit, Play, Settings, ArrowRight, RotateCcw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useOutlineStore } from "@/store/outline";
import { useTaskStore } from "@/store/task";
import { useOutlineManager } from "@/hooks/useOutlineManager";
import { ResearchOutline } from "@/types/outline";
import OutlineEditor from "./OutlineEditor";

// 安全的日期格式化函数
const formatDate = (date: Date | string): string => {
  try {
    if (date instanceof Date) {
      return isNaN(date.getTime()) ? '无效日期' : date.toLocaleDateString();
    }
    const parsedDate = new Date(date);
    return isNaN(parsedDate.getTime()) ? '无效日期' : parsedDate.toLocaleDateString();
  } catch {
    return '无效日期';
  }
};

interface OutlineIntegrationProps {
  onStartOutlineDrivenResearch?: (outline: ResearchOutline) => void;
}

export default function OutlineIntegration({ onStartOutlineDrivenResearch }: OutlineIntegrationProps) {
  const [showOutlineEditor, setShowOutlineEditor] = useState(false);
  
  const outlineStore = useOutlineStore();
  const taskStore = useTaskStore();
  const { applyOutlineToTask, convertReportPlanToOutline } = useOutlineManager();

  // 初始化内置模板
  // useEffect(() => {
  //   outlineStore.initializeBuiltInTemplates();
  // }, []); // 移除依赖，只在组件挂载时执行一次

  // 检查状态
  const hasReportPlan = taskStore.reportPlan && taskStore.reportPlan.trim().length > 0;
  const hasCurrentOutline = outlineStore.currentOutline !== null;
  const isOutlineDriven = taskStore.isOutlineDriven;

  // 从报告计划创建大纲
  const handleCreateFromReportPlan = async () => {
    if (!hasReportPlan) return;
    
    const outline = await convertReportPlanToOutline(taskStore.reportPlan);
    if (outline) {
      applyOutlineToTask(outline);
      setShowOutlineEditor(true);
    }
  };

  // 应用现有大纲到研究任务
  const handleApplyOutline = (outline: ResearchOutline) => {
    applyOutlineToTask(outline);
    // 不自动开始研究，让用户手动点击"开始研究"按钮
  };

  // 启动大纲驱动的研究流程
  const handleStartOutlineDrivenResearch = () => {
    if (outlineStore.currentOutline && onStartOutlineDrivenResearch) {
      onStartOutlineDrivenResearch(outlineStore.currentOutline);
    }
  };

  // 获取研究进度信息
  const getResearchProgress = () => {
    if (!isOutlineDriven || !outlineStore.currentOutline) {
      return { completed: 0, total: 0, percentage: 0 };
    }

    const totalChapters = outlineStore.currentOutline.chapters.filter((c: any) => c.enabled).length;
    const completedChapters = taskStore.writingProgress?.completedChapters.length || 0;
    // 🔥 修复：使用writingProgress中的totalProgress，如果没有则计算
    const percentage = taskStore.writingProgress?.totalProgress || (totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0);
    return {
      completed: completedChapters,
      total: totalChapters,
      percentage: percentage
    };
  };

  const progress = getResearchProgress();

  return (
    <div className="space-y-6">
      {/* 大纲驱动研究状态卡片 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>大纲驱动研究</span>
            </CardTitle>
            <Badge variant={isOutlineDriven ? "default" : "outline"}>
              {isOutlineDriven ? "已启用" : "未启用"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {isOutlineDriven && outlineStore.currentOutline ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{outlineStore.currentOutline.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    {outlineStore.currentOutline.chapters.length} 个章节
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">
                    {progress.percentage}%
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {progress.completed}/{progress.total} 章节完成
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>研究进度</span>
                  <span>{progress.completed}/{progress.total}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.percentage}%` }}
                  />
                </div>
              </div>

              <div className="flex space-x-2">
                <Button 
                  size="sm" 
                  onClick={handleStartOutlineDrivenResearch}
                  disabled={!outlineStore.currentOutline}
                >
                  <Play className="w-4 h-4 mr-1" />
                  开始研究
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => setShowOutlineEditor(true)}
                >
                  <Settings className="w-4 h-4 mr-1" />
                  编辑大纲
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="font-medium mb-2">启用大纲驱动研究</h4>
              <p className="text-sm text-muted-foreground mb-4">
                使用结构化大纲来指导研究和写作过程，提高效率和质量
              </p>
              
              <div className="flex justify-center space-x-2">
                {hasReportPlan && (
                  <Button onClick={handleCreateFromReportPlan}>
                    <ArrowRight className="w-4 h-4 mr-1" />
                    从报告计划创建大纲
                  </Button>
                )}
                
                <Button 
                  variant="outline"
                  onClick={() => setShowOutlineEditor(true)}
                >
                  <FileText className="w-4 h-4 mr-1" />
                  创建新大纲
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 流程说明和大纲管理 */}
      <Tabs defaultValue="outlines" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="outlines">可用大纲</TabsTrigger>
          <TabsTrigger value="integration">整合设置</TabsTrigger>
        </TabsList>

        {/* 删除流程概览的 TabsContent */}

        <TabsContent value="outlines" className="space-y-4">
          {/* 现有大纲 */}
          {outlineStore.outlines.length > 0 && (
            <div className="space-y-4">
              <h5 className="font-medium text-sm text-muted-foreground">已创建的大纲</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {outlineStore.outlines.map((outline) => (
                  <Card key={outline.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-base">{outline.title}</CardTitle>
                          <p className="text-sm text-muted-foreground mt-1">
                            {outline.description}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {outline.chapters.length} 章节
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-muted-foreground">
                          创建于 {formatDate(outline.createdAt)}
                        </div>
                        <div className="flex space-x-2">
                          <Button 
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              outlineStore.setCurrentOutline(outline);
                              setShowOutlineEditor(true);
                            }}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            编辑
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleApplyOutline(outline)}
                            disabled={taskStore.researchOutline?.id === outline.id}
                          >
                            {taskStore.researchOutline?.id === outline.id ? "已应用" : "应用"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* 可用模板 */}
          {outlineStore.templates.length > 0 && (
            <div className="space-y-4">
              <h5 className="font-medium text-sm text-muted-foreground">
                {outlineStore.outlines.length > 0 ? '可用模板' : '从模板创建大纲'}
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {outlineStore.templates.map((template) => (
                  <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow border-dashed">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-base flex items-center space-x-2">
                            <span>{template.name}</span>
                            {template.isBuiltIn && (
                              <Badge variant="secondary" className="text-xs">内置</Badge>
                            )}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground mt-1">
                            {template.description}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {template.chapters.length} 章节
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-muted-foreground">
                          {template.isBuiltIn ? '系统内置' : `创建于 ${formatDate(template.createdAt)}`}
                        </div>
                        <Button 
                          size="sm"
                          onClick={() => {
                            const newOutline = outlineStore.applyTemplate(template.id);
                            if (newOutline) {
                              handleApplyOutline(newOutline);
                            }
                          }}
                        >
                          <ArrowRight className="w-4 h-4 mr-1" />
                          使用模板
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* 空状态 */}
          {outlineStore.outlines.length === 0 && outlineStore.templates.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-muted-foreground mb-4">
                  还没有可用的大纲或模板。
                </p>
                <Button onClick={() => setShowOutlineEditor(true)}>
                  <FileText className="w-4 h-4 mr-1" />
                  创建新大纲
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="integration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">整合设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h5 className="font-medium">研究模式</h5>
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant={isOutlineDriven ? "outline" : "default"}
                    onClick={() => taskStore.setOutlineDriven(false)}
                  >
                    传统模式
                  </Button>
                  <Button 
                    variant={isOutlineDriven ? "default" : "outline"}
                    onClick={() => taskStore.setOutlineDriven(true)}
                    disabled={!outlineStore.currentOutline}
                  >
                    大纲驱动
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <h5 className="font-medium">当前状态</h5>
                <div className="text-sm space-y-1">
                  <div>研究主题: {taskStore.question || "未设置"}</div>
                  <div>报告计划: {hasReportPlan ? "已创建" : "未创建"}</div>
                  <div>当前大纲: {hasCurrentOutline ? outlineStore.currentOutline?.title : "未选择"}</div>
                  <div>驱动模式: {isOutlineDriven ? "已启用" : "未启用"}</div>
                </div>
              </div>

              {isOutlineDriven && (
                <div className="space-y-2">
                  <h5 className="font-medium">操作</h5>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => taskStore.resetWritingProgress()}
                    >
                      <RotateCcw className="w-4 h-4 mr-1" />
                      重置进度
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 大纲编辑器对话框 */}
      <Dialog open={showOutlineEditor} onOpenChange={setShowOutlineEditor}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>大纲编辑器</DialogTitle>
          </DialogHeader>
          <OutlineEditor 
            onSave={(outline) => {
              applyOutlineToTask(outline);
              setShowOutlineEditor(false);
            }}
            onCancel={() => setShowOutlineEditor(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
} 