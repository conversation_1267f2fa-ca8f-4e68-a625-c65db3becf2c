"use client";
import { useState, useMemo } from "react";
import { Search, Calendar, Building2, Check } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/utils/style";

// 业务附件类型定义
interface BusinessAttachment {
  id: string;
  filename: string;
  businessSystem: string;
  uploadTime: string;
  size: string;
  type: string;
  department: string;
  description?: string;
}

// 模拟的业务系统附件数据
const mockAttachments: BusinessAttachment[] = [
  {
    id: "att_001",
    filename: "2024年度财务报表.xlsx",
    businessSystem: "财务系统",
    uploadTime: "2024-12-20",
    size: "2.3MB",
    type: "excel",
    department: "财务部",
    description: "年度财务汇总报表"
  },
  {
    id: "att_002", 
    filename: "客户需求分析报告.docx",
    businessSystem: "CRM",
    uploadTime: "2024-12-19",
    size: "1.8MB",
    type: "word",
    department: "销售部",
    description: "重要客户需求调研"
  },
  {
    id: "att_003",
    filename: "产品库存明细表.xlsx",
    businessSystem: "ERP",
    uploadTime: "2024-12-18",
    size: "890KB",
    type: "excel", 
    department: "仓储部",
    description: "最新库存数据统计"
  },
  {
    id: "att_004",
    filename: "员工绩效考核表.pdf",
    businessSystem: "OA系统",
    uploadTime: "2024-12-17",
    size: "1.2MB",
    type: "pdf",
    department: "人事部"
  },
  {
    id: "att_005",
    filename: "项目进度跟踪表.xlsx", 
    businessSystem: "项目管理系统",
    uploadTime: "2024-12-16",
    size: "756KB",
    type: "excel",
    department: "项目部",
    description: "Q4项目执行情况"
  },
  {
    id: "att_006",
    filename: "供应商合同模板.docx",
    businessSystem: "合同管理系统",
    uploadTime: "2024-12-15",
    size: "432KB", 
    type: "word",
    department: "采购部"
  },
  {
    id: "att_007",
    filename: "市场调研数据分析.pptx",
    businessSystem: "CRM",
    uploadTime: "2024-12-14",
    size: "3.1MB",
    type: "powerpoint",
    department: "市场部",
    description: "用户行为分析报告"
  },
  {
    id: "att_008",
    filename: "设备维护记录表.xlsx",
    businessSystem: "设备管理系统",
    uploadTime: "2024-12-13",
    size: "623KB",
    type: "excel",
    department: "设备部"
  }
];

// 业务系统选项
const businessSystems = [
  "全部系统",
  "ERP",
  "CRM", 
  "OA系统",
  "财务系统",
  "项目管理系统",
  "合同管理系统",
  "设备管理系统"
];

// 时间筛选选项
const timeFilters = [
  { label: "全部时间", value: "all" },
  { label: "今日", value: "today" },
  { label: "昨日", value: "yesterday" },
  { label: "本周", value: "week" },
  { label: "上周", value: "lastWeek" },
  { label: "本月", value: "month" },
  { label: "上月", value: "lastMonth" },
  { label: "近3个月", value: "last3Months" },
  { label: "今年", value: "thisYear" },
  { label: "去年", value: "lastYear" }
];

// 文件类型文字标识
const getFileTypeText = () => {
  return "txt";
};



interface BusinessAttachmentDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (attachments: BusinessAttachment[]) => void;
}

function BusinessAttachmentDialog({ open, onClose, onConfirm }: BusinessAttachmentDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSystem, setSelectedSystem] = useState("全部系统");
  const [selectedTimeFilter, setSelectedTimeFilter] = useState("all");
  const [selectedAttachments, setSelectedAttachments] = useState<string[]>([]);

  // 筛选逻辑
  const filteredAttachments = useMemo(() => {
    let filtered = mockAttachments;

    // 搜索词筛选
    if (searchTerm.trim()) {
      filtered = filtered.filter(
        (att) =>
          att.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
          att.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
          att.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 业务系统筛选
    if (selectedSystem !== "全部系统") {
      filtered = filtered.filter((att) => att.businessSystem === selectedSystem);
    }

    // 时间筛选
    if (selectedTimeFilter !== "all") {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      filtered = filtered.filter((att) => {
        const attachmentDate = new Date(att.uploadTime);
        
        switch (selectedTimeFilter) {
          case "today":
            return attachmentDate >= today;
          case "yesterday":
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            const dayBefore = new Date(today);
            dayBefore.setDate(today.getDate() - 2);
            return attachmentDate >= dayBefore && attachmentDate < today;
          case "week":
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - 7);
            return attachmentDate >= weekStart;
          case "lastWeek":
            const lastWeekEnd = new Date(today);
            lastWeekEnd.setDate(today.getDate() - 7);
            const lastWeekStart = new Date(today);
            lastWeekStart.setDate(today.getDate() - 14);
            return attachmentDate >= lastWeekStart && attachmentDate < lastWeekEnd;
          case "month":
            const monthStart = new Date(today);
            monthStart.setDate(today.getDate() - 30);
            return attachmentDate >= monthStart;
          case "lastMonth":
            const lastMonthEnd = new Date(today);
            lastMonthEnd.setDate(today.getDate() - 30);
            const lastMonthStart = new Date(today);
            lastMonthStart.setDate(today.getDate() - 60);
            return attachmentDate >= lastMonthStart && attachmentDate < lastMonthEnd;
          case "last3Months":
            const threeMonthsStart = new Date(today);
            threeMonthsStart.setDate(today.getDate() - 90);
            return attachmentDate >= threeMonthsStart;
          case "thisYear":
            const thisYearStart = new Date(now.getFullYear(), 0, 1);
            return attachmentDate >= thisYearStart;
          case "lastYear":
            const lastYearStart = new Date(now.getFullYear() - 1, 0, 1);
            const lastYearEnd = new Date(now.getFullYear(), 0, 1);
            return attachmentDate >= lastYearStart && attachmentDate < lastYearEnd;
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [searchTerm, selectedSystem, selectedTimeFilter]);

  // 获取选中的附件信息
  const selectedAttachmentDetails = useMemo(() => {
    return selectedAttachments.map(id => 
      mockAttachments.find(att => att.id === id)!
    ).filter(Boolean);
  }, [selectedAttachments]);

  // 处理选择/取消选择
  const handleToggleSelection = (attachmentId: string) => {
    setSelectedAttachments(prev => 
      prev.includes(attachmentId)
        ? prev.filter(id => id !== attachmentId)
        : [...prev, attachmentId]
    );
  };

  // 处理确认
  const handleConfirm = () => {
    onConfirm(selectedAttachmentDetails);
    // 重置状态
    setSelectedAttachments([]);
    setSearchTerm("");
    setSelectedSystem("全部系统");
    setSelectedTimeFilter("all");
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    setSelectedAttachments([]);
    setSearchTerm("");
    setSelectedSystem("全部系统");
    setSelectedTimeFilter("all");
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleCancel}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            选择业务系统附件
          </DialogTitle>
          <DialogDescription>
            搜索和选择需要添加到研究资源的业务附件文档
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索文件名、部门或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* 筛选器 */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Building2 className="w-4 h-4 text-gray-500" />
              <Select value={selectedSystem} onValueChange={setSelectedSystem}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择业务系统" />
                </SelectTrigger>
                <SelectContent>
                  {businessSystems.map((system) => (
                    <SelectItem key={system} value={system}>
                      {system}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <Select value={selectedTimeFilter} onValueChange={setSelectedTimeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="时间范围" />
                </SelectTrigger>
                <SelectContent>
                  {timeFilters.map((filter) => (
                    <SelectItem key={filter.value} value={filter.value}>
                      {filter.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 结果统计 */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>找到 {filteredAttachments.length} 个附件</span>
            <span>已选择 {selectedAttachments.length} 个</span>
          </div>

          {/* 文件列表 */}
          <ScrollArea className="h-96 w-full border rounded-md">
            <div className="p-4">
              {filteredAttachments.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  没有找到匹配的附件
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredAttachments.map((attachment) => (
                    <div
                      key={attachment.id}
                      className={cn(
                        "flex items-center p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer",
                        selectedAttachments.includes(attachment.id) && "bg-blue-50 dark:bg-blue-900/20 border-blue-200"
                      )}
                      onClick={() => handleToggleSelection(attachment.id)}
                    >
                      <Checkbox
                        checked={selectedAttachments.includes(attachment.id)}
                        className="mr-3"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-gray-600 dark:text-gray-300">
                            {getFileTypeText()}
                          </span>
                          <span className="font-medium truncate">{attachment.filename}</span>
                          <Badge variant="secondary" className="text-xs">
                            {attachment.businessSystem}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>{attachment.department}</span>
                          <span>{attachment.size}</span>
                          <span>{attachment.uploadTime}</span>
                        </div>
                        
                        {attachment.description && (
                          <p className="text-sm text-gray-500 mt-1">
                            {attachment.description}
                          </p>
                        )}
                      </div>
                      
                      {selectedAttachments.includes(attachment.id) && (
                        <Check className="w-5 h-5 text-blue-600 ml-2" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={selectedAttachments.length === 0}
          >
            确认添加 ({selectedAttachments.length})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default BusinessAttachmentDialog;