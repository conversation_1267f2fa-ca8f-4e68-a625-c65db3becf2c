"use client";
import { useState, useEffect } from "react";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import OutlineEditor from "@/components/Research/OutlineEditor";
import { useOutlineStore } from "@/store/outline";

export default function OutlineTestPage() {
  const [showEditor, setShowEditor] = useState(false);
  const outlineStore = useOutlineStore();

  // 清除本地存储的函数
  const clearStorage = () => {
    localStorage.removeItem('outline-store');
    window.location.reload();
  };

  // 初始化内置模板
  useEffect(() => {
    outlineStore.initializeBuiltInTemplates();
  }, [outlineStore]);

  const handleCreateTestOutline = () => {
    // 创建一个测试大纲
    outlineStore.createOutline(
      "企业管理报告大纲",
      "用于测试大纲编辑器功能的示例大纲"
    );

    // 添加一些测试章节
    outlineStore.addChapter({
      number: 1,
      title: "项目公司的商业模式",
      description: "分析项目公司的核心商业模式和竞争优势",
      wordCount: { min: 3000, max: 4000 },
      researchPoints: [
        "产品的创新性、领先性与核心优势",
        "项目公司的盈利模式、营销策略",
        "目标市场的有效规模",
        "行业情况跟踪"
      ],
      enabled: true,
      priority: 'high',
      sections: []
    });

    outlineStore.addChapter({
      number: 2,
      title: "项目公司的经营状况",
      description: "全面分析项目公司的财务状况和经营效率",
      wordCount: { min: 3500, max: 4500 },
      researchPoints: [
        "财务指标及报表的分析",
        "营销策略执行情况",
        "内部审计情况",
        "资本市场操作空间"
      ],
      enabled: true,
      priority: 'high',
      sections: []
    });

    setShowEditor(true);
  };

  const handleCreateEmptyOutline = () => {
    outlineStore.createOutline("新建大纲", "请输入大纲描述");
    setShowEditor(true);
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">大纲系统测试</h1>
        <p className="text-muted-foreground">
          测试大纲编辑器的各项功能
        </p>
        
        {/* 添加清除存储按钮 */}
        <div className="flex justify-center">
          <Button 
            onClick={clearStorage}
            variant="outline"
            size="sm"
            className="bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
          >
            清除本地存储
          </Button>
        </div>
      </div>

      {!showEditor ? (
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>开始测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button onClick={handleCreateTestOutline} className="h-20">
                <div className="text-center">
                  <div className="font-semibold">创建测试大纲</div>
                  <div className="text-sm opacity-80">包含示例章节</div>
                </div>
              </Button>
              
              <Button variant="outline" onClick={handleCreateEmptyOutline} className="h-20">
                <div className="text-center">
                  <div className="font-semibold">创建空白大纲</div>
                  <div className="text-sm opacity-80">从头开始创建</div>
                </div>
              </Button>
            </div>

            <div className="text-center pt-4">
              <p className="text-sm text-muted-foreground">
                当前已有 {outlineStore.outlines.length} 个大纲，
                {outlineStore.templates.length} 个模板
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">大纲编辑器</h2>
            <Button variant="outline" onClick={() => setShowEditor(false)}>
              返回
            </Button>
          </div>
          
          <OutlineEditor />
        </div>
      )}

      {/* 调试信息 */}
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-base">调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="font-medium">大纲数量</div>
              <div className="text-muted-foreground">{outlineStore.outlines.length}</div>
            </div>
            
            <div>
              <div className="font-medium">模板数量</div>
              <div className="text-muted-foreground">{outlineStore.templates.length}</div>
            </div>
            
            <div>
              <div className="font-medium">当前大纲</div>
              <div className="text-muted-foreground">
                {outlineStore.currentOutline?.title || '无'}
              </div>
            </div>
          </div>
          
          {outlineStore.currentOutline && (
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <div className="font-medium mb-2">当前大纲详情：</div>
              <div className="text-xs space-y-1 mb-2 p-2 bg-yellow-50 rounded">
                <div>创建时间类型: {typeof outlineStore.currentOutline.createdAt}</div>
                <div>更新时间类型: {typeof outlineStore.currentOutline.updatedAt}</div>
                <div>模板数量: {outlineStore.templates.length}</div>
                {outlineStore.templates.length > 0 && (
                  <div>第一个模板创建时间类型: {typeof outlineStore.templates[0].createdAt}</div>
                )}
              </div>
              <pre className="text-xs overflow-auto">
                {JSON.stringify(outlineStore.currentOutline, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 