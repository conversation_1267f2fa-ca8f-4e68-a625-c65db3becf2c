"use client";

import { useState } from 'react';
import { streamText } from 'ai';
import { useTaskStore } from '@/store/task';
import { useOutlineStore } from '@/store/outline';
import useModelProvider from '@/hooks/useAiProvider';
import { ChapterRegenerator } from '@/utils/deep-research/chapter-regenerator';
import { ThinkTagStreamProcessor } from '@/utils/text';
import { getSystemPrompt } from '@/utils/deep-research/prompts';

// 获取响应语言提示词
function getResponseLanguagePrompt() {
  return "Please respond in Chinese (Simplified).";
}

// 错误处理函数
function handleError(error: unknown) {
  console.error('Chapter edit error:', error);
  throw error;
}

export function useChapterEdit() {
  const [isEditing, setIsEditing] = useState(false);
  const [editProgress, setEditProgress] = useState('');
  const [editedContent, setEditedContent] = useState('');
  const [lastChapterInfo, setLastChapterInfo] = useState<any>(null);
  const { createModelProvider, getModel } = useModelProvider();

  const editChapter = async (
    chapterTitle: string,
    editRequirements: string,
    dedicatedResources: Array<{
      id: string;
      title: string;
      type: 'text' | 'file';
      content: string;
    }> = [],
    domExtractedContent?: string // 🔥 新增：DOM提取的原文内容
  ) => {
    if (isEditing) {
      throw new Error('已有章节正在编辑中，请等待完成');
    }

    setIsEditing(true);
    setEditProgress('准备章节编辑...');
    setEditedContent('');

    // 🔥 保存当前研究主题，确保编辑过程中不会丢失
    const originalQuestion = useTaskStore.getState().question;

    try {
      const { finalReport, researchOutline } = useTaskStore.getState();
      const { thinkingModel } = getModel();

      if (!finalReport) {
        throw new Error('未找到报告内容');
      }

      if (!editRequirements.trim()) {
        throw new Error('请提供编辑要求');
      }

      setEditProgress('分析章节结构...');
      
      // 🔥 获取当前大纲的写作配置
      const { getEffectiveWritingConfig } = await import('@/utils/writing-config-helper');
      const currentOutline = useOutlineStore.getState().currentOutline;
      const writingConfig = currentOutline?.writingConfig || getEffectiveWritingConfig();
      
      console.log('📋 章节编辑使用的写作配置:', writingConfig);
      console.log('🎨 编辑时的写作风格:', writingConfig.style);
      console.log('🌍 编辑时的语言要求:', writingConfig.languageRequirements);
      
      // 🔥 添加任务上下文到全局以便ChapterRegenerator访问
      if (typeof window !== 'undefined') {
        (globalThis as any).__taskStore = useTaskStore;
      }
      
      // 🔥 步骤1：使用ChapterRegenerator分析章节并构建提示词，传递大纲数据、DOM内容和写作配置
      const regenerationAnalysis = ChapterRegenerator.analyzeChapterForRegeneration(
        finalReport,
        chapterTitle,
        editRequirements,
        dedicatedResources,
        researchOutline,
        domExtractedContent, // 🔥 传递DOM提取的内容
        writingConfig // 🔥 传递写作配置
      );

      setEditProgress('正在重新生成章节内容...');

      // 🔥 步骤2：真实AI调用 - 使用新的模板式提示词进行流式生成
      const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
      
      const result = streamText({
        model: await createModelProvider(thinkingModel),
        system: [getSystemPrompt()].join('\n\n'),
        prompt: [
          regenerationAnalysis.fullPrompt,
          getResponseLanguagePrompt(),
        ].join('\n\n'),
        onError: handleError,
      });

      let newContent = '';

      // 🔥 步骤3：流式处理AI响应
      for await (const part of result.fullStream) {
        if (part.type === "text-delta") {
          thinkTagStreamProcessor.processChunk(
            part.textDelta,
            (data) => {
              newContent += data;
              setEditedContent(newContent);
              setEditProgress(`正在生成章节内容... (${newContent.length} 字符)`);
            },
            () => {
              // 推理内容处理，暂时忽略
            }
          );
        }
      }

      if (!newContent.trim()) {
        throw new Error('AI生成的内容为空，请重试');
      }

      setEditProgress('内容生成完成，等待用户确认...');
      
      // 保存章节信息供后续确认时使用
      setLastChapterInfo(regenerationAnalysis.chapterInfo);
      
      return {
        success: true,
        updatedReport: null, // 暂不替换，等用户确认
        editedContent: newContent.trim(),
        chapterInfo: regenerationAnalysis.chapterInfo
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setEditProgress(`编辑失败: ${errorMessage}`);
      console.error('Chapter edit failed:', error);
      
      return {
        success: false,
        error: errorMessage,
        updatedReport: null,
        editedContent: '',
        chapterInfo: null
      };
    } finally {
      setIsEditing(false);
      
      // 🔥 确保研究主题不会被清空，即使在编辑过程中发生错误
      if (originalQuestion) {
        useTaskStore.getState().setQuestion(originalQuestion);
      }
      
      // 3秒后清除进度信息
      setTimeout(() => {
        setEditProgress('');
        setEditedContent('');
      }, 3000);
    }
  };

  const confirmReplace = async (content: string) => {
    console.log('🔧 useChapterEdit.confirmReplace 开始');
    console.log('📝 接收到的内容长度:', content?.length || 0);
    console.log('📊 当前章节信息:', lastChapterInfo);
    
    if (!lastChapterInfo || !content.trim()) {
      const error = `缺少必要信息 - 章节信息: ${!!lastChapterInfo}, 内容: ${!!content?.trim()}`;
      console.error('❌', error);
      throw new Error(error);
    }

    // 🔥 保存当前研究主题，确保替换过程中不会丢失
    const originalQuestion = useTaskStore.getState().question;
    console.log('💾 保存原始研究主题:', originalQuestion);

    try {
      const { ChapterReplacer } = await import('@/utils/deep-research/chapter-replacer');
      const { finalReport, researchOutline } = useTaskStore.getState();
      
      console.log('📋 当前报告长度:', finalReport?.length || 0);
      console.log('📋 大纲信息:', researchOutline ? '存在' : '不存在');
      
      if (!finalReport) {
        throw new Error('未找到报告内容');
      }

      // 🔥 确保有大纲信息，如果没有则尝试从outline store获取
      let outline = researchOutline;
      if (!outline) {
        console.log('⚠️ 从任务store中未找到大纲，尝试从outline store获取');
        const { currentOutline } = useOutlineStore.getState();
        outline = currentOutline;
        console.log('📋 从outline store获取的大纲:', outline ? '存在' : '不存在');
      }

      if (!outline) {
        console.warn('⚠️ 未找到大纲信息，将使用简化替换模式');
        // 🔥 如果没有大纲，尝试手动构造章节信息进行简单替换
        return await performManualReplacement(finalReport, lastChapterInfo, content.trim());
      }

      console.log('🔄 调用 ChapterReplacer.replaceChapterContent');
      console.log('   - 原报告长度:', finalReport.length);
      console.log('   - 章节标题:', lastChapterInfo.title);
      console.log('   - 新内容长度:', content.trim().length);
      console.log('   - 章节类型:', lastChapterInfo.type);
      console.log('   - 章节ID:', lastChapterInfo.id);
      
      // 执行章节替换
      const updatedReport = ChapterReplacer.replaceChapterContent(
        finalReport,
        lastChapterInfo,
        content.trim(),
        outline
      );

      console.log('✅ ChapterReplacer 替换完成');
      console.log('   - 替换前长度:', finalReport.length);
      console.log('   - 替换后长度:', updatedReport.length);
      console.log('   - 长度差异:', updatedReport.length - finalReport.length);

      // 🔥 验证替换是否成功
      if (updatedReport === finalReport) {
        console.warn('⚠️ 替换结果与原报告相同，可能替换失败，尝试手动替换');
        return await performManualReplacement(finalReport, lastChapterInfo, content.trim());
      }

            // 🔥 处理引用链接
      console.log('🔗 开始处理引用链接...');
      let finalProcessedReport = updatedReport;
      
      try {
        // 获取任务资源用于引用链接处理
        const { resources, sources } = useTaskStore.getState();
        
        // 🔥 简化的本地资源引用链接处理
        if (resources && resources.length > 0) {
          finalProcessedReport = await processChapterLocalCitations(finalProcessedReport, resources);
          console.log('✅ 本地资源引用链接处理完成');
        }
        
        // 🔥 添加网络来源参考文献（如果有的话）
        if (sources && sources.length > 0) {
          // 检查报告末尾是否已有参考文献
          if (!finalProcessedReport.includes('## 参考文献')) {
            const networkReferences = "\n\n---\n\n## 参考文献\n\n" +
              sources.map((item: any, idx: number) =>
                `[${idx + 1}]: ${item.url}${item.title ? ` "${item.title.replaceAll('"', ' ')}"` : ""}`
              ).join("\n");
            
            finalProcessedReport += networkReferences;
            console.log('✅ 网络来源参考文献添加完成');
          }
        }
        
      } catch (error) {
        console.warn('⚠️ 引用链接处理失败，使用原始内容:', error);
        finalProcessedReport = updatedReport;
      }

      // 更新任务存储中的报告
      const { updateFinalReport } = useTaskStore.getState();
      console.log('💾 更新最终报告到store（已处理引用链接）');
      updateFinalReport(finalProcessedReport);

      // 🔥 确保研究主题不会被清空
      if (originalQuestion) {
        useTaskStore.getState().setQuestion(originalQuestion);
        console.log('🔄 恢复研究主题:', originalQuestion);
      }

      // 🔥 延迟一下再清理状态，确保界面有时间重新渲染章节映射
      setTimeout(() => {
        setLastChapterInfo(null);
        setEditedContent('');
        setEditProgress('');
        console.log("🔄 章节替换完成，状态已清理，界面应重新渲染");
      }, 100);

      console.log('✅ useChapterEdit.confirmReplace 成功完成');
      return { success: true, updatedReport };
    } catch (error) {
      // 🔥 即使出错也要恢复研究主题
      if (originalQuestion) {
        useTaskStore.getState().setQuestion(originalQuestion);
        console.log('🔄 出错时恢复研究主题:', originalQuestion);
      }
      console.error('❌ useChapterEdit.confirmReplace 失败:', error);
      
      // 🔥 尝试手动替换作为备用方案
      try {
        console.log('🔄 尝试手动替换作为备用方案');
        const { finalReport } = useTaskStore.getState();
        if (finalReport && lastChapterInfo) {
          return await performManualReplacement(finalReport, lastChapterInfo, content.trim());
        }
      } catch (manualError) {
        console.error('❌ 手动替换也失败:', manualError);
      }
      
      throw error;
    }
  };

  // 🔥 检测子章节类型和编号
  const detectSubChapterNumber = (title: string): { major: number, minor: number } | null => {
    const match = title.match(/^(\d+)\.(\d+)、/);
    if (match) {
      return {
        major: parseInt(match[1]),
        minor: parseInt(match[2])
      };
    }
    return null;
  };

  // 🔥 通用的下一章节边界搜索（支持各种标题格式）
  const findNextChapterBoundary = (
    text: string, 
    startIndex: number, 
    currentTitle: string
  ): number => {
    console.log(`🔍 搜索下一章节边界，当前章节: "${currentTitle}"`);
    
    // 从指定位置开始搜索
    const searchText = text.substring(startIndex);
    
    // 🔥 通用章节边界搜索模式 - 按优先级排序
    const patterns = [
      // 1. 编号子章节格式 (1.1、1.2、等)
      {
        pattern: /^(\d+\.\d+、[^\n]*)/m,
        type: 'numbered_sub'
      },
      // 2. 简单编号格式 (1、2、等)
      {
        pattern: /^(\d+、[^\n]*)/m,
        type: 'numbered_simple'
      },
      // 3. 主章节标题 (第X章)
      {
        pattern: /^(第\d+章[^\n]*)/m,
        type: 'main_chapter'
      },
      // 4. Markdown标题格式 (##、###等)
      {
        pattern: /^(#{1,6}\s+[^\n]+)/m,
        type: 'markdown_title'
      },
      // 5. 粗体标题格式
      {
        pattern: /^(\*\*[^*\n]+\*\*)/m,
        type: 'bold_title'
      }
    ];

    for (const { pattern, type } of patterns) {
      const match = pattern.exec(searchText);
      if (match) {
        const foundIndex = startIndex + match.index;
        const foundTitle = match[1];
        
        console.log(`✅ 找到${type}类型边界: "${foundTitle}" 在位置 ${foundIndex}`);
        
        // 🔥 验证找到的标题不是当前标题本身
        if (!foundTitle.includes(currentTitle) && foundTitle !== currentTitle) {
          console.log(`📊 验证通过: 找到的边界标题与当前标题不同`);
          return foundIndex;
        } else {
          console.log(`❌ 跳过: 找到的标题与当前标题相同或包含当前标题`);
          continue;
        }
      }
    }

    console.log('⚠️ 未找到合适的章节边界，使用文档结尾');
    return text.length;
  };

  // 🔥 新增：通用的手动替换函数（支持各种章节标题格式）
  const performManualReplacement = async (
    originalReport: string, 
    chapterInfo: any, 
    newContent: string
  ) => {
    console.log('🔧 执行通用手动替换模式');
    console.log(`📝 目标章节: "${chapterInfo.title}"`);
    
    const title = chapterInfo.title;
    const titleIndex = originalReport.indexOf(title);
    
    if (titleIndex === -1) {
      console.error('❌ 未在报告中找到章节标题:', title);
      throw new Error(`未在报告中找到章节标题: ${title}`);
    }

    console.log('🔍 找到章节标题位置:', titleIndex);

    // 🔥 分析章节类型（可选，用于调试）
    const chapterNumber = detectSubChapterNumber(title);
    if (chapterNumber) {
      console.log(`📊 检测到编号子章节: ${chapterNumber.major}.${chapterNumber.minor}`);
    } else {
      console.log(`📝 检测到文本格式章节标题`);
    }

    // 找到章节开始位置（标题行的开始）
    const lineStart = originalReport.lastIndexOf('\n', titleIndex);
    const actualStart = lineStart === -1 ? 0 : lineStart + 1;

    // 🔥 使用通用搜索找到下一个章节边界
    const nextChapterIndex = findNextChapterBoundary(
      originalReport, 
      titleIndex + title.length + 10, // 给一些缓冲距离避免匹配到自己
      title
    );

    console.log('🎯 章节替换范围:', actualStart, '-', nextChapterIndex);
    console.log('📏 原章节长度:', nextChapterIndex - actualStart);
    console.log('📏 新内容长度:', newContent.length);

    // 🔥 确保新内容格式正确
    let finalNewContent = newContent.trim();
    
    // 检查新内容是否已包含正确的章节标题
    if (!finalNewContent.includes(title)) {
      // 如果新内容不包含标题，添加格式化的标题
      if (finalNewContent.startsWith('#')) {
        // 如果已经是 Markdown 标题格式，替换为正确的标题
        finalNewContent = finalNewContent.replace(/^#+\s*.*$/m, `## ${title}`);
      } else {
        // 在内容前添加标题
        finalNewContent = `## ${title}\n\n${finalNewContent}`;
      }
      console.log('✏️ 已为新内容添加/修正标题格式');
    }

    // 确保内容以换行结尾
    if (!finalNewContent.endsWith('\n')) {
      finalNewContent = finalNewContent + '\n';
    }

    // 执行精确替换
    const updatedReport = originalReport.substring(0, actualStart) + 
                         finalNewContent + 
                         '\n' + 
                         originalReport.substring(nextChapterIndex);

    console.log('✅ 通用手动替换完成');
    console.log('   - 替换前长度:', originalReport.length);
    console.log('   - 替换后长度:', updatedReport.length);
    console.log('   - 长度差异:', updatedReport.length - originalReport.length);

    // 🔥 验证替换结果
    const verifyTitle = updatedReport.includes(title);
    if (!verifyTitle) {
      console.error('❌ 替换后章节标题丢失！');
      throw new Error('替换后章节标题验证失败');
    }
    console.log('✅ 章节标题验证通过');

    // 更新任务存储中的报告
    const { updateFinalReport } = useTaskStore.getState();
    console.log('💾 手动替换：更新最终报告到store');
    updateFinalReport(updatedReport);

    // 清理状态
    setTimeout(() => {
      setLastChapterInfo(null);
      setEditedContent('');
      setEditProgress('');
      console.log("🔄 通用手动替换完成，状态已清理");
    }, 100);

    return { success: true, updatedReport };
  };

  return {
    editChapter,
    confirmReplace,
    isEditing,
    editProgress,
    editedContent
  };
}

/**
 * 处理章节中的本地资源引用链接
 */
async function processChapterLocalCitations(content: string, resources: any[]): Promise<string> {
  if (!content || !resources || resources.length === 0) {
    return content;
  }
  
  try {
    // 🔥 配置基础URL（从部署配置获取）
    const normalizedBaseUrl = (() => {
      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        const protocol = window.location.protocol;
        const port = window.location.port;
        
        const baseUrl = port ? `${protocol}//${hostname}:${port}` : `${protocol}//${hostname}`;
        return `${baseUrl}/uploads/`;
      }
      return '/uploads/';
    })();
    
         // 🔥 获取原始文件名的函数
     const getOriginalFileName = (fileName: string): string => {
       const cleanName = fileName.replace(/[^\w\-_.]/g, '_');
       
       if (cleanName.includes('_') && cleanName.match(/^\d{13}_/)) {
         return cleanName.replace(/^\d{13}_[a-z0-9]{6}_/, '');
       }
       
       return cleanName;
     };

    // 首先处理多引用格式 [L-1, L-2, L-9, L-14, L-16]
    let processedContent = content.replace(/\[L-(\d+(?:,\s*L-\d+)*)\]/g, (match: string, citationsStr: string) => {
      // 提取所有的引用编号
      const citations = citationsStr.split(/,\s*/).map((part: string) => part.replace(/L-/, ''));
      
      // 转换每个引用为链接格式
      const processedCitations = citations.map((indexStr: string) => {
        const index = parseInt(indexStr) - 1; // 转换为0基索引
        
        if (index >= 0 && index < resources.length) {
          const resource = resources[index];
          const originalFileName = getOriginalFileName(resource.name);
          const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
          // 🔥 保持L-索引格式显示，链接指向原文件
          return `[[L-${indexStr}]](${resourceUrl})`;
        }
        
        return `[L-${indexStr}]`; // 如果索引无效，保持原格式
      });
      
      // 返回处理后的引用，用逗号分隔
      return processedCitations.join(', ');
    });

    // 再处理单方括号格式 [L-数字]（避免被双方括号处理结果影响，也避免处理已有链接）
    processedContent = processedContent.replace(/(?<!\[)\[L-(\d+)\](?!\]|\()/g, (match: string, indexStr: string) => {
      const index = parseInt(indexStr) - 1; // 转换为0基索引
      
      if (index >= 0 && index < resources.length) {
        const resource = resources[index];
        const originalFileName = getOriginalFileName(resource.name);
        const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
        // 🔥 保持L-索引格式显示，链接指向原文件
        return `[[L-${indexStr}]](${resourceUrl})`;
      }
      
      // 🔍 调试信息：索引无效的情况
      console.warn(`⚠️ 章节编辑本地资源引用索引无效: [L-${indexStr}], 索引=${index}, 资源总数=${resources.length}`);
      return match; // 如果索引无效，保持原样
    });
    
    return processedContent;
  } catch (error) {
    console.error('处理章节本地资源引用失败:', error);
    return content;
  }
}