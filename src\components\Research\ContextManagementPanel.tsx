/**
 * 长程上下文管理面板
 * 提供可视化的上下文管理功能，包括统计信息、关联分析和一致性评估
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  Network, 
  Download, 
  Trash2, 
  CheckCircle, 
  AlertTriangle, 
  Info,
  TrendingUp,
  Database,
  GitBranch
} from 'lucide-react';

interface ContextStats {
  totalChapters: number;
  totalFindings: number;
  totalConclusions: number;
  crossDataKeys: number;
  lastUpdate: Date | null;
}

interface ChapterConnection {
  from: number;
  to: number;
  commonData: string[];
  relationshipStrength: 'strong' | 'medium' | 'weak';
}

interface ConnectionAnalysis {
  connections: ChapterConnection[];
  isolatedChapters: number[];
}

interface ConsistencyIssue {
  type: 'data_conflict' | 'missing_reference' | 'isolated_chapter' | 'weak_connection';
  severity: 'high' | 'medium' | 'low';
  chapter: number;
  description: string;
  suggestion: string;
}

interface ConsistencyReport {
  overallScore: number;
  issues: ConsistencyIssue[];
  strengths: string[];
}

interface ContextManagementPanelProps {
  getContextStats: () => ContextStats | null;
  getChapterConnectionAnalysis: () => ConnectionAnalysis | null;
  getConsistencyReport: () => ConsistencyReport | null;
  exportContextData: () => string | null;
  clearContextCache: () => void;
  onToggleContextMode: (enabled: boolean) => void;
  isContextModeEnabled: boolean;
}

export function ContextManagementPanel({
  getContextStats,
  getChapterConnectionAnalysis,
  getConsistencyReport,
  exportContextData,
  clearContextCache,
  onToggleContextMode,
  isContextModeEnabled
}: ContextManagementPanelProps) {
  const [stats, setStats] = useState<ContextStats | null>(null);
  const [connectionAnalysis, setConnectionAnalysis] = useState<ConnectionAnalysis | null>(null);
  const [consistencyReport, setConsistencyReport] = useState<ConsistencyReport | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // 刷新数据
  const refreshData = useCallback(async () => {
    setStats(getContextStats());
    setConnectionAnalysis(getChapterConnectionAnalysis());
    setConsistencyReport(getConsistencyReport());
  }, [getContextStats, getChapterConnectionAnalysis, getConsistencyReport]);

  // 定期刷新数据
  useEffect(() => {
    refreshData();
    
    if (isContextModeEnabled) {
      const interval = setInterval(refreshData, 5000); // 每5秒刷新一次
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isContextModeEnabled, refreshData, refreshInterval]);

  // 导出上下文数据
  const handleExportData = () => {
    const data = exportContextData();
    if (data) {
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `context-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 清理缓存
  const handleClearCache = () => {
    if (confirm('确定要清空上下文缓存吗？这将删除所有已提取的章节摘要和关联数据。')) {
      clearContextCache();
      refreshData();
    }
  };

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'warning';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  // 获取强度颜色
  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'strong': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'weak': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* 头部控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="w-5 h-5" />
            长程上下文管理
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant={isContextModeEnabled ? "default" : "outline"}
                onClick={() => onToggleContextMode(!isContextModeEnabled)}
              >
                {isContextModeEnabled ? "禁用上下文管理" : "启用上下文管理"}
              </Button>
              
              {isContextModeEnabled && (
                <Badge variant="outline" className="bg-green-50">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  上下文管理已启用
                </Badge>
              )}
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportData}
                disabled={!stats || stats.totalChapters === 0}
              >
                <Download className="w-4 h-4 mr-1" />
                导出数据
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearCache}
                disabled={!stats || stats.totalChapters === 0}
              >
                <Trash2 className="w-4 h-4 mr-1" />
                清空缓存
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容 */}
      {isContextModeEnabled && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview" className="flex items-center gap-1">
              <BarChart3 className="w-4 h-4" />
              概览
            </TabsTrigger>
            <TabsTrigger value="connections" className="flex items-center gap-1">
              <GitBranch className="w-4 h-4" />
              章节关联
            </TabsTrigger>
            <TabsTrigger value="consistency" className="flex items-center gap-1">
              <CheckCircle className="w-4 h-4" />
              一致性评估
            </TabsTrigger>
          </TabsList>

          {/* 概览标签页 */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 统计信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">上下文统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {stats ? (
                    <>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">已缓存章节</span>
                        <Badge variant="outline">{stats.totalChapters} / 6</Badge>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">关键发现</span>
                        <Badge variant="outline">{stats.totalFindings}</Badge>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">核心结论</span>
                        <Badge variant="outline">{stats.totalConclusions}</Badge>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">跨章节数据</span>
                        <Badge variant="outline">{stats.crossDataKeys} 项</Badge>
                      </div>
                      
                      {stats.lastUpdate && (
                        <div className="text-xs text-gray-500 mt-2">
                          最后更新：{stats.lastUpdate.toLocaleString()}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Database className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>暂无数据</p>
                      <p className="text-xs">开始生成章节后将显示统计信息</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 进度指示 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">生成进度</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {stats ? (
                    <>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>章节完成度</span>
                          <span>{Math.round((stats.totalChapters / 6) * 100)}%</span>
                        </div>
                        <Progress value={(stats.totalChapters / 6) * 100} />
                      </div>
                      
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>信息密度</span>
                          <span>
                            {stats.totalChapters > 0 
                              ? Math.round((stats.totalFindings + stats.totalConclusions) / stats.totalChapters)
                              : 0
                            } 项/章节
                          </span>
                        </div>
                        <Progress 
                          value={Math.min(
                            ((stats.totalFindings + stats.totalConclusions) / Math.max(stats.totalChapters, 1)) * 10, 
                            100
                          )} 
                        />
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        {stats.totalChapters === 6 ? (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="w-3 h-3" />
                            所有章节已完成
                          </div>
                        ) : (
                          <div className="flex items-center gap-1">
                            <TrendingUp className="w-3 h-3" />
                            正在进行中...
                          </div>
                        )}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <TrendingUp className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>暂无进度数据</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 章节关联标签页 */}
          <TabsContent value="connections">
            <Card>
              <CardHeader>
                <CardTitle>章节关联分析</CardTitle>
              </CardHeader>
              <CardContent>
                {connectionAnalysis ? (
                  <div className="space-y-6">
                    {/* 关联图表 */}
                    <div>
                      <h4 className="font-medium mb-3">章节间数据关联</h4>
                      <div className="space-y-2">
                        {connectionAnalysis.connections.length > 0 ? (
                          connectionAnalysis.connections.map((conn, idx) => (
                            <div key={idx} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center gap-3">
                                <span className="text-sm font-medium">
                                  第{conn.from}章 ↔ 第{conn.to}章
                                </span>
                                <div className={`w-3 h-3 rounded-full ${getStrengthColor(conn.relationshipStrength)}`} />
                                <Badge variant="outline" className="text-xs">
                                  {conn.relationshipStrength === 'strong' ? '强关联' : 
                                   conn.relationshipStrength === 'medium' ? '中关联' : '弱关联'}
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                {conn.commonData.length} 个共同数据点
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            <GitBranch className="w-8 h-8 mx-auto mb-2 opacity-50" />
                            <p>暂无章节关联数据</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 孤立章节 */}
                    {connectionAnalysis.isolatedChapters.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4 text-orange-500" />
                          孤立章节
                        </h4>
                        <div className="flex gap-2">
                          {connectionAnalysis.isolatedChapters.map(chapterNum => (
                            <Badge key={chapterNum} variant="outline" className="border-orange-200">
                              第{chapterNum}章
                            </Badge>
                          ))}
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          这些章节与其他章节的数据关联较少，建议增加引用
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Network className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>暂无关联分析数据</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 一致性评估标签页 */}
          <TabsContent value="consistency">
            <Card>
              <CardHeader>
                <CardTitle>一致性评估报告</CardTitle>
              </CardHeader>
              <CardContent>
                {consistencyReport ? (
                  <div className="space-y-6">
                    {/* 总体评分 */}
                    <div className="text-center">
                      <div className="text-3xl font-bold mb-2">
                        {consistencyReport.overallScore}
                        <span className="text-lg text-gray-500">/100</span>
                      </div>
                      <Progress 
                        value={consistencyReport.overallScore} 
                        className="max-w-xs mx-auto"
                      />
                      <p className="text-sm text-gray-600 mt-2">
                        {consistencyReport.overallScore >= 90 ? '优秀' :
                         consistencyReport.overallScore >= 70 ? '良好' :
                         consistencyReport.overallScore >= 50 ? '一般' : '需要改进'}
                      </p>
                    </div>

                    {/* 发现的问题 */}
                    {consistencyReport.issues.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4 text-orange-500" />
                          发现的问题 ({consistencyReport.issues.length})
                        </h4>
                        <div className="space-y-3">
                          {consistencyReport.issues.map((issue, idx) => (
                            <div key={idx} className="border rounded-lg p-3">
                              <div className="flex items-start justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <Badge variant={getSeverityColor(issue.severity) as any}>
                                    {issue.severity === 'high' ? '高' :
                                     issue.severity === 'medium' ? '中' : '低'}
                                  </Badge>
                                  {issue.chapter > 0 && (
                                    <Badge variant="outline">第{issue.chapter}章</Badge>
                                  )}
                                </div>
                              </div>
                              <p className="text-sm mb-2">{issue.description}</p>
                              <p className="text-xs text-gray-600">
                                <strong>建议：</strong>{issue.suggestion}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 优势 */}
                    {consistencyReport.strengths.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          发现的优势 ({consistencyReport.strengths.length})
                        </h4>
                        <div className="space-y-2">
                          {consistencyReport.strengths.map((strength, idx) => (
                            <div key={idx} className="flex items-center gap-2 p-2 bg-green-50 rounded">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <span className="text-sm">{strength}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>暂无一致性评估数据</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* 禁用状态提示 */}
      {!isContextModeEnabled && (
        <Card className="border-dashed">
          <CardContent className="text-center py-12">
            <Info className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">长程上下文管理已禁用</h3>
            <p className="text-gray-600 mb-4">
              启用后可以享受章节间上下文记忆、智能避重和数据一致性检查功能
            </p>
            <Button onClick={() => onToggleContextMode(true)}>
              启用长程上下文管理
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 