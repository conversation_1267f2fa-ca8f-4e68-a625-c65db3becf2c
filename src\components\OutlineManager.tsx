"use client";
import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/Internal/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Download, 
  Upload,
  Search,
  Calendar
} from 'lucide-react';
import { useOutlineStore } from '@/store/outline';
import { useTaskStore } from '@/store/task';
import { useOutlineManager } from '@/hooks/useOutlineManager';
import OutlineEditor from './Research/OutlineEditor';
import { ResearchOutline } from '@/types/outline';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface OutlineManagerProps {
  open: boolean;
  onClose: () => void;
}

// 安全的日期格式化函数
const formatDate = (date: Date | string): string => {
  try {
    if (date instanceof Date) {
      return isNaN(date.getTime()) ? '无效日期' : date.toLocaleDateString('zh-CN');
    }
    const parsedDate = new Date(date);
    return isNaN(parsedDate.getTime()) ? '无效日期' : parsedDate.toLocaleDateString('zh-CN');
  } catch {
    return '无效日期';
  }
};

export default function OutlineManager({ open, onClose }: OutlineManagerProps) {
  const [activeTab, setActiveTab] = useState("list");
  const [showEditor, setShowEditor] = useState(false);
  const [editingOutline, setEditingOutline] = useState<ResearchOutline | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isEditingTemplate, setIsEditingTemplate] = useState(false);
  const [originalTemplateId, setOriginalTemplateId] = useState<string | null>(null);

  const outlineStore = useOutlineStore();
  const taskStore = useTaskStore();
  const { applyOutlineToTask, exportOutline } = useOutlineManager();

  // 过滤大纲
  const filteredOutlines = outlineStore.outlines.filter(outline =>
    outline.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    outline.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 创建新大纲
  const handleCreateNew = () => {
    setEditingOutline(null);
    setIsEditingTemplate(false);
    setOriginalTemplateId(null);
    setShowEditor(true);
  };

  // 编辑大纲
  const handleEdit = (outline: ResearchOutline) => {
    setEditingOutline(outline);
    setIsEditingTemplate(false);
    setOriginalTemplateId(null);
    outlineStore.setCurrentOutline(outline);
    setShowEditor(true);
  };

  // 复制大纲
  const handleDuplicate = (outline: ResearchOutline) => {
    const duplicated = outlineStore.duplicateOutline(outline.id);
    if (duplicated) {
      console.log(`大纲 "${outline.title}" 已复制`);
    }
  };

  // 删除大纲
  const handleDelete = (outline: ResearchOutline) => {
    if (confirm(`确定要删除大纲 "${outline.title}" 吗？此操作不可撤销。`)) {
      outlineStore.deleteOutline(outline.id);
    }
  };

  // 应用大纲到当前研究
  const handleApply = (outline: ResearchOutline) => {
    applyOutlineToTask(outline);
    onClose();
  };

  // 导出大纲
  const handleExport = (outline: ResearchOutline) => {
    const exported = exportOutline(outline.id);
    if (exported) {
      const blob = new Blob([exported], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${outline.title}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  // 编辑模板 - 将模板转换为大纲进行编辑
  const handleEditTemplate = (template: any) => {
    // 将模板转换为大纲格式
    const outline: ResearchOutline = {
      id: `template-edit-${template.id}`,
      title: template.name,
      description: template.description,
      chapters: template.chapters.map((chapter: any, index: number) => ({
        ...chapter,
        id: `chapter-${index}`,
        number: index + 1,
      })),
      writingConfig: template.defaultConfig,
      createdAt: new Date(),
      updatedAt: new Date(),
      source: 'user_created' as const,
      version: '1.0.0',
    };
    
    setEditingOutline(outline);
    setIsEditingTemplate(true);
    setOriginalTemplateId(template.id);
    outlineStore.setCurrentOutline(outline);
    setShowEditor(true);
  };

  // 删除模板
  const handleDeleteTemplate = (template: any) => {
    if (template.isBuiltIn) {
      alert('内置模板无法删除');
      return;
    }
    
    if (confirm(`确定要删除模板 "${template.name}" 吗？此操作不可撤销。`)) {
      outlineStore.deleteTemplate(template.id);
    }
  };

  // 复制模板
  const handleDuplicateTemplate = (template: any) => {
    const newTemplate = {
      ...template,
      id: `template-${Date.now()}`,
      name: `${template.name} (副本)`,
      isBuiltIn: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // 这里需要添加到模板store中，但当前store可能没有直接添加模板的方法
    // 作为替代方案，我们可以先转换为大纲，然后保存为模板
    const outline = outlineStore.applyTemplate(template.id);
    if (outline) {
      outlineStore.saveAsTemplate(outline.id, newTemplate.name, template.description);
    }
  };

  return (
    <>
      <Dialog open={open && !showEditor} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>大纲管理器</span>
            </DialogTitle>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="list">大纲列表</TabsTrigger>
              <TabsTrigger value="templates">模板管理</TabsTrigger>
              <TabsTrigger value="settings">设置</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-4">
              {/* 工具栏 */}
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="搜索大纲..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 border rounded-md w-64"
                    />
                  </div>
                  <Badge variant="outline">
                    {filteredOutlines.length} 个大纲
                  </Badge>
                </div>
                                  <div className="flex space-x-2">
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="opacity-50">
                          <Button size="sm" variant="outline" className="cursor-not-allowed" onClick={(e) => e.preventDefault()}>
                            <Upload className="w-4 h-4 mr-1" />
                            导入
                          </Button>
                        </span>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>🚧 正在开发中，敬请期待</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Button size="sm" onClick={handleCreateNew}>
                    <Plus className="w-4 h-4 mr-1" />
                    新建大纲
                  </Button>
                </div>
              </div>

              {/* 大纲列表 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredOutlines.map((outline) => (
                  <Card key={outline.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-base line-clamp-2">
                            {outline.title}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {outline.description}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {outline.chapters.length} 章节
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="w-3 h-3 mr-1" />
                          创建于 {formatDate(outline.createdAt)}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleEdit(outline)}
                          >
                            <Edit className="w-3 h-3 mr-1" />
                            编辑
                          </Button>
                          <Button 
                            size="sm" 
                            onClick={() => handleApply(outline)}
                            disabled={taskStore.researchOutline?.id === outline.id}
                          >
                            {taskStore.researchOutline?.id === outline.id ? "已应用" : "应用"}
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleDuplicate(outline)}
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            复制
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleExport(outline)}
                          >
                            <Download className="w-3 h-3 mr-1" />
                            导出
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleDelete(outline)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3 mr-1" />
                            删除
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredOutlines.length === 0 && (
                <Card>
                  <CardContent className="text-center py-12">
                    <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                      {searchQuery ? "未找到匹配的大纲" : "还没有大纲"}
                    </h3>
                    <p className="text-muted-foreground mb-6">
                      {searchQuery ? "尝试调整搜索条件" : "创建您的第一个研究大纲"}
                    </p>
                    {!searchQuery && (
                      <Button onClick={handleCreateNew}>
                        <Plus className="w-4 h-4 mr-2" />
                        创建新大纲
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="templates" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>模板管理</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {outlineStore.templates.map((template) => (
                      <Card key={template.id} className="hover:shadow-md transition-shadow">
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <CardTitle className="text-base">{template.name}</CardTitle>
                              <p className="text-sm text-muted-foreground mt-1">
                                {template.description}
                              </p>
                              {template.isBuiltIn && (
                                <Badge variant="secondary" className="mt-2">
                                  内置模板
                                </Badge>
                              )}
                            </div>
                            <Badge variant="outline">
                              {template.chapters.length} 章节
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="flex items-center text-xs text-muted-foreground">
                              <Calendar className="w-3 h-3 mr-1" />
                              创建于 {formatDate(template.createdAt)}
                            </div>
                            
                            <div className="flex flex-wrap gap-1">
                              <Button 
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditTemplate(template)}
                              >
                                <Edit className="w-3 h-3 mr-1" />
                                编辑
                              </Button>
                              <Button 
                                size="sm"
                                onClick={() => {
                                  const outline = outlineStore.applyTemplate(template.id);
                                  if (outline) {
                                    handleApply(outline);
                                  }
                                }}
                              >
                                应用模板
                              </Button>
                              <Button 
                                size="sm"
                                variant="outline"
                                onClick={() => handleDuplicateTemplate(template)}
                              >
                                <Copy className="w-3 h-3 mr-1" />
                                复制
                              </Button>
                              {!template.isBuiltIn && (
                                <Button 
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDeleteTemplate(template)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-3 h-3 mr-1" />
                                  删除
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                  
                  {outlineStore.templates.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">暂无可用模板</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>大纲设置</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">统计信息</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {outlineStore.outlines.length}
                          </div>
                          <div className="text-sm text-muted-foreground">总大纲数</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {outlineStore.templates.length}
                          </div>
                          <div className="text-sm text-muted-foreground">模板数</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {outlineStore.outlines.reduce((sum, outline) => sum + outline.chapters.length, 0)}
                          </div>
                          <div className="text-sm text-muted-foreground">总章节数</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {taskStore.isOutlineDriven ? "已启用" : "未启用"}
                          </div>
                          <div className="text-sm text-muted-foreground">大纲驱动</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* 大纲编辑器 */}
      <Dialog open={showEditor} onOpenChange={setShowEditor}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isEditingTemplate 
                ? `编辑模板：${editingOutline?.title}` 
                : editingOutline 
                  ? `编辑大纲：${editingOutline.title}` 
                  : "创建新大纲"
              }
            </DialogTitle>
          </DialogHeader>
          <OutlineEditor 
            outline={editingOutline || undefined}
            onSave={(outline) => {
              if (isEditingTemplate && originalTemplateId) {
                // 编辑模板：直接更新模板，不需要删除重建
                const template = outlineStore.getTemplateById(originalTemplateId);
                if (template) {
                  outlineStore.updateTemplate(originalTemplateId, {
                    name: outline.title,
                    description: outline.description,
                    chapters: outline.chapters.map((chapter) => {
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      const { id, ...chapterWithoutId } = chapter;
                      return chapterWithoutId;
                    }),
                    defaultConfig: outline.writingConfig,
                  });
                  console.log(`✅ 模板 "${outline.title}" 已更新`);
                  console.log('✅ 模板保存成功，配置已更新:', outline.writingConfig);
                  // 强制触发组件重新渲染，确保模板列表显示最新数据
                  setTimeout(() => {
                    // 使用小技巧触发状态更新，确保模板列表刷新
                    const currentTemplates = outlineStore.templates;
                    console.log('🔄 强制刷新模板列表，当前模板数量:', currentTemplates.length);
                  }, 100);
                }
              } else {
                // 普通大纲编辑或创建
                outlineStore.updateOutline(outline);
              }
              setShowEditor(false);
              setEditingOutline(null);
              setIsEditingTemplate(false);
              setOriginalTemplateId(null);
            }}
            onCancel={() => {
              setShowEditor(false);
              setEditingOutline(null);
              setIsEditingTemplate(false);
              setOriginalTemplateId(null);
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
} 