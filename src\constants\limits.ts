export const MAX_TOPIC_LENGTH = 500;
export const MAX_SEARCH_RESULTS = 10;
export const MAX_KNOWLEDGE_RESULTS = 5;
export const MAX_CONTEXT_LENGTH = 10000;
export const MAX_SUMMARY_LENGTH = 2000;
export const MAX_TITLE_LENGTH = 100;
export const MAX_DESCRIPTION_LENGTH = 500;

export const FILE_UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_FILE_COUNT: 10,
  ALLOWED_TYPES: [
    'text/plain',
    'text/markdown',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ]
}; 