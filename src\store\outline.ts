import { create } from "zustand";
import { persist } from "zustand/middleware";
import { customAlphabet } from "nanoid";
import { 
  ResearchOutline, 
  OutlineTemplate, 
  WritingProgress,
  OutlineValidationResult,
  OutlineGenerationConfig,
  OutlineChapter,
  OutlineSection,
  WritingConfig,
  WordCountRange
} from "@/types/outline";

const nanoid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz", 12);

export interface OutlineStore {
  // 当前大纲
  currentOutline: ResearchOutline | null;
  
  // 大纲历史记录
  outlines: ResearchOutline[];
  
  // 大纲模板
  templates: OutlineTemplate[];
  
  // 写作进度
  writingProgress: WritingProgress | null;
  
  // 生成配置
  generationConfig: OutlineGenerationConfig;
}

export interface OutlineActions {
  // 大纲管理
  createOutline: (title: string, description: string) => ResearchOutline;
  updateOutline: (outline: ResearchOutline) => void;
  setCurrentOutline: (outline: ResearchOutline | null) => void;
  deleteOutline: (id: string) => boolean;
  duplicateOutline: (id: string) => ResearchOutline | null;
  
  // 章节管理
  addChapter: (chapter: Omit<OutlineChapter, 'id'>) => void;
  updateChapter: (chapterId: string, updates: Partial<OutlineChapter>) => void;
  deleteChapter: (chapterId: string) => void;
  reorderChapters: (chapterIds: string[]) => void;
  
  // 子章节管理
  addSection: (chapterId: string, section: Omit<OutlineSection, 'id'>) => void;
  updateSection: (chapterId: string, sectionId: string, updates: Partial<OutlineSection>) => void;
  deleteSection: (chapterId: string, sectionId: string) => void;
  reorderSections: (chapterId: string, sectionIds: string[]) => void;
  
  // 模板管理
  saveAsTemplate: (outlineId: string, templateName: string, description: string) => OutlineTemplate;
  updateTemplate: (templateId: string, updates: Partial<OutlineTemplate>) => void;
  applyTemplate: (templateId: string) => ResearchOutline | null;
  deleteTemplate: (templateId: string) => boolean;
  initializeBuiltInTemplates: () => void;
  forceUpdateBuiltInTemplates: () => void;
  
  // 进度管理
  updateProgress: (progress: Partial<WritingProgress>) => void;
  resetProgress: () => void;
  
  // 配置管理
  updateGenerationConfig: (config: Partial<OutlineGenerationConfig>) => void;
  updateWritingConfig: (config: Partial<WritingConfig>) => void;
  
  // 验证和导入导出
  validateOutline: (outline: ResearchOutline) => OutlineValidationResult;
  exportOutline: (id: string) => string | null;
  importOutline: (data: string) => ResearchOutline | null;
  
  // 工具方法
  getOutlineById: (id: string) => ResearchOutline | null;
  getTemplateById: (id: string) => OutlineTemplate | null;
  searchOutlines: (query: string) => ResearchOutline[];
  getOutlineStats: (id: string) => { totalChapters: number; totalSections: number; totalWords: WordCountRange } | null;
  
  // 章节映射相关方法
  generateChapterMap: (outlineId?: string) => Map<string, import("@/components/MagicDown").ChapterInfo> | null;
}

// 默认生成配置
const defaultGenerationConfig: OutlineGenerationConfig = {
  maxChapters: 10,
  maxSectionsPerChapter: 8,
  defaultWordCountPerChapter: { min: 3000, max: 4000 },
  defaultWordCountPerSection: { min: 800, max: 1200 },
  includeResearchPoints: true,
  generateDescriptions: true,
};

// 默认写作配置
const defaultWritingConfig: WritingConfig = {
  mode: 'chapter',
  format: {
    titleFormat: 'numbered',
    includeTableOfContents: true,
    includeCitations: true,
    includeImages: false,
    paragraphSpacing: 'normal',
  },
  avoidDuplication: true,
  enableContextMemory: true,
  chapterDelay: 2000,
  sectionDelay: 1000,
  style: 'business',
  languageRequirements: '必须使用中文撰写，采用专业、严谨、客观的商业报告语言风格',
  enableQualityCheck: true,
  customInstructions: '',
};

type Store = OutlineStore & OutlineActions;

export const useOutlineStore = create<Store>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentOutline: null,
      outlines: [],
      templates: [],
      writingProgress: null,
      generationConfig: defaultGenerationConfig,

      // 大纲管理
      createOutline: (title: string, description: string = '') => {
        const newOutline: ResearchOutline = {
          id: nanoid(),
          title,
          description,
          chapters: [],
          writingConfig: defaultWritingConfig,
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user_created',
          version: '1.0.0',
        };

        set((state) => ({
          outlines: [newOutline, ...state.outlines],
          currentOutline: newOutline,
        }));

        return newOutline;
      },

      updateOutline: (outline: ResearchOutline) => {
        set((state) => ({
          outlines: state.outlines.map(o => o.id === outline.id ? outline : o),
          currentOutline: state.currentOutline?.id === outline.id ? outline : state.currentOutline,
        }));
      },

      setCurrentOutline: (outline: ResearchOutline | null) => {
        set({ currentOutline: outline });
      },

      deleteOutline: (id: string) => {
        set((state) => ({
          outlines: state.outlines.filter(o => o.id !== id),
          currentOutline: state.currentOutline?.id === id ? null : state.currentOutline,
        }));
        return true;
      },

      duplicateOutline: (id: string) => {
        const outline = get().getOutlineById(id);
        if (!outline) return null;

        const duplicated: ResearchOutline = {
          ...outline,
          id: nanoid(),
          title: `${outline.title} (副本)`,
          createdAt: new Date(),
          updatedAt: new Date(),
          chapters: outline.chapters.map(chapter => ({
            ...chapter,
            id: nanoid(),
            sections: chapter.sections.map(section => ({
              ...section,
              id: nanoid(),
            })),
          })),
        };

        set((state) => ({
          outlines: [duplicated, ...state.outlines],
        }));

        return duplicated;
      },

      // 章节管理
      addChapter: (chapter: Omit<OutlineChapter, 'id'>) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const newChapter: OutlineChapter = {
          ...chapter,
          id: nanoid(),
        };

        const updatedOutline = {
          ...currentOutline,
          chapters: [...currentOutline.chapters, newChapter],
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      updateChapter: (chapterId: string, updates: Partial<OutlineChapter>) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const updatedOutline = {
          ...currentOutline,
          chapters: currentOutline.chapters.map(c => 
            c.id === chapterId ? { ...c, ...updates } : c
          ),
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      deleteChapter: (chapterId: string) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const updatedOutline = {
          ...currentOutline,
          chapters: currentOutline.chapters.filter(c => c.id !== chapterId),
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      reorderChapters: (chapterIds: string[]) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const reorderedChapters = chapterIds.map(id => 
          currentOutline.chapters.find(c => c.id === id)!
        ).filter(Boolean);

        const updatedOutline = {
          ...currentOutline,
          chapters: reorderedChapters,
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      // 子章节管理
      addSection: (chapterId: string, section: Omit<OutlineSection, 'id'>) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const newSection: OutlineSection = {
          ...section,
          id: nanoid(),
        };

        const updatedOutline = {
          ...currentOutline,
          chapters: currentOutline.chapters.map(c => 
            c.id === chapterId 
              ? { ...c, sections: [...c.sections, newSection] }
              : c
          ),
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      updateSection: (chapterId: string, sectionId: string, updates: Partial<OutlineSection>) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const updatedOutline = {
          ...currentOutline,
          chapters: currentOutline.chapters.map(c => 
            c.id === chapterId 
              ? {
                  ...c,
                  sections: c.sections.map(s => 
                    s.id === sectionId ? { ...s, ...updates } : s
                  )
                }
              : c
          ),
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      deleteSection: (chapterId: string, sectionId: string) => {
        const { currentOutline } = get();
        if (!currentOutline) return;

        const updatedOutline = {
          ...currentOutline,
          chapters: currentOutline.chapters.map(c => 
            c.id === chapterId 
              ? { ...c, sections: c.sections.filter(s => s.id !== sectionId) }
              : c
          ),
          updatedAt: new Date(),
        };

        get().updateOutline(updatedOutline);
      },

      reorderSections: (chapterId: string, sectionIds: string[]) => {
        const { currentOutline } = get();
        if (!currentOutline) return;
        
        const chapter = currentOutline.chapters.find(c => c.id === chapterId);
        if (!chapter) return;
        
        const reorderedSections = sectionIds.map(id => 
          chapter.sections.find(s => s.id === id)!
        ).filter(Boolean);
        
        const updatedOutline = {
          ...currentOutline,
          chapters: currentOutline.chapters.map(c => 
            c.id === chapterId 
              ? { ...c, sections: reorderedSections }
              : c
          ),
          updatedAt: new Date(),
        };
        
        get().updateOutline(updatedOutline);
      },
      
      // 模板管理
      saveAsTemplate: (outlineId: string, templateName: string, description: string) => {
        const outline = get().getOutlineById(outlineId);
        if (!outline) throw new Error('Outline not found');
        
        const template: OutlineTemplate = {
          id: nanoid(),
          name: templateName,
          description,
          category: 'custom',
          chapters: outline.chapters.map((chapter) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { id, ...chapterWithoutId } = chapter;
            return chapterWithoutId;
          }),
          defaultConfig: outline.writingConfig,
          tags: [],
          isBuiltIn: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set((state) => ({
          templates: [template, ...state.templates],
        }));
        
        return template;
      },
      
      updateTemplate: (templateId: string, updates: Partial<OutlineTemplate>) => {
        console.log('🔄 更新模板:', { templateId, updates });
        set((state) => {
          const newTemplates = state.templates.map(t => 
            t.id === templateId 
              ? { ...t, ...updates, updatedAt: new Date() }
              : t
          );
          
          // 调试：检查更新后的模板
          const updatedTemplate = newTemplates.find(t => t.id === templateId);
          if (updatedTemplate && updates.chapters) {
            console.log('✅ 模板更新成功:', {
              templateId,
              chaptersCount: updatedTemplate.chapters.length,
              firstChapterSections: updatedTemplate.chapters[0]?.sections?.length || 0,
            });
          }
          
          return { templates: newTemplates };
        });
      },
      
      applyTemplate: (templateId: string) => {
        const template = get().getTemplateById(templateId);
        if (!template) return null;
        
        const newOutline: ResearchOutline = {
          id: nanoid(),
          title: `基于${template.name}的大纲`,
          description: template.description,
          chapters: template.chapters.map(chapter => ({
            ...chapter,
            id: nanoid(),
            sections: chapter.sections.map(section => ({
              ...section,
              id: nanoid(),
            })),
          })),
          writingConfig: template.defaultConfig,
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'hybrid',
          version: '1.0.0',
        };
        
        console.log('🔄 应用模板:', template.name);
        console.log('📋 模板写作配置:', template.defaultConfig);
        console.log('✅ 新大纲继承的配置:', newOutline.writingConfig);
        
        set((state) => ({
          outlines: [newOutline, ...state.outlines],
          currentOutline: newOutline,
        }));
        
        return newOutline;
      },
      
      deleteTemplate: (templateId: string) => {
        set((state) => ({
          templates: state.templates.filter(t => t.id !== templateId),
        }));
        return true;
      },

      initializeBuiltInTemplates: () => {
        // 检查是否已经初始化过内置模板
        const { templates } = get();
        const hasBuiltInTemplates = templates.some(t => t.isBuiltIn);
        if (hasBuiltInTemplates) {
          return; // 已经初始化过，直接返回
        }

        const builtInTemplates: OutlineTemplate[] = [
          {
            id: 'enterprise-report',
            name: '企业管理报告',
            description: '基于投资分析的企业管理报告标准结构，涵盖商业模式、经营状况、管理团队、治理结构、发展情况和总结评价六大核心维度，支持子章节分拆写作',
            category: 'builtin',
            isBuiltIn: true,
            tags: ['企业', '投资', '管理', '分析', '投后管理'],
            createdAt: new Date(),
            updatedAt: new Date(),
            chapters: [
              {
                number: 1,
                title: '项目公司的商业模式',
                description: '深入分析项目公司的核心商业模式和竞争优势，包括产品创新性、盈利模式、目标市场、行业情况和战略联盟',
                wordCount: { min: 13300, max: 18100 },
                researchPoints: [
                  '详细描述项目公司的核心产品/服务',
                  '深入分析其在技术、设计、功能、成本等方面的创新点和领先性',
                  '清晰阐述项目公司的主要盈利模式和收入来源构成',
                  '分析当前主要的营销策略和渠道，并评估其有效性',
                  '详细介绍公司管理层制定的下一年度商业计划和经营目标',
                  '界定项目公司的主要目标市场及其细分领域',
                  '评估目标市场的当前规模、增长潜力和未来趋势',
                  '分析项目公司所处行业的整体发展态势、市场集中度、技术演进方向',
                  '详细分析行业内的主要竞争对手，包括其市场份额、竞争策略、优劣势等',
                  '回顾报告期内是否有引入新的战略投资者或达成重要战略联盟的情况'
                ],
                enabled: true,
                priority: 'high',
                sections: [
                  {
                    id: '1.1',
                    number: '1.1',
                    title: '产品的创新性、领先性与核心优势',
                    description: '详细分析项目公司核心产品或服务的创新点、技术先进性和竞争优势',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '详细介绍项目公司的核心产品或服务内容',
                      '深入分析产品在技术层面的创新点和突破',
                      '明确指出产品相对于竞争对手的核心优势',
                      '评估产品在细分市场中的领先地位'
                    ],
                    enabled: true,
                    dependencies: []
                  },
                  {
                    id: '1.2',
                    number: '1.2',
                    title: '项目公司的盈利模式、营销策略、下年度商业计划',
                    description: '深入分析盈利机制、营销策略执行和商业计划制定',
                    wordCount: { min: 3000, max: 3900 },
                    researchPoints: [
                      '详细阐述公司的主要收入来源和盈利机制',
                      '分析当前的营销渠道和客户获取策略',
                      '评估营销策略的有效性和投入产出比',
                      '详细介绍下一年度的商业计划和目标'
                    ],
                    enabled: true,
                    dependencies: ['1.1']
                  },
                  {
                    id: '1.3',
                    number: '1.3',
                    title: '目标市场的有效规模',
                    description: '量化分析目标市场规模、增长潜力和渗透空间',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '清晰界定公司的主要目标市场和细分领域',
                      '提供目标市场的当前规模数据（引用权威来源）',
                      '评估公司在目标市场中的当前渗透率',
                      '分析未来增长空间和市场机会'
                    ],
                    enabled: true,
                    dependencies: ['1.1', '1.2']
                  },
                  {
                    id: '1.4',
                    number: '1.4',
                    title: '行业情况跟踪',
                    description: '全面分析行业发展态势、竞争格局和外部环境影响',
                    wordCount: { min: 3000, max: 3900 },
                    researchPoints: [
                      '分析行业整体发展趋势、市场集中度、技术演进',
                      '详细分析主要竞争对手的市场地位和策略',
                      '评估宏观环境和政策对行业的影响',
                      '分析行业变化对项目公司的具体影响'
                    ],
                    enabled: true,
                    dependencies: ['1.1', '1.3']
                  },
                  {
                    id: '1.5',
                    number: '1.5',
                    title: '引入战略投资人、战略联盟',
                    description: '分析战略投资和合作伙伴关系对业务发展的影响',
                    wordCount: { min: 2000, max: 3000 },
                    researchPoints: [
                      '回顾报告期内引入的战略投资者情况',
                      '梳理重要的战略合作伙伴关系',
                      '分析战略投资和联盟对业务发展的实际推动作用',
                      '评估对公司核心竞争力的提升效果'
                    ],
                    enabled: true,
                    dependencies: ['1.2']
                  }
                ]
              },
              {
                number: 2,
                title: '项目公司的经营状况',
                description: '全面分析项目公司的财务状况、经营效率和资本市场表现，特别关注财务指标深度分析',
                wordCount: { min: 15400, max: 21200 },
                researchPoints: [
                  '回顾报告期内公司经营预算的编制情况',
                  '分析预算的实际执行情况，对比预算与实际的差异',
                  '详细回顾报告期内关键营销活动的执行情况、投入产出及市场反馈',
                  '基于公司最新披露的财务报表进行核心财务指标分析',
                  '进行关键财务指标的历年对比和同业对比',
                  '简要解读资产负债表、利润表、现金流量表的主要变动和结构特点',
                  '简述公司内部审计工作的开展情况、主要发现及改进措施',
                  '分析公司作为上市企业，在资本市场的融资能力、并购潜力、市值管理等方面的空间',
                  '综合评估外部环境因素对公司经营的具体影响'
                ],
                enabled: true,
                priority: 'high',
                sections: [
                  {
                    id: '2.1',
                    number: '2.1',
                    title: '项目经营预算编制及执行',
                    description: '分析预算编制的科学性和执行控制的有效性',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '分析报告期内经营预算的编制情况和主要假设',
                      '对比预算与实际执行的差异情况',
                      '评估预算管理水平和执行控制能力'
                    ],
                    enabled: true,
                    dependencies: []
                  },
                  {
                    id: '2.2',
                    number: '2.2',
                    title: '营销策略执行情况',
                    description: '评估营销活动的市场反馈和投入产出效果',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '详细回顾报告期内的重要营销活动和投入',
                      '评估营销策略对销售业绩的贡献度',
                      '分析投入产出比和ROI表现',
                      '识别营销策略的优势和不足'
                    ],
                    enabled: true,
                    dependencies: ['2.1']
                  },
                  {
                    id: '2.3',
                    number: '2.3',
                    title: '财务指标及报表的分析',
                    description: '深度分析核心财务指标，进行历年对比和同业对比',
                    wordCount: { min: 3200, max: 4000 },
                    researchPoints: [
                      '深度分析营收增长率、毛利率、净利率、ROA、ROE等核心指标',
                      '分析存货周转率、应收账款周转率等运营效率指标',
                      '分析偿债能力指标（资产负债率、流动比率等）',
                      '解读资产负债表、利润表、现金流量表的主要变动'
                    ],
                    enabled: true,
                    dependencies: ['2.1', '2.2']
                  },
                  {
                    id: '2.4',
                    number: '2.4',
                    title: '内部审计',
                    description: '评估内部审计工作质量和内控体系有效性',
                    wordCount: { min: 2200, max: 3200 },
                    researchPoints: [
                      '简述公司内部审计工作的组织架构和开展情况',
                      '梳理内部审计发现的主要问题和风险点',
                      '分析针对审计发现问题的改进措施',
                      '评估内部控制体系的健全性和有效性'
                    ],
                    enabled: true,
                    dependencies: ['2.3']
                  },
                  {
                    id: '2.5',
                    number: '2.5',
                    title: '资本市场操作空间',
                    description: '分析融资能力、并购潜力和市值管理空间',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '分析公司在资本市场的融资能力和信用状况',
                      '分析公司的并购整合能力和战略空间',
                      '分析公司的市值表现和投资者关系管理',
                      '评估市值管理的策略和效果'
                    ],
                    enabled: true,
                    dependencies: ['2.3']
                  },
                  {
                    id: '2.6',
                    number: '2.6',
                    title: '外部环境影响分析',
                    description: '评估宏观环境、政策法规和市场环境对经营的影响',
                    wordCount: { min: 2200, max: 3200 },
                    researchPoints: [
                      '分析宏观经济环境对公司经营的具体影响',
                      '分析相关政策法规变化对公司的影响',
                      '分析市场环境变化对公司经营的影响',
                      '评估公司的适应能力和应对策略'
                    ],
                    enabled: true,
                    dependencies: []
                  }
                ]
              },
              {
                number: 3,
                title: '项目公司的管理团队',
                description: '深入分析核心管理团队的构成、素质、稳定性和激励机制',
                wordCount: { min: 7800, max: 10800 },
                researchPoints: [
                  '介绍核心管理团队（董事长、CEO、CFO、CTO、核心事业部负责人等）的构成及分工',
                  '详细分析核心管理人员的教育背景、从业经历、专业能力',
                  '评估团队在行业经验、技术能力、管理水平等方面的综合素质',
                  '评估核心管理团队的稳定性，包括任职时间、变动情况等',
                  '分析公司对管理层的激励机制（薪酬体系、股权激励等）'
                ],
                enabled: true,
                priority: 'medium',
                sections: [
                  {
                    id: '3.1',
                    number: '3.1',
                    title: '团队配置',
                    description: '分析核心管理团队的构成、结构和职能覆盖',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '介绍董事长、CEO、CFO、CTO等核心管理人员',
                      '评估管理团队的层级结构和决策机制',
                      '评估各业务职能的人员配置充足性',
                      '分析关键岗位的人员稳定性'
                    ],
                    enabled: true,
                    dependencies: []
                  },
                  {
                    id: '3.2',
                    number: '3.2',
                    title: '管理队伍素质及专业技巧',
                    description: '评估管理人员的教育背景、从业经历和专业能力',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '详细分析核心管理人员的教育背景和专业领域',
                      '分析核心管理人员的从业经历和行业经验',
                      '评估团队在技术、管理、市场等方面的综合能力',
                      '分析团队成员之间的能力互补性'
                    ],
                    enabled: true,
                    dependencies: ['3.1']
                  },
                  {
                    id: '3.3',
                    number: '3.3',
                    title: '团队稳定性及激励机制',
                    description: '分析团队稳定性和激励机制的设计及效果',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '评估核心管理团队的任职时间和变动情况',
                      '分析公司的薪酬体系和绩效考核机制',
                      '评估股权激励计划的设计和执行效果',
                      '评估激励机制对核心人才保留的效果'
                    ],
                    enabled: true,
                    dependencies: ['3.1', '3.2']
                  }
                ]
              },
              {
                number: 4,
                title: '项目公司的治理结构',
                description: '分析公司组织架构、内部管理制度、治理水平和风险管控机制',
                wordCount: { min: 9700, max: 13700 },
                researchPoints: [
                  '描述公司当前的组织架构（若可公开获取或推断）',
                  '分析该组织架构与公司战略和业务发展的匹配程度，以及运作效率',
                  '基于公开信息评估公司主要内部管理制度的健全性',
                  '分析公司的薪酬体系和股权激励计划（若有）对核心人才的吸引和保留效果',
                  '评估董事会、监事会、股东大会等治理机构的运作情况',
                  '分析公司的风险管理体系和内控制度'
                ],
                enabled: true,
                priority: 'medium',
                sections: [
                  {
                    id: '4.1',
                    number: '4.1',
                    title: '组织机构设置',
                    description: '分析组织架构的合理性、运作效率和适应性',
                    wordCount: { min: 2200, max: 3200 },
                    researchPoints: [
                      '描述公司当前的组织架构和部门设置',
                      '评估组织架构与公司战略和业务发展的匹配程度',
                      '评估组织架构的灵活性和变革适应能力',
                      '分析架构调整对业务发展的支持作用'
                    ],
                    enabled: true,
                    dependencies: []
                  },
                  {
                    id: '4.2',
                    number: '4.2',
                    title: '内部管理制度、薪酬激励制度、授权与执行力',
                    description: '评估管理制度的健全性、薪酬激励的有效性和授权执行的效率',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '评估公司主要内部管理制度的健全性和执行情况',
                      '分析公司的薪酬体系和激励机制设计',
                      '评估公司内部的授权机制和决策流程',
                      '分析从决策到执行的效率和执行力'
                    ],
                    enabled: true,
                    dependencies: ['4.1']
                  },
                  {
                    id: '4.3',
                    number: '4.3',
                    title: '公司治理水平评估',
                    description: '评估治理机构运作、独立董事作用和信息披露质量',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '评估董事会、监事会、股东大会等治理机构的运作情况',
                      '分析独立董事的独立性和专业性',
                      '评估公司信息披露的及时性、准确性和透明度',
                      '分析投资者关系管理的质量'
                    ],
                    enabled: true,
                    dependencies: ['4.1', '4.2']
                  },
                  {
                    id: '4.4',
                    number: '4.4',
                    title: '风险管控机制',
                    description: '评估风险管理体系、内控制度和风险监控机制',
                    wordCount: { min: 2200, max: 3200 },
                    researchPoints: [
                      '分析公司的风险管理框架和组织体系',
                      '评估内部控制制度的设计和执行有效性',
                      '分析风险识别、评估、应对和监控的流程',
                      '评估风险预警和应急响应机制'
                    ],
                    enabled: true,
                    dependencies: ['4.2', '4.3']
                  }
                ]
              },
              {
                number: 5,
                title: '项目公司的发展情况与投资回报',
                description: '分析企业发展轨迹、投资回报、重大事件影响和退出机会',
                wordCount: { min: 13600, max: 18500 },
                researchPoints: [
                  '回顾投资时对项目公司的核心发展逻辑和预期',
                  '评估项目公司当前实际的发展路径、业务进展、战略重点是否与当初的投资设想基本吻合',
                  '概述公司知识产权（专利、商标、著作权等）的最新状况及其对核心竞争力的支撑',
                  '分析公司在报告期内的股价表现和市值变化',
                  '梳理报告期内发生的重大事件',
                  '识别并评估公司当前面临的主要内部风险和外部风险',
                  '从投资方角度，分析当前市场环境下可能的退出路径'
                ],
                enabled: true,
                priority: 'high',
                sections: [
                  {
                    id: '5.1',
                    number: '5.1',
                    title: '企业发展模式与投资前设想的吻合度',
                    description: '对比实际发展与投资预期，分析偏差及其影响',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '回顾投资时对项目公司的核心发展逻辑和预期',
                      '分析项目公司实际的发展路径和业务进展',
                      '识别和分析主要偏差及其原因',
                      '评估偏差对投资价值的影响'
                    ],
                    enabled: true,
                    dependencies: []
                  },
                  {
                    id: '5.2',
                    number: '5.2',
                    title: 'IPR情况、市值反映',
                    description: '分析知识产权状况和市值表现与基本面的匹配度',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '概述公司知识产权（专利、商标、著作权等）的最新状况',
                      '分析公司在报告期内的股价表现和市值变化',
                      '分析市值表现与公司基本面的匹配程度',
                      '评估市场对公司价值的认知和预期'
                    ],
                    enabled: true,
                    dependencies: ['5.1']
                  },
                  {
                    id: '5.3',
                    number: '5.3',
                    title: '重大事件的调研、尽职调查',
                    description: '梳理重大事件并评估对公司各方面的影响',
                    wordCount: { min: 3000, max: 3900 },
                    researchPoints: [
                      '梳理报告期内发生的资产处置、关联交易、对外担保、股东变化、诉讼等重大事件',
                      '评估各重大事件对公司财务、运营、声誉等方面的影响',
                      '识别重大事件可能带来的潜在风险',
                      '评估公司的应对措施和风险控制能力'
                    ],
                    enabled: true,
                    dependencies: ['5.1', '5.2']
                  },
                  {
                    id: '5.4',
                    number: '5.4',
                    title: '风险控制，异情预警',
                    description: '识别风险点，评估控制措施，建立预警机制',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '识别公司当前面临的主要内部风险和外部风险',
                      '评估公司现有的风险管理和内部控制措施',
                      '提出需要重点关注的潜在异情或预警信号',
                      '建立动态监控和预警指标体系'
                    ],
                    enabled: true,
                    dependencies: ['5.3']
                  },
                  {
                    id: '5.5',
                    number: '5.5',
                    title: '项目退出机会',
                    description: '分析退出路径、时机和策略，评估潜在回报',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '分析当前市场环境下可能的退出路径（IPO、并购、股权转让等）',
                      '分析最佳退出时机和市场窗口',
                      '提出具体的退出策略和实施路径',
                      '分析退出的潜在回报和风险'
                    ],
                    enabled: true,
                    dependencies: ['5.3', '5.4']
                  }
                ]
              },
              {
                number: 6,
                title: '总结与评价',
                description: '综合评价公司表现，提出投资建议和关键监控点',
                wordCount: { min: 12200, max: 17200 },
                researchPoints: [
                  '简要概括报告期内公司在商业模式、经营状况、管理团队、治理结构、发展情况等方面的核心观察点和关键变化',
                  '客观评价公司在报告期内的主要成就和突出表现',
                  '诚实指出公司存在的不足和需要改进的方面',
                  '结合公司基本面、行业前景、风险因素和潜在回报，对公司的整体投资价值进行综合评价',
                  '基于以上分析，给出明确的投资操作建议',
                  '详细列出未来一段时间内，为有效进行投后管理，需要对项目公司持续监控的关键指标和事项'
                ],
                enabled: true,
                priority: 'high',
                sections: [
                  {
                    id: '6.1',
                    number: '6.1',
                    title: '核心发现总结',
                    description: '提炼各章节重要发现，总结核心优势和变化趋势',
                    wordCount: { min: 2200, max: 3200 },
                    researchPoints: [
                      '简要概括报告期内公司的核心观察点和关键变化',
                      '总结公司的核心竞争优势和关键成功因素',
                      '识别公司在各方面的发展趋势和变化轨迹',
                      '评估变化对未来发展的意义'
                    ],
                    enabled: true,
                    dependencies: []
                  },
                  {
                    id: '6.2',
                    number: '6.2',
                    title: '经营亮点与不足',
                    description: '客观对比分析公司的成就和不足，评估整体表现平衡性',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '客观评价公司在报告期内的主要成就和突出表现',
                      '诚实指出公司存在的不足和需要改进的方面',
                      '将亮点与不足进行对比分析',
                      '评估整体表现的平衡性'
                    ],
                    enabled: true,
                    dependencies: ['6.1']
                  },
                  {
                    id: '6.3',
                    number: '6.3',
                    title: '投资价值评估',
                    description: '综合分析投资价值要素，评估估值合理性和投资逻辑',
                    wordCount: { min: 2500, max: 3500 },
                    researchPoints: [
                      '结合公司基本面、行业前景、风险因素分析投资价值',
                      '分析公司当前估值水平的合理性',
                      '明确公司的投资价值定位和投资逻辑',
                      '分析投资的风险收益特征'
                    ],
                    enabled: true,
                    dependencies: ['6.1', '6.2']
                  },
                  {
                    id: '6.4',
                    number: '6.4',
                    title: '初步投资建议',
                    description: '给出明确投资建议，分析风险收益和操作策略',
                    wordCount: { min: 2200, max: 3200 },
                    researchPoints: [
                      '给出明确的投资操作建议（继续持有、增持、减持等）',
                      '分析投资建议的风险和收益预期',
                      '提出具体的投资操作策略和时间安排',
                      '分析操作的最佳时机和方式'
                    ],
                    enabled: true,
                    dependencies: ['6.3']
                  },
                  {
                    id: '6.5',
                    number: '6.5',
                    title: '投后管理关键监控点',
                    description: '制定详细的投后管理监控体系和关键指标跟踪计划',
                    wordCount: { min: 2800, max: 3800 },
                    researchPoints: [
                      '详细列出需要持续监控的关键财务指标',
                      '明确需要关注的核心产品/技术研发进展',
                      '确定核心管理团队稳定性的监控机制',
                      '制定下一年度商业计划执行的监控方案'
                    ],
                    enabled: true,
                    dependencies: ['6.3', '6.4']
                  }
                ]
              }
            ],
            defaultConfig: {
              mode: 'section',
              format: {
                titleFormat: 'numbered',
                includeTableOfContents: true,
                includeCitations: true,
                includeImages: true,
                paragraphSpacing: 'normal'
              },
              avoidDuplication: true,
              enableContextMemory: true,
              chapterDelay: 2000,
              sectionDelay: 1000,
              style: 'business',
              languageRequirements: '请以中国官方/正式报告风格撰写：采用整段连贯叙述，避免分项符号和子标题罗列，用连接词自然过渡，确保逻辑严谨、语言精练庄重，将要点融入段落中阐述',
              enableQualityCheck: true,
              customInstructions: '重点关注投资分析视角，确保数据支撑充分，分析客观深入，结论明确可操作。优先使用公司最新披露的财务报告（如2024年年报、2025年第一季度报告）及最新市场动态。每个分析观点都需要有具体的数据或事实支撑，避免空泛的描述。'
            }
          }
        ];

        set((state) => ({
          templates: [...state.templates, ...builtInTemplates.filter(
            template => !state.templates.some(t => t.id === template.id)
          )]
        }));
        
        console.log('✅ 内置模板初始化完成', { templateCount: builtInTemplates.length });
      },

      // 强制更新内置模板（用于开发时更新模板内容）
      forceUpdateBuiltInTemplates: () => {
        // 删除所有现有的内置模板，保留用户自定义模板
        set((state) => ({
          templates: state.templates.filter(t => !t.isBuiltIn)
        }));

        // 重新创建内置模板（与 initializeBuiltInTemplates 相同的逻辑）
        get().initializeBuiltInTemplates();
        
        console.log('🔄 强制更新内置模板完成');
      },

      // 进度管理
      updateProgress: (progress: Partial<WritingProgress>) => {
        set((state) => ({
          writingProgress: state.writingProgress 
            ? { ...state.writingProgress, ...progress, lastUpdateTime: new Date() }
            : {
                currentChapter: 0,
                currentSection: 0,
                completedChapters: [],
                completedSections: [],
                totalProgress: 0,
                estimatedTimeRemaining: 0,
                startTime: new Date(),
                lastUpdateTime: new Date(),
                ...progress,
              },
        }));
      },
      
      resetProgress: () => {
        set({ writingProgress: null });
      },
      
      // 配置管理
      updateGenerationConfig: (config: Partial<OutlineGenerationConfig>) => {
        set((state) => ({
          generationConfig: { ...state.generationConfig, ...config },
        }));
      },
      
      updateWritingConfig: (config: Partial<WritingConfig>) => {
        const { currentOutline } = get();
        if (!currentOutline) return;
        
        const updatedOutline = {
          ...currentOutline,
          writingConfig: { ...currentOutline.writingConfig, ...config },
          updatedAt: new Date(),
        };
        
        get().updateOutline(updatedOutline);
      },
      
      // 验证
      validateOutline: (outline: ResearchOutline): OutlineValidationResult => {
        const errors: any[] = [];
        const warnings: any[] = [];
        const suggestions: string[] = [];
        
        // 基本验证
        if (!outline.title.trim()) {
          errors.push({
            type: 'missing_title',
            message: '大纲标题不能为空',
          });
        }
        
        if (outline.chapters.length === 0) {
          errors.push({
            type: 'missing_title',
            message: '大纲至少需要包含一个章节',
          });
        }
        
        // 章节验证
        outline.chapters.forEach(chapter => {
          if (!chapter.title.trim()) {
            errors.push({
              type: 'missing_title',
              message: `第${chapter.number}章标题不能为空`,
              chapterId: chapter.id,
            });
          }
          
          if (chapter.wordCount.min >= chapter.wordCount.max) {
            errors.push({
              type: 'invalid_word_count',
              message: `第${chapter.number}章字数范围设置错误`,
              chapterId: chapter.id,
            });
          }
          
          if (chapter.wordCount.min < 500) {
            warnings.push({
              type: 'low_word_count',
              message: `第${chapter.number}章最小字数可能过少`,
              chapterId: chapter.id,
            });
          }
          
          if (chapter.researchPoints.length === 0) {
            warnings.push({
              type: 'missing_research_points',
              message: `第${chapter.number}章缺少研究要点`,
              chapterId: chapter.id,
            });
          }
        });
        
        // 建议
        if (outline.chapters.length > 8) {
          suggestions.push('章节数量较多，建议考虑合并相关章节');
        }
        
        if (outline.chapters.some(c => c.sections.length > 6)) {
          suggestions.push('某些章节的子章节过多，建议重新组织结构');
        }
        
        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          suggestions,
        };
      },
      
      // 导入导出
      exportOutline: (id: string) => {
        const outline = get().getOutlineById(id);
        if (!outline) return null;
        
        const exportData = {
          outline,
          metadata: {
            exportedAt: new Date(),
            exportedBy: 'user',
            version: '1.0.0',
          },
        };
        
        return JSON.stringify(exportData, null, 2);
      },
      
      importOutline: (data: string) => {
        try {
          const parsed = JSON.parse(data);
          const outline = parsed.outline;
          
          // 重新生成ID避免冲突
          const importedOutline: ResearchOutline = {
            ...outline,
            id: nanoid(),
            title: `${outline.title} (导入)`,
            createdAt: new Date(),
            updatedAt: new Date(),
            chapters: outline.chapters.map((chapter: any) => ({
              ...chapter,
              id: nanoid(),
              sections: chapter.sections.map((section: any) => ({
                ...section,
                id: nanoid(),
              })),
            })),
          };
          
          set((state) => ({
            outlines: [importedOutline, ...state.outlines],
          }));
          
          return importedOutline;
        } catch (error) {
          console.error('Failed to import outline:', error);
          return null;
        }
      },
      
      // 工具方法
      getOutlineById: (id: string) => {
        return get().outlines.find(o => o.id === id) || null;
      },
      
      getTemplateById: (id: string) => {
        return get().templates.find(t => t.id === id) || null;
      },
      
      searchOutlines: (query: string) => {
        const { outlines } = get();
        if (!query.trim()) return outlines;
        
        const lowercaseQuery = query.toLowerCase();
        return outlines.filter(outline => 
          outline.title.toLowerCase().includes(lowercaseQuery) ||
          outline.description.toLowerCase().includes(lowercaseQuery) ||
          outline.chapters.some(chapter => 
            chapter.title.toLowerCase().includes(lowercaseQuery) ||
            chapter.description.toLowerCase().includes(lowercaseQuery)
          )
        );
      },
      
      getOutlineStats: (id: string) => {
        const outline = get().getOutlineById(id);
        if (!outline) return null;
        
        const totalChapters = outline.chapters.length;
        const totalSections = outline.chapters.reduce((sum, chapter) => sum + chapter.sections.length, 0);
        const totalWords: WordCountRange = outline.chapters.reduce(
          (sum, chapter) => ({
            min: sum.min + chapter.wordCount.min,
            max: sum.max + chapter.wordCount.max,
          }),
          { min: 0, max: 0 }
        );
        
        return {
          totalChapters,
          totalSections,
          totalWords,
        };
      },
      
      // 章节映射方法
      generateChapterMap: (outlineId?: string) => {
        const outline = outlineId ? get().getOutlineById(outlineId) : get().currentOutline;
        if (!outline) return null;
        
        const chapterMap = new Map<string, import("@/components/MagicDown").ChapterInfo>();
        
        outline.chapters.forEach((chapter, chapterIndex) => {
          // 添加主章节
          const mainChapterKey = `chapter_${chapter.number}`;
          const mainChapterInfo: import("@/components/MagicDown").ChapterInfo = {
            id: chapter.id,
            title: chapter.title,
            level: 1,
            type: 'main',
            editable: true,
            order: chapterIndex,
            chapterData: chapter
          };
          chapterMap.set(mainChapterKey, mainChapterInfo);
          chapterMap.set(chapter.title, mainChapterInfo); // 也通过标题访问
          
          // 添加子章节
          chapter.sections.forEach((section, sectionIndex) => {
            const sectionKey = `section_${section.number}`;
            const sectionInfo: import("@/components/MagicDown").ChapterInfo = {
              id: section.id,
              title: section.title,
              level: 2,
              type: 'sub',
              editable: true,
              parentId: chapter.id,
              order: sectionIndex,
              chapterData: section
            };
            chapterMap.set(sectionKey, sectionInfo);
            chapterMap.set(section.title, sectionInfo); // 也通过标题访问
            
            // 添加完整的章节标题格式，如"1.1、具体标题"
            const fullSectionTitle = `${section.number}、${section.title}`;
            chapterMap.set(fullSectionTitle, sectionInfo);
          });
        });
        
        console.log('🗺️ 生成的章节映射:', chapterMap);
        return chapterMap;
      },
    }),
    {
      name: "outline-store",
      onRehydrateStorage: () => (state) => {
        console.log('🔄 重新水化状态:', state);
        if (state) {
          // 恢复Date对象
          if (state.currentOutline) {
            state.currentOutline.createdAt = new Date(state.currentOutline.createdAt);
            state.currentOutline.updatedAt = new Date(state.currentOutline.updatedAt);
          }
          state.outlines = state.outlines.map((outline: any) => ({
            ...outline,
            createdAt: new Date(outline.createdAt),
            updatedAt: new Date(outline.updatedAt),
          }));
          state.templates = state.templates.map((template: any) => ({
            ...template,
            createdAt: new Date(template.createdAt),
            updatedAt: new Date(template.updatedAt),
          }));
        }
      },
    }
  )
); 