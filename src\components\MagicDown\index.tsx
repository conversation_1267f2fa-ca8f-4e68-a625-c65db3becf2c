"use client";
import dynamic from "next/dynamic";
import { useState, useRef, memo, useCallback, type ReactNode } from "react";
import { useTranslation } from "react-i18next";
import copy from "copy-to-clipboard";
import { FilePenLine, Save, Copy, CopyCheck } from "lucide-react";
import FloatingMenu from "@/components/Internal/FloatingMenu";
import { Button } from "@/components/Internal/Button";
import { useMobile } from "@/hooks/useMobile";
import { cn } from "@/utils/style";

const Editor = dynamic(() => import("./Editor"));
const View = dynamic(() => import("./View"));

import { OutlineChapter, OutlineSection } from "@/types/outline";

// 章节信息接口
export interface ChapterInfo {
  id: string;
  title: string;
  level: number; // 1=主章节, 2=子章节
  type: 'main' | 'sub';
  editable: boolean;
  parentId?: string;
  order: number;
  chapterData?: OutlineChapter | OutlineSection; // 原始大纲数据
}

type Props = {
  className?: string;
  value: string;
  onChange: (value: string) => void;
  hideTools?: boolean;
  fixedTopOffset?: number;
  fixedRightOffset?: number;
  tools?: ReactNode;
  enableChapterEdit?: boolean;
  chapterMap?: Map<string, ChapterInfo>; // 章节映射数据
  onChapterEdit?: (chapterInfo: ChapterInfo) => void;
};

function MagicDown({
  value,
  onChange,
  className,
  hideTools,
  fixedTopOffset,
  fixedRightOffset,
  tools,
  enableChapterEdit = false,
  chapterMap,
  onChapterEdit,
}: Props) {
  const { t } = useTranslation();
  const isMobile = useMobile(450);
  const containerRef = useRef<HTMLDivElement>(null);
  const [mode, setMode] = useState<"editor" | "view">("view");
  const [waitingCopy, setWaitingCopy] = useState<boolean>(false);

  // 优化onChange回调，避免不必要的重渲染
  const optimizedOnChange = useCallback((newValue: string) => {
    if (newValue !== value) {
      onChange(newValue);
    }
  }, [value, onChange]);

  const handleCopy = useCallback(() => {
    setWaitingCopy(true);
    copy(value);
    setTimeout(() => {
      setWaitingCopy(false);
    }, 1200);
  }, [value]);

  const handleModeToggle = useCallback(() => {
    setMode(prevMode => prevMode === "view" ? "editor" : "view");
  }, []);

  // 检测是否包含 Mermaid 内容
  const hasMermaid = value.includes('```mermaid');
  
  return (
    <div className={cn("relative", className)} ref={containerRef}>
      {mode === "view" ? (
        <div className={cn("magicdown-view prose prose-slate dark:prose-invert max-w-full", {
          "mermaid-view": hasMermaid
        })}>
          <View
            enableChapterEdit={enableChapterEdit}
            chapterMap={chapterMap}
            onChapterEdit={onChapterEdit}
          >
            {value}
          </View>
        </div>
      ) : (
        <div className="magicdown-editor my-2">
          <Editor defaultValue={value} onChange={optimizedOnChange}></Editor>
        </div>
      )}
      
      {!hideTools ? (
        <FloatingMenu
          targetRef={containerRef}
          fixedTopOffset={fixedTopOffset ?? 16}
          fixedRightOffset={fixedRightOffset ?? (isMobile ? 0 : -70)}
        >
          <div className="flex flex-col gap-1 border rounded-full py-2 p-1 bg-white dark:bg-slate-800 max-sm:opacity-80 max-sm:hover:opacity-100 print:hidden">
            {mode === "view" ? (
              <Button
                className="float-menu-button"
                title={t("research.common.edit")}
                side="left"
                sideoffset={8}
                size="icon"
                variant="ghost"
                onClick={handleModeToggle}
              >
                <FilePenLine />
              </Button>
            ) : (
              <Button
                className="float-menu-button"
                title={t("research.common.save")}
                side="left"
                sideoffset={8}
                size="icon"
                variant="ghost"
                onClick={handleModeToggle}
              >
                <Save />
              </Button>
            )}
            <Button
              className="float-menu-button"
              title={t("research.common.copy")}
              side="left"
              sideoffset={8}
              size="icon"
              variant="ghost"
              onClick={handleCopy}
            >
              {waitingCopy ? (
                <CopyCheck className="h-full w-full text-green-500" />
              ) : (
                <Copy className="h-full w-full" />
              )}
            </Button>
            {tools ? tools : null}
          </div>
        </FloatingMenu>
      ) : null}
    </div>
  );
}

export default memo(MagicDown);
