import dynamic from "next/dynamic";
import { useMemo, memo, useState } from "react";
import Markdown, { type Options } from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import remarkBreaks from "remark-breaks";
import rehypeHighlight from "rehype-highlight";
import rehypeKatex from "rehype-katex";
import { useSettingStore } from "@/store/setting";
import { clsx } from "clsx";
import { animateText } from "@/utils/animate-text";
import { omit } from "radash";
import { Button } from "@/components/ui/button";
import { Edit3 } from "lucide-react";
import React from "react";
import { ChapterInfo } from "./index";

import "katex/dist/katex.min.css";
import "./style.css";
import "./highlight.css";

const Code = dynamic(() => import("./Code"));
const Mermaid = dynamic(() => import("./Mermaid"));
const ChapterEditDialog = dynamic(() => import("../Research/FinalReport/ChapterEditDialog"));

interface MagicdownProps extends Options {
  enableChapterEdit?: boolean;
  chapterMap?: Map<string, ChapterInfo>;
  onChapterEdit?: (chapterInfo: ChapterInfo) => void;
}

function Magicdown({ 
  children: content, 
  enableChapterEdit = false,
  chapterMap,
  onChapterEdit,
  ...rest 
}: MagicdownProps) {
  const { language } = useSettingStore();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingChapter, setEditingChapter] = useState<{
    id: string;
    title: string;
    content: string;
  } | null>(null);

  const remarkPlugins = useMemo(
    () => rest.remarkPlugins ?? [],
    [rest.remarkPlugins]
  );
  const rehypePlugins = useMemo(
    () => rest.rehypePlugins ?? [],
    [rest.rehypePlugins]
  );
  const components = useMemo(() => rest.components ?? {}, [rest.components]);

  // 章节识别函数 - 优先使用章节映射，备用正则表达式
  const isChapterTitle = (title: string): { 
    isChapter: boolean; 
    isSubChapter: boolean; 
    chapterInfo?: ChapterInfo 
  } => {
    if (!title || title.trim().length === 0) return { isChapter: false, isSubChapter: false };
    
    const cleanTitle = title.trim();
    
    // 优先使用章节映射查找
    if (chapterMap) {
      // 尝试多种匹配方式
      const exactMatch = chapterMap.get(cleanTitle);
      if (exactMatch) {
        // 判断是否为子章节 - 只有明确标记为'sub'类型的才是子章节
        const isSubChapter = exactMatch.type === 'sub';
        return { isChapter: true, isSubChapter, chapterInfo: exactMatch };
      }
      
      // 尝试部分匹配
      for (const [key, info] of chapterMap) {
        if (key.includes(cleanTitle) || cleanTitle.includes(key)) {
          // 判断是否为子章节 - 只有明确标记为'sub'类型的才是子章节
          const isSubChapter = info.type === 'sub';
          return { isChapter: true, isSubChapter, chapterInfo: info };
        }
      }
      
      // 尝试通过标题匹配
      for (const [, info] of chapterMap) {
        if (info.title === cleanTitle || info.title.includes(cleanTitle) || cleanTitle.includes(info.title)) {
          // 判断是否为子章节 - 只有明确标记为'sub'类型的才是子章节
          const isSubChapter = info.type === 'sub';
          return { isChapter: true, isSubChapter, chapterInfo: info };
        }
      }
    }
    
    // 排除报告主标题
    if (cleanTitle.length > 25 && (cleanTitle.includes('公司') || cleanTitle.includes('股份') || cleanTitle.includes('有限'))) {
      return { isChapter: false, isSubChapter: false };
    }
    
    // 备用方案：使用正则表达式匹配
    const isMainChapter = /^第[一二三四五六七八九十\d]+章/.test(cleanTitle);
    const isSubChapterPattern = /^\d+\.\d+、/.test(cleanTitle);
    const isSimpleChapter = /^\d+、/.test(cleanTitle);
    const isChineseChapter = /^[一二三四五六七八九十]+、/.test(cleanTitle);
    const isDeepSubChapter = /^\d+\.\d+\.\d+/.test(cleanTitle);
    
    // 判断是否为任何类型的章节
    const isAnyChapter = (isMainChapter || isSubChapterPattern || isSimpleChapter || isChineseChapter) && !isDeepSubChapter;
    
    // 判断是否为子章节（根据正则表达式）
    // 只有满足子章节模式的才被认为是子章节
    const isSubChapter = isSubChapterPattern || isDeepSubChapter;
    
    // 🔥 改进的匹配逻辑：如果有chapterMap但没找到精确匹配，尝试模糊匹配
    if (chapterMap) {
      // 尝试通过编号模糊匹配（适应生成内容的格式变化）
      for (const [, info] of chapterMap) {
        if (info.chapterData && (info.chapterData as any).number) {
          const chapterNumber = (info.chapterData as any).number;
          
          // 检查标题是否包含章节编号
          if (cleanTitle.includes(chapterNumber)) {
            console.log(`🎯 通过编号模糊匹配到章节: ${chapterNumber}`);
            const isSubChapter = info.type === 'sub';
            return { isChapter: true, isSubChapter, chapterInfo: info };
          }
        }
      }
      
      // 如果仍未找到，但符合章节格式，则允许正则表达式判断
      // 这样可以兼容新生成的内容
      if (isAnyChapter) {
        console.log(`🎯 未在映射中找到但符合章节格式，使用正则判断: ${cleanTitle}`);
        return { isChapter: isAnyChapter, isSubChapter };
      }
      
      return { isChapter: false, isSubChapter: false };
    }
    
    return { isChapter: isAnyChapter, isSubChapter };
  };

  const handleChapterEdit = (title: string, level: number, existingChapterInfo?: ChapterInfo) => {
    // 如果已有章节信息，直接使用
    const chapterInfo: ChapterInfo = existingChapterInfo || {
      id: `chapter_${level}_${Date.now()}`,
      title: title,
      level: level,
      type: level <= 2 ? 'main' : 'sub',
      editable: true,
      order: 0
    };
    
    setEditingChapter({
      id: chapterInfo.id,
      title: chapterInfo.title,
      content: `这是 ${title} 的章节内容（简化版本）`
    });
    setEditDialogOpen(true);
    onChapterEdit?.(chapterInfo);
  };

  const createHeaderComponent = (level: number) => {
    return (props: any) => {
      const { children, ...rest } = props;
      
      // 递归提取文本内容的函数
      const extractText = (node: any): string => {
        if (typeof node === 'string') {
          return node;
        }
        if (typeof node === 'number') {
          return node.toString();
        }
        if (Array.isArray(node)) {
          return node.map(extractText).join('');
        }
        if (node && typeof node === 'object') {
          if (node.props && node.props.children) {
            return extractText(node.props.children);
          }
          if (node.children) {
            return extractText(node.children);
          }
        }
        return '';
      };
      
      const title = extractText(children).trim();
      
      // 检查是否应该显示编辑按钮
      const chapterResult = isChapterTitle(title);
      
      // 修改显示逻辑：只有当是子章节时才显示编辑按钮
      // 如果有chapterMap，则必须在chapterMap中找到且类型为'sub'
      // 如果没有chapterMap，则使用正则表达式判断的结果
      const shouldShowButton = enableChapterEdit && chapterResult.isChapter && chapterResult.isSubChapter;
      
      // 生成章节ID，用于标识段落
      const chapterId = chapterResult.chapterInfo?.id || `chapter_${level}_${title.replace(/\s+/g, '_')}`;
      const chapterType = chapterResult.isSubChapter ? 'sub' : 'main';
      
      // 构建标题元素，如果需要显示按钮则添加按钮
      const headerElement = React.createElement(
        `h${level}` as any,
        {
          ...omit(rest, ["node"]),
          className: "chapter-title-container flex items-center",
          // 添加自定义data属性，用于标识章节
          "data-chapter-id": chapterId,
          "data-chapter-type": chapterType,
          "data-chapter-level": level,
          "data-chapter-title": title,
          "data-chapter-editable": shouldShowButton ? "true" : "false"
        },
        children,
        shouldShowButton && React.createElement(
          Button,
          {
            variant: "outline" as const,
            size: "sm" as const,
            className: "ml-2 chapter-edit-button-fixed",
            onClick: (e: React.MouseEvent) => {
              e.preventDefault();
              e.stopPropagation();
              handleChapterEdit(title, level, chapterResult.chapterInfo);
            },
            title: `编辑章节: ${title}`,
          },
          React.createElement(Edit3, { className: "w-4 h-4 mr-1" }),
          "编辑"
        )
      );
      
      return headerElement;
    };
  };

  // 如果启用章节编辑，创建增强的标题组件
  const enhancedComponents = enableChapterEdit ? {
    ...components,
    h1: createHeaderComponent(1),
    h2: createHeaderComponent(2),
    h3: createHeaderComponent(3),
    h4: createHeaderComponent(4),
    h5: createHeaderComponent(5),
    h6: createHeaderComponent(6),
  } : components;



  return (
    <>
      <Markdown
        {...rest}
        remarkPlugins={[remarkGfm, remarkMath, remarkBreaks, ...remarkPlugins]}
        rehypePlugins={[
          [rehypeHighlight, { detect: true, ignoreMissing: true }],
          rehypeKatex,
          animateText(language),
          ...rehypePlugins,
        ]}
        components={{
          pre: (props) => {
            const { children, className, ...rest } = props;
            return (
              <pre
                {...omit(rest, ["node"])}
                className={clsx("my-4 not-prose", className)}
              >
                {children}
              </pre>
            );
          },
          code: (props) => {
            const { children, className, ...rest } = props;
            if (className?.includes("hljs")) {
              const lang = /language-(\w+)/.exec(className || "");
              if (lang && lang[1] === "mermaid") {
                return <Mermaid>{children}</Mermaid>;
              }
              return (
                <Code lang={lang ? lang[1] : ""}>
                  <code
                    {...omit(rest, ["node"])}
                    className={clsx("break-all", className)}
                  >
                    {children}
                  </code>
                </Code>
              );
            } else {
              return (
                <code
                  {...omit(rest, ["node"])}
                  className={clsx("break-all", className)}
                >
                  {children}
                </code>
              );
            }
          },
          a: (props) => {
            const { children, className, href = "", target, ...rest } = props;
            if (/\.(aac|mp3|opus|wav)$/.test(href)) {
              return (
                <figure>
                  <audio controls src={href}></audio>
                </figure>
              );
            }
            if (/\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(href)) {
              return (
                <video controls width="99.9%">
                  <source src={href} />
                </video>
              );
            }
            const isInternal = /^\/#/i.test(href);
            const isReferenceLink = /^[0-9]*$/.test(children?.toString() || "");
            return (
              <a
                {...omit(rest, ["node"])}
                className={clsx("break-all", className, {
                  reference: isReferenceLink,
                })}
                href={href}
                target={isInternal ? "_self" : target ?? "_blank"}
              >
                {children}
              </a>
            );
          },
          img: (props) => {
            const { className, src, alt, ...rest } = props;
            return (
              <picture
                className={clsx(
                  "my-2 flex justify-center items-center w-4/5 max-sm:w-full h-[50vw] max-sm:h-80 m-auto",
                  className
                )}
              >
                <img
                  className="size-full object-cover rounded transition-all duration-200 ease-out"
                  {...omit(rest, ["node"])}
                  src={src}
                  alt={alt}
                  title={alt}
                  referrerPolicy="no-referrer"
                  rel="noopener noreferrer"
                />
              </picture>
            );
          },
          ...enhancedComponents,
        }}
      >
        {content}
      </Markdown>
      
      {/* 章节编辑弹窗 */}
      {enableChapterEdit && editingChapter && (
        <ChapterEditDialog
          open={editDialogOpen}
          onClose={() => {
            setEditDialogOpen(false);
            setEditingChapter(null);
          }}
          chapterTitle={editingChapter.title}
          chapterContent={editingChapter.content}
        />
      )}
    </>
  );
}

export default memo(Magicdown);
