import { create } from "zustand";
import { persist, type StorageValue } from "zustand/middleware";
import { pick } from "radash";
import { ResearchOutline, WritingProgress } from "@/types/outline";
import { researchStore } from "@/utils/storage";

// 🔥 新增：章节编辑专用资源类型
export interface ChapterEditResource {
    id: string;
    name: string;
    content: string;
    type: 'file' | 'text' | 'url';
    size: number;
    createdAt: number;
}

export interface TaskStore {
    id: string;
    question: string;
    resources: Resource[];
    query: string;
    questions: string;
    feedback: string;
    reportPlan: string;
    suggestion: string;
    tasks: SearchTask[];
    requirement: string;
    title: string;
    finalReport: string;
    sources: Source[];
    images: ImageSource[];
    knowledgeGraph: string;

    // 新增大纲相关字段
    researchOutline: ResearchOutline | null;
    writingProgress: WritingProgress | null;
    isOutlineDriven: boolean; // 是否使用大纲驱动模式
    chapterResearchProgress: string; // 🔥 新增：用于显示章节研究进度的状态

    // 🔥 新增：当前章节上下文，用于指导研究任务生成
    currentChapterContext: {
        chapterIndex: number;
        chapterTitle: string;
        chapterDescription: string;
        researchPoints: string[];
        originalResearchTopic: string; // 🔥 新增：原始研究主题
    } | null;

    // 🔥 新增：章节编辑相关状态
    chapterEditState: {
        isEditing: boolean;
        editingChapterId: string | null;
        editingDescription: string;
        editingResources: ChapterEditResource[];
        editingProgress: string;
    };
}

interface TaskFunction {
    update: (tasks: SearchTask[]) => void;
    setId: (id: string) => void;
    setTitle: (title: string) => void;
    setSuggestion: (suggestion: string) => void;
    setRequirement: (requirement: string) => void;
    setQuery: (query: string) => void;
    updateTask: (query: string, task: Partial<SearchTask>) => void;
    removeTask: (query: string) => boolean;
    setQuestion: (question: string) => void;
    addResource: (resource: Resource) => void;
    updateResource: (id: string, resource: Partial<Resource>) => void;
    removeResource: (id: string) => boolean;
    updateQuestions: (questions: string) => void;
    updateReportPlan: (plan: string) => void;
    updateFinalReport: (report: string) => void;
    setSources: (sources: Source[]) => void;
    setImages: (images: Source[]) => void;
    setFeedback: (feedback: string) => void;
    updateKnowledgeGraph: (knowledgeGraph: string) => void;
    clear: () => void;
    reset: (preserveTopic?: boolean) => void;
    resetWithTopicProtection: () => void;
    backup: () => TaskStore;
    restore: (taskStore: TaskStore) => void;

    // 新增大纲相关方法
    setResearchOutline: (outline: ResearchOutline | null) => void;
    updateWritingProgress: (progress: Partial<WritingProgress>) => void;
    setOutlineDriven: (enabled: boolean) => void;
    resetWritingProgress: () => void;
    setWritingProgress: (progress: WritingProgress) => void;
    setChapterResearchProgress: (progress: string) => void; // 🔥 新增：更新章节研究进度的 Action

    // 🔥 新增：章节上下文管理方法
    setCurrentChapterContext: (context: {
        chapterIndex: number;
        chapterTitle: string;
        chapterDescription: string;
        researchPoints: string[];
        originalResearchTopic: string; // 🔥 新增：原始研究主题
    } | null) => void;
    clearCurrentChapterContext: () => void;

    // 🔥 新增：章节编辑相关方法
    startChapterEdit: (chapterId: string, description: string) => void;
    stopChapterEdit: () => void;
    updateChapterEditDescription: (description: string) => void;
    addChapterEditResource: (resource: ChapterEditResource) => void;
    removeChapterEditResource: (resourceId: string) => void;
    updateChapterEditProgress: (progress: string) => void;
    replaceChapterContent: (chapterId: string, newContent: string) => void;
}

const defaultValues: TaskStore = {
    id: "",
    question: "",
    resources: [],
    query: "",
    questions: "",
    feedback: "",
    reportPlan: "",
    suggestion: "",
    tasks: [],
    requirement: "",
    title: "",
    finalReport: "",
    sources: [],
    images: [],
    knowledgeGraph: "",

    // 新增字段的默认值
    researchOutline: null,
    writingProgress: null,
    isOutlineDriven: false,
    chapterResearchProgress: "", // 🔥 初始化新状态
    currentChapterContext: null, // 🔥 新增：当前章节上下文，用于指导研究任务生成
    chapterEditState: {
        isEditing: false,
        editingChapterId: null,
        editingDescription: "",
        editingResources: [],
        editingProgress: "",
    },
};

export const useTaskStore = create(
    persist<TaskStore & TaskFunction>(
        (set, get) => ({
            ...defaultValues,
            update: (tasks) => {
                set((state) => {
                    // 如果是大纲驱动模式，需要保留所有章节的任务
                    if (state.isOutlineDriven) {
                        // 使用Map进行去重合并：以query为键，新任务覆盖旧任务
                        const merged = new Map<string, SearchTask>();

                        // 首先添加现有任务
                        state.tasks.forEach((task) => merged.set(task.query, task));

                        // 然后添加新任务（会覆盖同query的旧任务）
                        tasks.forEach((task) => merged.set(task.query, task));

                        return { tasks: Array.from(merged.values()) };
                    } else {
                        // 传统模式：直接替换
                        return { tasks: [...tasks] };
                    }
                });
            },
            setId: (id) => set(() => ({ id })),
            setTitle: (title) => set(() => ({ title })),
            setSuggestion: (suggestion) => set(() => ({ suggestion })),
            setRequirement: (requirement) => set(() => ({ requirement })),
            setQuery: (query) => set(() => ({ query })),
            updateTask: (query, task) => {
                const newTasks = get().tasks.map((item) => {
                    return item.query === query ? { ...item, ...task } : item;
                });
                set(() => ({ tasks: [...newTasks] }));
            },
            removeTask: (query) => {
                set((state) => ({
                    tasks: state.tasks.filter((task) => task.query !== query),
                }));
                return true;
            },
            setQuestion: (question) => set(() => ({ question })),
            addResource: (resource) =>
                set((state) => ({ resources: [resource, ...state.resources] })),
            updateResource: (id, resource) => {
                const newResources = get().resources.map((item) => {
                    return item.id === id ? { ...item, ...resource } : item;
                });
                set(() => ({ resources: [...newResources] }));
            },
            removeResource: (id) => {
                set((state) => ({
                    resources: state.resources.filter((resource) => resource.id !== id),
                }));
                return true;
            },
            updateQuestions: (questions) => set(() => ({ questions })),
            updateReportPlan: (plan) => set(() => ({ reportPlan: plan })),
            updateFinalReport: (report) => set(() => ({ finalReport: report })),
            setSources: (sources) => set(() => ({ sources })),
            setImages: (images) => set(() => ({ images })),
            setFeedback: (feedback) => set(() => ({ feedback })),
            updateKnowledgeGraph: (knowledgeGraph) => set(() => ({ knowledgeGraph })),
            clear: () => {
                // 🔥 保存当前研究主题，避免被清空
                const currentQuestion = get().question;
                const currentTitle = get().title;
                const currentRequirement = get().requirement;

                set(() => ({ tasks: [] }));

                // 🔥 恢复研究主题相关信息
                if (currentQuestion) set(() => ({ question: currentQuestion }));
                if (currentTitle) set(() => ({ title: currentTitle }));
                if (currentRequirement) set(() => ({ requirement: currentRequirement }));
            },
            reset: (preserveTopic = false) => {
                // 🔥 保存当前研究主题，避免在重置状态时被清空
                const currentQuestion = preserveTopic ? get().question : "";
                const currentTitle = preserveTopic ? get().title : "";
                const currentRequirement = preserveTopic ? get().requirement : "";

                // 重置所有状态为默认值
                set(() => ({ ...defaultValues }));

                // 🔥 恢复研究主题相关信息（如果需要保护的话）
                if (preserveTopic) {
                    if (currentQuestion) set(() => ({ question: currentQuestion }));
                    if (currentTitle) set(() => ({ title: currentTitle }));
                    if (currentRequirement) set(() => ({ requirement: currentRequirement }));
                }
            },

            // 🔥 修复：专门的重置方法，确保保护研究主题
            resetWithTopicProtection: () => {
                get().reset(true);  // 传入 true 表示保护主题
            },
            backup: () => {
                return {
                    ...pick(get(), Object.keys(defaultValues) as (keyof TaskStore)[]),
                } as TaskStore;
            },
            restore: (taskStore) => set(() => ({ ...taskStore })),

            // 新增大纲相关方法
            setResearchOutline: (outline: ResearchOutline | null) => {
                set(() => ({
                    researchOutline: outline,
                    // 如果设置了大纲，自动启用大纲驱动模式
                    isOutlineDriven: outline !== null,
                }));
            },

            updateWritingProgress: (progress: Partial<WritingProgress>) => {
                set((state) => ({
                    writingProgress: state.writingProgress
                        ? { ...state.writingProgress, ...progress, lastUpdateTime: new Date() }
                        : {
                            currentChapter: 0,
                            currentSection: 0,
                            completedChapters: [],
                            completedSections: [],
                            totalProgress: 0,
                            estimatedTimeRemaining: 0,
                            startTime: new Date(),
                            lastUpdateTime: new Date(),
                            ...progress,
                        },
                }));
            },

            setOutlineDriven: (enabled: boolean) => {
                set(() => ({ isOutlineDriven: enabled }));
            },

            resetWritingProgress: () => {
                set(() => ({ writingProgress: null }));
            },
            setWritingProgress: (progress: WritingProgress) => {
                set(() => ({ writingProgress: progress }));
            },
            setChapterResearchProgress: (progress: string) => {
                set(() => ({ chapterResearchProgress: progress }));
            },

            // 🔥 新增：章节上下文管理方法
            setCurrentChapterContext: (context: {
                chapterIndex: number;
                chapterTitle: string;
                chapterDescription: string;
                researchPoints: string[];
                originalResearchTopic: string; // 🔥 新增：原始研究主题
            } | null) => {
                set(() => ({ currentChapterContext: context }));
            },
            clearCurrentChapterContext: () => {
                set(() => ({ currentChapterContext: null }));
            },

            // 🔥 新增：章节编辑相关方法
            startChapterEdit: (chapterId: string, description: string) => {
                set(() => ({
                    chapterEditState: {
                        isEditing: true,
                        editingChapterId: chapterId,
                        editingDescription: description,
                        editingResources: [],
                        editingProgress: "",
                    },
                }));
            },
            stopChapterEdit: () => {
                // 🔥 保存当前研究主题，避免被清空
                const currentQuestion = get().question;
                const currentTitle = get().title;
                const currentRequirement = get().requirement;

                set(() => ({
                    chapterEditState: {
                        isEditing: false,
                        editingChapterId: null,
                        editingDescription: "",
                        editingResources: [],
                        editingProgress: "",
                    },
                }));

                // 🔥 恢复研究主题相关信息
                if (currentQuestion) set(() => ({ question: currentQuestion }));
                if (currentTitle) set(() => ({ title: currentTitle }));
                if (currentRequirement) set(() => ({ requirement: currentRequirement }));
            },
            updateChapterEditDescription: (description: string) => {
                set((state) => ({
                    chapterEditState: {
                        ...state.chapterEditState,
                        editingDescription: description,
                    },
                }));
            },
            addChapterEditResource: (resource: ChapterEditResource) => {
                set((state) => ({
                    chapterEditState: {
                        ...state.chapterEditState,
                        editingResources: [...state.chapterEditState.editingResources, resource],
                    },
                }));
            },
            removeChapterEditResource: (resourceId: string) => {
                set((state) => ({
                    chapterEditState: {
                        ...state.chapterEditState,
                        editingResources: state.chapterEditState.editingResources.filter((resource) => resource.id !== resourceId),
                    },
                }));
            },
            updateChapterEditProgress: (progress: string) => {
                set((state) => ({
                    chapterEditState: {
                        ...state.chapterEditState,
                        editingProgress: progress,
                    },
                }));
            },
            replaceChapterContent: (chapterId: string, newContent: string) => {
                set((state) => ({
                    chapterEditState: {
                        ...state.chapterEditState,
                        editingResources: state.chapterEditState.editingResources.map((resource) =>
                            resource.id === chapterId ? { ...resource, content: newContent } : resource
                        ),
                    },
                }));
            },
        }),
        {
            name: "taskStore",
            version: 1,
            storage: {
                getItem: async (key: string) => {
                    return await researchStore.getItem<
                        StorageValue<TaskStore & TaskFunction>
                    >(key);
                },
                setItem: async (
                    key: string,
                    store: StorageValue<TaskStore & TaskFunction>
                ) => {
                    // 🔥 确保保存关键的研究主题相关字段
                    const fieldsToSave = [
                        "id", "question", "resources", "query", "questions", "feedback",
                        "reportPlan", "suggestion", "tasks", "requirement", "title",
                        "finalReport", "sources", "images", "knowledgeGraph",
                        "researchOutline", "writingProgress", "isOutlineDriven",
                        "chapterResearchProgress", "currentChapterContext", "chapterEditState"
                    ] as (keyof TaskStore)[];

                    return await researchStore.setItem(key, {
                        state: pick(store.state, fieldsToSave),
                        version: store.version,
                    });
                },
                removeItem: async (key: string) => await researchStore.removeItem(key),
            },
            // 🔥 添加数据恢复回调，确保研究主题正确恢复
            onRehydrateStorage: () => (state) => {
                if (state) {
                    // 🔥 确保研究主题相关字段不为 undefined
                    if (state.question === undefined) state.question = "";
                    if (state.requirement === undefined) state.requirement = "";
                    if (state.title === undefined) state.title = "";

                    console.log('🔄 TaskStore 缓存恢复:', {
                        question: state.question,
                        requirement: state.requirement,
                        title: state.title
                    });

                    // 🔥 如果存在有效的研究主题，确保它被正确保存
                    const hasValidTopic = state.question?.trim() ||
                        state.requirement?.trim() ||
                        state.title?.trim();

                    if (hasValidTopic) {
                        // 🔥 优化：使用 Promise.resolve 替代 setTimeout，更可靠的异步处理
                        Promise.resolve().then(() => {
                            try {
                                const currentState = useTaskStore.getState();

                                // 🔥 检查是否需要恢复数据
                                const needsRestore = !(currentState.question?.trim() ||
                                    currentState.requirement?.trim() ||
                                    currentState.title?.trim());

                                if (needsRestore) {
                                    // 如果当前状态中研究主题丢失，从恢复的状态重新设置
                                    if (state.question?.trim()) {
                                        currentState.setQuestion(state.question);
                                        console.log('✅ 恢复 question:', state.question);
                                    }
                                    if (state.requirement?.trim()) {
                                        currentState.setRequirement(state.requirement);
                                        console.log('✅ 恢复 requirement:', state.requirement);
                                    }
                                    if (state.title?.trim()) {
                                        currentState.setTitle(state.title);
                                        console.log('✅ 恢复 title:', state.title);
                                    }
                                } else {
                                    console.log('✅ 研究主题数据已存在，无需恢复');
                                }
                            } catch (error) {
                                console.error('❌ 研究主题恢复失败:', error);
                            }
                        });
                    }
                }
            },
        }
    )
);
