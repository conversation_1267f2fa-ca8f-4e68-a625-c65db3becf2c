events {
    worker_connections 1024;
}

http {
    # 包含基本的 MIME 类型
    include       mime.types;
    default_type  application/octet-stream;

    # 添加 .mjs 文件类型到 MIME 类型
    map $sent_http_content_type $content_type_override {
        default $sent_http_content_type;
        "~application/octet-stream" "application/javascript; charset=utf-8";
    }

    server {
        listen 80;
        server_name localhost;
        
        # 设置根目录
        root D:/nginx-1.21.6/html/deepresearch;
        index index.html;

        # 专门处理 .mjs 文件 - 覆盖默认 MIME 类型
        location ~ \.mjs$ {
            add_header Content-Type "application/javascript; charset=utf-8" always;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 特别处理 PDF worker 文件
        location = /scripts/pdf.worker.min.mjs {
            add_header Content-Type "application/javascript; charset=utf-8";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 处理静态文件
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # SPA 路由处理
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 安全头部
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
} 