{"setting": {"apiKey": "API Key", "apiProxy": "API Proxy", "thinkingModel": "Thinking Model", "networkingModel": "Networking Model", "openRouterApiKey": "OpenRouter API Key", "openRouterApiProxy": "OpenRouter API Proxy", "openRouterThinkingModel": "OpenRouter Thinking Model", "openRouterNetworkingModel": "OpenRouter Networking Model", "openAIApiKey": "OpenAI API Key", "openAIApiProxy": "OpenAI API Proxy", "openAIThinkingModel": "OpenAI Thinking Model", "openAINetworkingModel": "OpenAI Networking Model", "anthropicApiKey": "Anthropic API Key", "anthropicApiProxy": "Anthropic API Proxy", "anthropicThinkingModel": "Anthropic Thinking Model", "anthropicNetworkingModel": "Anthropic Networking Model", "deepseekApiKey": "DeepSeek API Key", "deepseekApiProxy": "DeepSeek API Proxy", "deepseekThinkingModel": "DeepSeek Thinking Model", "deepseekNetworkingModel": "DeepSeek Networking Model", "xAIApiKey": "xAI API Key", "xAIApiProxy": "xAI API Proxy", "xAIThinkingModel": "xAI Thinking Model", "xAINetworkingModel": "xAI Networking Model", "mistralApiKey": "Mistral API Key", "mistralApiProxy": "Mistral API Proxy", "mistralThinkingModel": "Mistral Thinking Model", "mistralNetworkingModel": "Mistral Networking Model", "azureApiKey": "Azure API Key", "azureResourceName": "Azure Resource Name", "azureApiVersion": "Azure API Version", "azureThinkingModel": "Azure Thinking Model", "azureNetworkingModel": "Azure Networking Model", "openAICompatibleApiKey": "Compatible API Key", "openAICompatibleApiProxy": "Compatible API Proxy", "openAICompatibleThinkingModel": "Compatible Thinking Model", "openAICompatibleNetworkingModel": "Compatible Networking Model", "pollinationsApiProxy": "Pollinations API Proxy", "pollinationsThinkingModel": "Pollinations Thinking Model", "pollinationsNetworkingModel": "Pollinations Networking Model", "ollamaApiProxy": "Ollama API Proxy", "ollamaThinkingModel": "Ollama Thinking Model", "ollamaNetworkingModel": "Ollama Networking Model", "accessPassword": "Access Password", "selectProvider": "Select Provider", "selectSearchProvider": "Select Search Provider", "searchProvider": "Search Provider", "searchMaxResult": "Max Search Results", "searchMaxResultTip": "Maximum number of results returned by each search engine, recommended to set to 5", "parallelSearch": "Parallel Search Count", "parallelSearchTip": "Number of search engines running simultaneously, recommended to set to 1 to save API costs", "tavilyApiKey": "Tavily API Key", "tavilyApiProxy": "Tavily API Proxy", "tavilyScope": "<PERSON><PERSON>", "firecrawlApiKey": "Firecrawl API Key", "firecrawlApiProxy": "Firecrawl API Proxy", "exaApiKey": "Exa API Key", "exaApiProxy": "Exa API Proxy", "exaScope": "Exa Search Scope", "bochaApiKey": "Bocha API Key", "bochaApiProxy": "Bocha API Proxy", "searxngApiProxy": "SearXNG API Proxy", "searxngScope": "SearXNG Search Scope", "general": "General", "academic": "Academic", "business": "Business", "research": "Research", "finance": "Finance", "news": "News", "health": "Health", "government": "Government", "researchPaper": "Research Paper", "all": "All", "crawler": "Crawler", "selectCrawler": "Select Crawler", "jinaReader": "<PERSON><PERSON>", "rScraper": "<PERSON>", "firecrawl": "Firecrawl", "enable": "Enable", "disable": "Disable", "enableSearch": "Enable Search", "enableSearchTip": "Enable search to get the latest information, which helps reduce hallucinations", "userAgent": "User Agent", "userAgentTip": "User agent string used to simulate browser access", "language": "Language", "experimental": "Experimental Features", "theme": "Theme", "systemTheme": "System", "lightTheme": "Light", "darkTheme": "Dark", "debug": "Debug Mode", "debugTip": "Enable debug mode to view detailed logs in the console", "selectTheme": "Select Theme", "save": "Save", "reset": "Reset", "references": "References", "referencesTip": "Enable references to show information sources in reports", "citationImage": "Citation Images", "citationImageTip": "Enable citation images to show referenced images in reports", "enableChapterValidation": "Chapter Validation", "enableChapterValidationTip": "Enable chapter validation to check content accuracy after each chapter is written, reducing AI hallucinations but increasing generation time by 50%-80%. Focus on citation accuracy, fact verification, and content authenticity.", "enableFullTextValidation": "Full Text Validation", "enableFullTextValidationTip": "Enable full text validation to check logical consistency after the entire article is completed, reducing cross-chapter contradictions but increasing total time by 30%-50%. Focus on inter-chapter logic, data conflicts, and overall coherence.", "enableDualModelValidation": "Dual Model Validation", "enableDualModelValidationTip": "Enable dual model validation to use a specialized validation model to check content and provide modification suggestions, then have the writing model apply the suggestions. Provides more professional validation but significantly increases generation time.", "validationModel": "Validation Model", "validationModelTip": "A specialized model for content validation and providing modification suggestions. Recommend choosing models that excel in analysis and critical thinking.", "validationNetworkingEnabled": "Validation Model Networking", "validationNetworkingEnabledTip": "When enabled, the validation model can use networking search functionality to verify factual accuracy, find latest information, and validate data. This helps improve validation quality but increases processing time and API costs.", "validationProvider": "Validation Model Provider", "validationApiKey": "Validation Model API Key", "validationApiProxy": "Validation Model API Proxy"}}