export interface AIProviderOptions {
  provider: string;
  baseURL?: string;
  apiKey?: string;
  headers?: Record<string, string>;
  model: string;
  settings?: any;
  useSearchGrounding?: boolean;
}

export async function createAIProvider({
  provider,
  apiKey,
  baseURL,
  headers,
  model,
  settings,
  useSearchGrounding = false,
}: AIProviderOptions) {
  if (provider === "google") {
    const { createGoogleGenerativeAI } = await import("@ai-sdk/google");
    const google = createGoogleGenerativeAI({
      baseURL,
      apiKey,
    });
    
    let modelSettings = settings || {};
    
    if (useSearchGrounding) {
      if (model.startsWith('gemini-2.0')) {
        // 为模型启用搜索grounding (工具模式)
      } else {
        modelSettings = {
          ...modelSettings,
          useSearchGrounding: true,
          dynamicRetrievalConfig: {
            mode: "MODE_DYNAMIC",
            dynamicThreshold: 0.7
          }
        };
                  // 为模型启用搜索grounding (传统模式)
      }
    }
    
    return google(model, modelSettings);
  } else if (provider === "openai") {
    const { createOpenAI } = await import("@ai-sdk/openai");
    const openai = createOpenAI({
      baseURL,
      apiKey,
    });
    return model.startsWith("gpt-4o")
      ? openai.responses(model)
      : openai(model, settings);
  } else if (provider === "anthropic") {
    const { createAnthropic } = await import("@ai-sdk/anthropic");
    const anthropic = createAnthropic({
      baseURL,
      apiKey,
      headers,
    });
    return anthropic(model, settings);
  } else if (provider === "deepseek") {
    const { createDeepSeek } = await import("@ai-sdk/deepseek");
    const deepseek = createDeepSeek({
      baseURL,
      apiKey,
    });
    return deepseek(model, settings);
  } else if (provider === "xai") {
    const { createXai } = await import("@ai-sdk/xai");
    const xai = createXai({
      baseURL,
      apiKey,
    });
    return xai(model, settings);
  } else if (provider === "mistral") {
    const { createMistral } = await import("@ai-sdk/mistral");
    const mistral = createMistral({
      baseURL,
      apiKey,
    });
    return mistral(model, settings);
  } else if (provider === "azure") {
    const { createAzure } = await import("@ai-sdk/azure");
    const azure = createAzure({
      baseURL,
      apiKey,
    });
    return azure(model, settings);
  } else if (provider === "openrouter") {
    const { createOpenRouter } = await import("@openrouter/ai-sdk-provider");
    const openrouter = createOpenRouter({
      baseURL,
      apiKey,
    });
    return openrouter(model, settings);
  } else if (provider === "openaicompatible") {
    const { createOpenAI } = await import("@ai-sdk/openai");
    const openaicompatible = createOpenAI({
      baseURL,
      apiKey,
      compatibility: "compatible",
    });
    return openaicompatible(model, settings);
  } else if (provider === "pollinations") {
    const { createOpenAI } = await import("@ai-sdk/openai");
    const pollinations = createOpenAI({
      baseURL,
      apiKey: apiKey ?? "",
      compatibility: "compatible",
      fetch: async (input, init) => {
        const headers = (init?.headers || {}) as Record<string, string>;
        if (!baseURL?.startsWith("/api/ai/pollinations"))
          delete headers["Authorization"];
        return await fetch(input, {
          ...init,
          headers,
          credentials: "omit",
        });
      },
    });
    return pollinations(model, settings);
  } else if (provider === "ollama") {
    const { createOllama } = await import("ollama-ai-provider");
    const ollama = createOllama({
      baseURL,
      headers,
      fetch: async (input, init) => {
        const headers = (init?.headers || {}) as Record<string, string>;
        if (!baseURL?.startsWith("/api/ai/ollama"))
          delete headers["Authorization"];
        return await fetch(input, {
          ...init,
          headers,
          credentials: "omit",
        });
      },
    });
    return ollama(model, settings);
  } else {
    throw new Error("Unsupported Provider: " + provider);
  }
}
