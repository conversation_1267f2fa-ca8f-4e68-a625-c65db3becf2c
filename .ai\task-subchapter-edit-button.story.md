# Context
Filename: task-subchapter-edit-button.story.md
Created on: 2025-01-27
Created by: User
Yolo mode: false

# Task Description
需要在最终生成报告时给子章节添加编辑按钮实现子章节重新生成替换功能。重新生成子章节时需要结合之前搜集的资料以及章节提示词。

具体需求：
1. 在最终报告的子章节标题上添加编辑按钮
2. 点击编辑按钮后打开章节编辑对话框
3. 重新生成时结合之前的搜索资料和章节提示词
4. 支持子章节内容的重新生成和替换

# Project Overview
这是一个AI驱动的企业管理报告自动化平台，使用Next.js 15、React、Zustand状态管理、Tailwind CSS等技术栈。项目已经具备章节编辑的基础功能，但子章节编辑按钮功能尚未完全实现。

⚠️ Warning: Do Not Modify This Section ⚠️
RIPER-5协议核心规则摘要：
- [MODE: RESEARCH] - 仅信息收集和理解，禁止建议或实现
- [MODE: INNOVATE] - 仅讨论可能的解决方案，禁止具体规划
- [MODE: PLAN] - 创建详细技术规范，禁止实现
- [MODE: EXECUTE] - 严格按计划实现，禁止偏离
- [MODE: REVIEW] - 验证实现与计划的一致性
⚠️ Warning: Do Not Modify This Section ⚠️

# Analysis
通过代码分析发现：

## 现有功能组件
1. **ChapterEditDialog.tsx** - 章节编辑对话框组件，已实现但需要集成
2. **useChapterEdit.ts** - 章节编辑相关的hook，具备编辑和替换功能
3. **ChapterRegenerator.ts** - 章节重新生成器，支持子章节重新生成
4. **MagicDown/View.tsx** - Markdown渲染组件，已支持章节编辑按钮显示
5. **FinalReport/index.tsx** - 最终报告组件，`handleChapterEdit`函数为空实现

## 技术架构分析
- 使用React + TypeScript
- 状态管理：Zustand (task store, outline store)
- UI组件：shadcn/ui
- Markdown渲染：自定义MagicDown组件
- 章节映射：基于outline的chapterMap

## 关键发现
- `MagicDown/View.tsx`已经具备显示编辑按钮的能力
- `enableChapterEdit={true}`已在FinalReport中设置
- 缺少的是`handleChapterEdit`的具体实现
- 需要将ChapterEditDialog集成到FinalReport组件中

# Proposed Solution
基于现有代码架构，实现子章节编辑按钮功能的最佳方案：

## 技术方案
1. **完善FinalReport组件**
   - 实现`handleChapterEdit`函数
   - 添加ChapterEditDialog组件的状态管理
   - 集成章节编辑对话框

2. **利用现有基础设施**
   - 复用现有的ChapterEditDialog组件
   - 利用useChapterEdit hook的编辑功能
   - 使用ChapterRegenerator进行子章节重新生成

3. **状态管理集成**
   - 通过taskStore管理编辑状态
   - 使用chapterMap获取章节信息
   - 集成搜索资料和章节提示词

## 实现策略
- 最小化代码修改，复用现有组件和功能
- 确保编辑按钮仅在子章节显示
- 保持现有的章节编辑工作流

# Current Execution Step: "1. 分析现有代码结构"

# Task Progress
[2025-01-27 10:30]
- Modified: 分析现有代码结构
- Change: 通过codebase_search和read_file分析了相关组件
- Reason: 了解现有功能和架构，确定实现方案
- Blockers: 无
- Status: Success

# Final Review
待完成后填写总结 