import { useState } from "react";
import { streamText, type <PERSON><PERSON><PERSON>V<PERSON><PERSON>, type Tool, generateText } from "ai";
import { parsePartialJson } from "@ai-sdk/ui-utils";
import { openai } from "@ai-sdk/openai";
import { type GoogleGenerativeAIProviderMetadata } from "@ai-sdk/google";
import { useTranslation } from "react-i18next";
import Plimit from "p-limit";
import { toast } from "sonner";
import useModelProvider from "@/hooks/useAiProvider";
import useWebSearch from "@/hooks/useWebSearch";
import { useTaskStore } from "@/store/task";
import { useHistoryStore } from "@/store/history";
import { useSettingStore } from "@/store/setting";
import { useKnowledgeStore } from "@/store/knowledge";
import { outputGuidelinesPrompt } from "@/constants/prompts";
import {
  getSystemPrompt,
  generateQuestionsPrompt,
  writeReportPlanPrompt,
  generateSerpQueriesPrompt,
  processResultPrompt,
  processSearchResultPrompt,
  processSearchKnowledgeResultPrompt,
  reviewSerpQueriesPrompt,
  writeFinalReportPrompt,
  writeReportTitlePrompt,
  getSERPQuerySchema,
} from "@/utils/deep-research/prompts";
import { getEffectiveWritingConfig, getConfiguredDelays } from "@/utils/writing-config-helper";
import { useOutlineStore } from "@/store/outline";
import {
  getSubChapterCount,
  getSubChapterTitle,
  generateSubChapterPrompt,
  getChapterMainTitle,
} from "@/constants/prompts";
import { isNetworkingModel } from "@/utils/model";
import { ThinkTagStreamProcessor, removeJsonMarkdown } from "@/utils/text";
import { parseError } from "@/utils/error";
import { unique, flat, pick } from "radash";
import { fixFormat, removeThinkTags } from '../utils/formatFixer';
import { validateContentWithAI, isChapterValidationEnabled, isFullTextValidationEnabled } from "../utils/aiValidator";
import { validateContentWithDualModel, isDualModelValidationEnabled, isValidationNetworkingEnabled } from "../utils/dualModelValidator";

type ProviderOptions = Record<string, Record<string, JSONValue>>;
type Tools = Record<string, Tool>;

function getResponseLanguagePrompt() {
  return `**全部使用简体中文返回**`;
}

function handleError(error: unknown) {
  const errorMessage = parseError(error);
  toast.error(errorMessage);
}

class MemoryOptimizer {
  private gcTimer: NodeJS.Timeout | null = null;

  scheduleCleanup() {
    if (this.gcTimer) clearTimeout(this.gcTimer);

    this.gcTimer = setTimeout(() => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          this.performCleanup();
        });
      } else {
        setTimeout(() => this.performCleanup(), 0);
      }
    }, 1000);
  }

  private performCleanup() {
    if (typeof window !== 'undefined' && 'gc' in window && process.env.NODE_ENV === 'development') {
      (window as any).gc();
    }
  }

  destroy() {
    if (this.gcTimer) {
      clearTimeout(this.gcTimer);
      this.gcTimer = null;
    }
  }
}

class SimplifiedReportBuilder {
  private content = '';
  private updateCallback: (content: string) => void;
  private updateTimer: NodeJS.Timeout | null = null;
  private pendingChapter = '';

  constructor(onUpdate: (content: string) => void) {
    this.updateCallback = onUpdate;
  }

  append(text: string) {
    this.content += text;
    this.scheduleUpdate();
  }

  // 新增：实时更新当前章节内容（用于流式显示）
  updateCurrentChapter(chapterContent: string) {
    this.pendingChapter = chapterContent;
    this.scheduleRealTimeUpdate();
  }

  // 新增：立即更新当前章节
  private scheduleRealTimeUpdate() {
    if (this.updateTimer) clearTimeout(this.updateTimer);
    this.updateTimer = setTimeout(() => {
      const fullContent = this.content + this.pendingChapter;
      this.updateCallback(fullContent);
    }, 50); // 更短的延迟，提供更好的实时体验
  }

  private scheduleUpdate() {
    if (this.updateTimer) clearTimeout(this.updateTimer);
    this.updateTimer = setTimeout(() => {
      this.updateCallback(this.content);
      this.pendingChapter = ''; // 清空待处理章节
    }, 100);
  }

  toString() {
    return this.content;
  }

  destroy() {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
  }
}

// 处理正文中的本地资源引用，将 [L-X] 转换为可点击链接
async function processLocalResourceCitations(content: string, resources: any[]) {
  if (!resources || resources.length === 0) {
    return content;
  }

  // 🔥 移除预检查机制，让 processLocalResourceCitations 专门负责所有本地资源引用处理
  // 现在 fixCitationFormat 不再处理 L- 格式，避免了重复处理问题

  try {
    // 获取配置信息
    const configResponse = await fetch('/api/config');
    let locationKnowledgePath = '';

    if (configResponse.ok) {
      const config = await configResponse.json();
      locationKnowledgePath = config.locationKnowledgePath || '';
    }

    if (!locationKnowledgePath) {
      return content; // 如果没有配置路径，返回原内容
    }

    // 确保基础URL以斜杠结尾
    const normalizedBaseUrl = locationKnowledgePath.endsWith('/')
        ? locationKnowledgePath
        : locationKnowledgePath + '/';

    // 从拆分的文件名中提取原始文件名的函数
    function getOriginalFileName(fileName: string): string {
      // 处理拆分文件名格式：原始名称-数字.扩展名
      const match = fileName.match(/^(.+)-\d+\.(.+)$/);
      if (match) {
        return `${match[1]}.${match[2]}`; // 返回原始文件名
      }
      return fileName; // 如果不是拆分文件格式，返回原名
    }

    // 首先处理多引用格式 [L-1, L-2, L-9, L-14, L-16]
    let processedContent = content.replace(/\[L-(\d+(?:,\s*L-\d+)*)\]/g, (match, citationsStr) => {
      // 提取所有的引用编号
      const citations = citationsStr.split(/,\s*/).map((part: string) => part.replace(/L-/, ''));

      // 转换每个引用为链接格式
      const processedCitations = citations.map((indexStr: string) => {
        const index = parseInt(indexStr) - 1; // 转换为0基索引

        if (index >= 0 && index < resources.length) {
          const resource = resources[index];
          const originalFileName = getOriginalFileName(resource.title);
          const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
          // 🔥 保持L-索引格式显示，链接指向原文件
          return `[[L-${indexStr}]](${resourceUrl})`;
        }

        return `[L-${indexStr}]`; // 如果索引无效，保持原格式
      });

      // 返回处理后的引用，用逗号分隔
      return processedCitations.join(', ');
    });

    // 再处理单方括号格式 [L-数字]（避免被双方括号处理结果影响，也避免处理已有链接）
    processedContent = processedContent.replace(/(?<!\[)\[L-(\d+)\](?!\]|\()/g, (match, indexStr) => {
      const index = parseInt(indexStr) - 1; // 转换为0基索引

      if (index >= 0 && index < resources.length) {
        const resource = resources[index];
        const originalFileName = getOriginalFileName(resource.title);
        const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
        // 处理单方括号引用
        // 🔥 保持L-索引格式显示，链接指向原文件
        return `[[L-${indexStr}]](${resourceUrl})`;
      }

      // 🔍 调试信息：索引无效的情况
      console.warn(`⚠️ 单方括号本地资源引用索引无效: [L-${indexStr}], 索引=${index}, 资源总数=${resources.length}`);
      return match; // 如果索引无效，保持原样
    });

    // 再处理双方括号格式 [[L-数字]]
    processedContent = processedContent.replace(/\[\[L-(\d+)\]\](?!\()/g, (match, indexStr) => {
      const index = parseInt(indexStr) - 1; // 转换为0基索引

      if (index >= 0 && index < resources.length) {
        const resource = resources[index];
        const originalFileName = getOriginalFileName(resource.title);
        const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
        // 🔥 保持L-索引格式显示，链接指向原文件
        return `[[L-${indexStr}]](${resourceUrl})`;
      }

      // 🔍 调试信息：索引无效的情况
      console.warn(`⚠️ 本地资源引用索引无效: L-${indexStr}, 索引=${index}, 资源总数=${resources.length}`);
      return match; // 如果索引无效，保持原样
    });

    return processedContent;
  } catch (error) {
    console.warn('处理本地资源引用失败:', error);
    return content; // 如果处理失败，返回原内容
  }
}

function useDeepResearch() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const { createModelProvider, getModel } = useModelProvider();
  const { search } = useWebSearch();
  const [status, setStatus] = useState<string>("");

  async function askQuestions() {
    const { question } = useTaskStore.getState();
    
    if (!question || question.trim() === '') {
      throw new Error('研究主题不能为空，请先填写研究主题');
    }
    
    const { thinkingModel } = getModel();
    setStatus(t("research.common.thinking"));
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: getSystemPrompt(),
      prompt: [
        generateQuestionsPrompt(question),
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      onError: handleError,
    });
    let content = "";
    let reasoning = "";
    taskStore.setQuestion(question);
    for await (const part of result.fullStream) {
      if (part.type === "text-delta") {
        thinkTagStreamProcessor.processChunk(
            part.textDelta,
            (data) => {
              content += data;
              taskStore.updateQuestions(content);
            },
            (data) => {
              reasoning += data;
            }
        );
      } else if (part.type === "reasoning") {
        reasoning += part.textDelta;
      }
    }
  }

  async function writeReportPlan() {
    const { query } = useTaskStore.getState();
    const { thinkingModel } = getModel();
    setStatus(t("research.common.thinking"));
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: getSystemPrompt(),
      prompt: [writeReportPlanPrompt(query), getResponseLanguagePrompt()].join(
          "\n\n"
      ),
      onError: handleError,
    });
    let content = "";
    let reasoning = "";
    for await (const part of result.fullStream) {
      if (part.type === "text-delta") {
        thinkTagStreamProcessor.processChunk(
            part.textDelta,
            (data) => {
              content += data;
              taskStore.updateReportPlan(content);
            },
            (data) => {
              reasoning += data;
            }
        );
      } else if (part.type === "reasoning") {
        reasoning += part.textDelta;
      }
    }
    return content;
  }

  async function searchLocalKnowledges(query: string, researchGoal: string) {
    const { resources } = useTaskStore.getState();
    const knowledgeStore = useKnowledgeStore.getState();
    const knowledges: Knowledge[] = [];
    const addedKnowledgeIds = new Set<string>(); // 🔥 新增：用于去重的ID集合

    // 🔥 修复：正确处理拆分文件的查找逻辑，并去重
    for (const item of resources) {
      if (item.status === "completed") {
        let resource = knowledgeStore.get(item.id);

        // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
        if (!resource) {
          let originalId = item.id;

          // 方式1：ID包含下划线，提取原始ID
          if (item.id.includes('_')) {
            originalId = item.id.split('_')[0];
            resource = knowledgeStore.get(originalId);
            if (resource) {
              console.log(`🔍 拆分文件查找成功(方式1): ${item.name} -> 原始文件: ${resource.title}`);
            }
          }

          // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
          if (!resource && item.name.match(/-\d+\./)) {
            const originalFileName = item.name.replace(/-\d+\./, '.');
            // 在knowledgeStore中查找匹配的原始文件
            const allKnowledges = knowledgeStore.knowledges;
            resource = allKnowledges.find((k: Knowledge) => k.title === originalFileName) || null;
            if (resource) {
              console.log(`🔍 拆分文件查找成功(方式2): ${item.name} -> 原始文件: ${resource.title}`);
            }
          }
        }

        // 🔥 新增：去重逻辑，避免同一个原始文件被多次添加
        if (resource && !addedKnowledgeIds.has(resource.id)) {
          knowledges.push(resource);
          addedKnowledgeIds.add(resource.id);
        } else if (!resource) {
          console.warn(`⚠️ 无法找到资源内容: ${item.name} (ID: ${item.id})`);
        } else {
          console.log(`🔄 跳过重复的知识资源: ${resource.title} (来自拆分文件: ${item.name})`);
        }
      }
    }

    console.log(`📚 本地知识库搜索: 找到 ${knowledges.length} 个有效资源（已去重）`);

    const { networkingModel } = getModel();
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    const searchResult = streamText({
      model: await createModelProvider(networkingModel),
      system: getSystemPrompt(),
      prompt: [
        processSearchKnowledgeResultPrompt(query, researchGoal, knowledges),
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      onError: handleError,
    });
    let content = "";
    let reasoning = "";
    for await (const part of searchResult.fullStream) {
      if (part.type === "text-delta") {
        thinkTagStreamProcessor.processChunk(
            part.textDelta,
            (data) => {
              content += data;
              taskStore.updateTask(query, { learning: content });
            },
            (data) => {
              reasoning += data;
            }
        );
      } else if (part.type === "reasoning") {
        reasoning += part.textDelta;
      }
    }
    // 返回内容和实际参与分析的知识资源数组，确保引用索引一致
    return {
      content,
      knowledges
    };
  }

  async function runSearchTask(queries: SearchTask[]) {
    const {
      provider,
      enableSearch,
      searchProvider,
      parallelSearch,
      searchMaxResult,
      references,
    } = useSettingStore.getState();
    const { resources } = useTaskStore.getState();
    const { networkingModel } = getModel();
    setStatus(t("research.common.research"));
    const plimit = Plimit(parallelSearch);
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    const createModel = (model: string) => {
      // Enable Gemini's built-in search tool
      if (
          enableSearch === "1" &&
          searchProvider === "model" &&
          provider === "google" &&
          isNetworkingModel(model)
      ) {
        return createModelProvider(model);
      } else {
        return createModelProvider(model);
      }
    };
    const getTools = (model: string) => {
      // Enable OpenAI's built-in search tool
      if (enableSearch === "1" && searchProvider === "model") {
        if (
            ["openai", "azure"].includes(provider) &&
            model.startsWith("gpt-4o")
        ) {
          return {
            web_search_preview: openai.tools.webSearchPreview({
              // optional configuration:
              searchContextSize: "medium",
            }),
          } as Tools;
        }
      }
      return undefined;
    };
    const getProviderOptions = (model: string) => {
      if (enableSearch === "1" && searchProvider === "model") {
        // Enable OpenRouter's built-in search tool
        if (provider === "openrouter") {
          return {
            openrouter: {
              plugins: [
                {
                  id: "web",
                  max_results: searchMaxResult, // Defaults to 5
                },
              ],
            },
          } as ProviderOptions;
        } else if (
            provider === "xai" &&
            model.startsWith("grok-3") &&
            !model.includes("mini")
        ) {
          return {
            xai: {
              search_parameters: {
                mode: "auto",
                max_search_results: searchMaxResult,
              },
            },
          } as ProviderOptions;
        }
      }
      return undefined;
    };
    await Promise.all(
        queries.map((item) => {
          return plimit(async () => {
            let content = "";
            let reasoning = "";
            let searchResult;
            let sources: Source[] = [];
            let images: ImageSource[] = [];
            taskStore.updateTask(item.query, { state: "processing" });
            if (resources.length > 0) {
              const knowledgeResult = await searchLocalKnowledges(
                  item.query,
                  item.researchGoal
              );
              // 🔥 修复：使用实际参与分析的knowledges数组生成引用列表，确保索引一致
              content += [
                knowledgeResult.content,
                `### ${t("research.searchResult.references")}`,
                knowledgeResult.knowledges.map((knowledge, idx) =>
                    `[L-${idx + 1}]: ${knowledge.title}`
                ).join("\n"),
                "---",
                "",
              ].join("\n\n");
            }
            if (enableSearch === "1") {
              if (searchProvider !== "model") {
                try {
                  const results = await search(item.query);
                  sources = results.sources;
                  images = results.images;

                  if (sources.length === 0) {
                    throw new Error("Invalid Search Results");
                  }
                } catch (err) {
                  console.error(err);
                  handleError(
                      `[${searchProvider}]: ${
                          err instanceof Error ? err.message : "Search Failed"
                      }`
                  );
                  return plimit.clearQueue();
                }
                const enableReferences =
                    sources.length > 0 && references === "enable";
                searchResult = streamText({
                  model: await createModel(networkingModel),
                  system: getSystemPrompt(),
                  prompt: [
                    processSearchResultPrompt(
                        item.query,
                        item.researchGoal,
                        sources,
                        enableReferences
                    ),
                    getResponseLanguagePrompt(),
                  ].join("\n\n"),
                  onError: handleError,
                });
              } else {
                searchResult = streamText({
                  model: await createModel(networkingModel),
                  system: getSystemPrompt(),
                  prompt: [
                    processResultPrompt(item.query, item.researchGoal, true), // enableSearch = true
                    getResponseLanguagePrompt(),
                  ].join("\n\n"),
                  tools: getTools(networkingModel),
                  providerOptions: getProviderOptions(networkingModel),
                  onError: handleError,
                });
              }
            } else {
              // 🔥 修复：关闭联网时完全不提供搜索工具，确保无网络请求
              searchResult = streamText({
                model: await createModelProvider(networkingModel),
                system: getSystemPrompt(),
                prompt: [
                  processResultPrompt(item.query, item.researchGoal, false), // enableSearch = false
                  getResponseLanguagePrompt(),
                ].join("\n\n"),
                // 🔥 关键：不提供任何工具，确保AI无法发起网络请求
                tools: undefined,
                providerOptions: undefined,
                onError: (err) => {
                  taskStore.updateTask(item.query, { state: "failed" });
                  handleError(err);
                },
              });
            }
            for await (const part of searchResult.fullStream) {
              if (part.type === "text-delta") {
                thinkTagStreamProcessor.processChunk(
                    part.textDelta,
                    (data) => {
                      content += data;
                      taskStore.updateTask(item.query, { learning: content });
                    },
                    (data) => {
                      reasoning += data;
                    }
                );
              } else if (part.type === "reasoning") {
                reasoning += part.textDelta;
              } else if (part.type === "source") {
                // 🔥 修复：只有在明确启用联网搜索时才处理来源
                if (enableSearch === "1") {
                  sources.push(part.source);
                }
              } else if (part.type === "finish") {
                if (part.providerMetadata?.google) {
                  const { groundingMetadata } = part.providerMetadata.google;
                  const googleGroundingMetadata =
                      groundingMetadata as GoogleGenerativeAIProviderMetadata["groundingMetadata"];
                  if (googleGroundingMetadata?.groundingSupports) {
                    googleGroundingMetadata.groundingSupports.forEach(
                        ({ segment, groundingChunkIndices }) => {
                          if (segment.text && groundingChunkIndices) {
                            const index = groundingChunkIndices.map(
                                (idx: number) => `[${idx + 1}]`
                            );
                            content = content.replaceAll(
                                segment.text,
                                `${segment.text}${index.join("")}`
                            );
                          }
                        }
                    );
                  }
                } else if (part.providerMetadata?.openai) {
                  // Fixed the problem that OpenAI cannot generate markdown reference link syntax properly in Chinese context
                  content = content.replaceAll("【", "[").replaceAll("】", "]");
                }
              }
            }
            if (sources.length > 0) {
              content +=
                  "\n\n" +
                  sources
                      .map(
                          (item, idx) =>
                              `[${idx + 1}]: ${item.url}${
                                  item.title ? ` "${item.title.replaceAll('"', " ")}"` : ""
                              }`
                      )
                      .join("\n");
            }
            taskStore.updateTask(item.query, {
              state: "completed",
              learning: content,
              sources,
              images,
            });
            return content;
          });
        })
    );
  }

  async function reviewSearchResult() {
    const { reportPlan, tasks, suggestion, currentChapterContext } = useTaskStore.getState();
    const { thinkingModel } = getModel();
    setStatus(t("research.common.research"));
    const learnings = tasks.map((item) => item.learning);
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: getSystemPrompt(),
      prompt: [
        reviewSerpQueriesPrompt(reportPlan, learnings, suggestion),
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      onError: handleError,
    });

    const querySchema = getSERPQuerySchema();
    let content = "";
    let reasoning = "";
    let queries: SearchTask[] = [];
    const tasksBeforeThisRun = [...tasks];
    for await (const textPart of result.textStream) {
      thinkTagStreamProcessor.processChunk(
          textPart,
          (text) => {
            content += text;
            const data: PartialJson = parsePartialJson(
                removeJsonMarkdown(content)
            );
            if (
                querySchema.safeParse(data.value) &&
                (data.state === "repaired-parse" || data.state === "successful-parse")
            ) {
              if (data.value) {
                queries = data.value.map(
                    (item: { query: string; researchGoal: string }) => {
                      // 🔥 关键修复：如果存在章节上下文，为查询添加章节标识
                      let processedQuery = item.query;
                      if (currentChapterContext) {
                        const chapterTag = `[第${currentChapterContext.chapterIndex + 1}章: ${currentChapterContext.chapterTitle}]`;
                        processedQuery = `${chapterTag} ${item.query}`;
                      }

                      return {
                        state: "unprocessed",
                        learning: "",
                        query: processedQuery,
                        researchGoal: item.researchGoal,
                        chapterIndex: currentChapterContext?.chapterIndex,
                        chapterTitle: currentChapterContext?.chapterTitle,
                      };
                    }
                );
              }
            }
          },
          (text) => {
            reasoning += text;
          }
      );
    }
    if (queries.length > 0) {
      // 🔥 简化：直接更新任务，合并逻辑已在store中处理
      taskStore.update(queries);
      await runSearchTask(queries);
    }
  }

  async function writeFinalReport(customRequirement?: string) {
    const { citationImage, references } = useSettingStore.getState();
    const {
      reportPlan,
      tasks,
      setId,
      setTitle,
      setSources,

      question,      // 添加question变量
      updateFinalReport,
      resources,
    } = useTaskStore.getState();
    
    // 🔥 优先使用传入的写作要求，避免读取缓存
    const requirement = customRequirement || "";
    const { save } = useHistoryStore.getState();
    const { thinkingModel } = getModel();

    // 直接从store获取最新的大纲写作配置
    const currentOutline = useOutlineStore.getState().currentOutline;
    const writingConfig = currentOutline?.writingConfig || getEffectiveWritingConfig();

    console.log('📋 Final report 配置来源:', currentOutline ? '大纲配置' : '默认配置');
    console.log('📋 Final report 使用的写作配置:', writingConfig);
    console.log('🎨 当前写作风格:', writingConfig.style);
    console.log('🌍 语言要求:', writingConfig.languageRequirements);
    console.log('⚙️ 自定义指令:', writingConfig.customInstructions || '无');

    updateFinalReport("");
    setTitle("");
    setSources([]);

    const learnings = tasks.map((item) => item.learning);
    const { enableSearch } = useSettingStore.getState();
    const sources: Source[] = enableSearch === "1"
        ? unique(
            flat(tasks.map((item) => item.sources || [])),
            (item) => item.url
        )
        : [];
    const images: ImageSource[] = enableSearch === "1"
        ? unique(
            flat(tasks.map((item) => item.images || [])),
            (item) => item.url
        )
        : [];
    const enableCitationImage = images.length > 0 && citationImage === "enable";
    const enableReferences = sources.length > 0 && references === "enable";

    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();

    try {
      // 首先生成报告标题
      setStatus(t("research.common.writing") + " - 生成报告标题...");
      const titleResult = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          writeReportTitlePrompt(
              reportPlan,
              learnings,
              question,      // 使用核心研究主题
              requirement    // 使用补充要求
          ),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
      });

      // 清理标题中的think标签
      const rawTitle = titleResult.text.trim();
      const cleanedTitleResult = removeThinkTags(rawTitle);
      const reportTitle = cleanedTitleResult.content.trim();
      setTitle(reportTitle);

      // 传统方式：一次性生成完整报告
      setStatus(t("research.common.writing"));
      const result = streamText({
        model: await createModelProvider(thinkingModel),
        system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
        prompt: [
          writeFinalReportPrompt(
              reportPlan,
              learnings,
              sources.map((item) => pick(item, ["title", "url"])),
              resources,
              images,
              requirement,
              enableCitationImage,
              enableReferences,
              writingConfig
          ),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
        onError: handleError,
      });

      let content = `# ${reportTitle}\n\n`;
      const currentDate = new Date().toLocaleDateString('zh-CN');
      content += `**报告日期：** ${currentDate}\n\n`;
      content += `**目标读者：** 投资决策机构\n\n`;
      content += `---\n\n`;

      updateFinalReport(content);

      let reasoning = "";
      for await (const part of result.fullStream) {
        if (part.type === "text-delta") {
          thinkTagStreamProcessor.processChunk(
              part.textDelta,
              (data) => {
                content += data;
                updateFinalReport(content);
              },
              (data) => {
                reasoning += data;
              }
          );
        } else if (part.type === "reasoning") {
          reasoning += part.textDelta;
        }
      }
      // 🔥 新增：在调用 processLocalResourceCitations 之前，构建一个与AI分析时完全一致的有效知识资源列表
      const knowledgeStore = useKnowledgeStore.getState();
      const validKnowledges: Knowledge[] = [];
      const addedKnowledgeIds = new Set<string>();

      for (const item of resources) {
        if (item.status === "completed") {
          let resource = knowledgeStore.get(item.id);
          if (!resource) {
            if (item.id.includes('_')) {
              const originalId = item.id.split('_')[0];
              resource = knowledgeStore.get(originalId);
            }
            if (!resource && item.name.match(/-\d+\./)) {
              const originalFileName = item.name.replace(/-\d+\./, '.');
              resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
            }
          }
          if (resource && !addedKnowledgeIds.has(resource.id)) {
            validKnowledges.push(resource);
            addedKnowledgeIds.add(resource.id);
          }
        }
      }

      // 处理正文中的本地资源引用，将 [L-X] 转换为可点击链接
      const processedContent = await processLocalResourceCitations(content, validKnowledges);

      // 🔥 新增：AI内容验证（智能上下文模式）
      let validatedContent = processedContent;

      // 优先使用双模型验证，如果未启用则使用单模型验证
      if (isDualModelValidationEnabled()) {
        const networkingStatus = isValidationNetworkingEnabled() ? "（联网搜索已启用）" : "（仅本地验证）";
        setStatus(t("research.common.writing") + ` - 🔍 验证模型正在检查内容...${networkingStatus}`);

        // 准备本地知识库内容
        const knowledgeStore = useKnowledgeStore.getState();
        const localKnowledge = resources
            .filter(item => item.status === "completed")
            .map(item => {
              const resource = knowledgeStore.get(item.id);
              return resource ? `${resource.title}:\n${resource.content}` : '';
            })
            .filter(content => content.length > 0)
            .join('\n\n---\n\n');

        try {
          const dualValidationResult = await validateContentWithDualModel(processedContent, {
            mode: 'chapter',
            localKnowledge: localKnowledge,
            chapterContext: `智能上下文模式 - 完整报告验证`
          });

          if (dualValidationResult.hasChanges) {
            validatedContent = dualValidationResult.validatedContent;

            setStatus(t("research.common.writing") + " - ✏️ 写作模型正在应用建议...");
            await new Promise(resolve => setTimeout(resolve, 800));
            setStatus(t("research.common.writing") + " - ✅ 双模型验证完成，内容已优化");
            await new Promise(resolve => setTimeout(resolve, 800));
          } else {

            setStatus(t("research.common.writing") + " - ✅ 双模型验证完成，未发现问题");
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.warn('双模型验证失败，使用原内容:', error);
          setStatus(t("research.common.writing") + " - ⚠️ 双模型验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } else if (isChapterValidationEnabled()) {
        setStatus(t("research.common.writing") + " - 🔍 正在验证内容准确性，减少AI幻觉...");

        // 准备本地知识库内容
        const knowledgeStore = useKnowledgeStore.getState();
        const localKnowledge = resources
            .filter(item => item.status === "completed")
            .map(item => {
              const resource = knowledgeStore.get(item.id);
              return resource ? `${resource.title}:\n${resource.content}` : '';
            })
            .filter(content => content.length > 0)
            .join('\n\n---\n\n');

        try {
          const validationResult = await validateContentWithAI(processedContent, {
            mode: 'chapter',
            localKnowledge: localKnowledge,
            chapterContext: `智能上下文模式 - 完整报告验证`
          });

          if (validationResult.hasChanges) {
            validatedContent = validationResult.validatedContent;

            setStatus(t("research.common.writing") + " - ✅ 内容验证完成，准确性已提升");
            // 短暂显示验证完成状态
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus(t("research.common.writing") + " - ✅ 内容验证完成，未发现问题");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('内容验证失败，使用原内容:', error);
          setStatus(t("research.common.writing") + " - ⚠️ 验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 页面级格式自动纠正

      setStatus(t("research.common.writing") + " - 🔧 优化格式...");
      const formatFixResult = fixFormat(validatedContent, {
        fixTitles: true,
        fixBold: true,
        fixSpacing: false, // 保持原有段落间距，避免过度调整
        fixCitations: true,
        maxBoldPerParagraph: 2
      });

      let finalContent = formatFixResult.content;

      // 🔥 新增：全文级AI验证（智能上下文模式）
      // 优先使用双模型验证，如果未启用则使用单模型验证
      // 注意：双模型验证也需要遵循全文验证开关的设置
      if (isDualModelValidationEnabled() && isFullTextValidationEnabled()) {
        const networkingStatus = isValidationNetworkingEnabled() ? "（联网搜索已启用）" : "（仅本地验证）";
        setStatus(t("research.common.writing") + ` - 🔍 验证模型正在检查全文逻辑一致性...${networkingStatus}`);

        try {
          const dualFullTextValidationResult = await validateContentWithDualModel(finalContent, {
            mode: 'fulltext',
            chapterContext: `智能上下文模式 - 完整报告\n报告标题: ${reportTitle}`
          });

          if (dualFullTextValidationResult.hasChanges) {
            finalContent = dualFullTextValidationResult.validatedContent;

            setStatus(t("research.common.writing") + " - ✏️ 写作模型正在应用全文优化建议...");
            await new Promise(resolve => setTimeout(resolve, 1000));
            setStatus(t("research.common.writing") + " - ✅ 双模型全文验证完成，逻辑一致性已提升");
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus(t("research.common.writing") + " - ✅ 双模型全文验证完成，逻辑一致性良好");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('双模型全文验证失败，使用原内容:', error);
          setStatus(t("research.common.writing") + " - ⚠️ 双模型全文验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } else if (isFullTextValidationEnabled()) {
        setStatus(t("research.common.writing") + " - 🔍 正在进行全文验证，检查逻辑一致性...");

        try {
          const fullTextValidationResult = await validateContentWithAI(finalContent, {
            mode: 'fulltext',
            chapterContext: `智能上下文模式 - 完整报告\n报告标题: ${reportTitle}`
          });

          if (fullTextValidationResult.hasChanges) {
            finalContent = fullTextValidationResult.validatedContent;

            setStatus(t("research.common.writing") + " - ✅ 全文验证完成，逻辑一致性已提升");
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus(t("research.common.writing") + " - ✅ 全文验证完成，逻辑一致性良好");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('全文验证失败，使用原内容:', error);
          setStatus(t("research.common.writing") + " - ⚠️ 全文验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 最终完整更新
      updateFinalReport(finalContent);

      // 从报告内容中提取被引用的本地资源
      if (resources.length > 0) {
        // 🔥 修复：获取实际参与分析的知识资源列表，而不是原始resources
        const knowledgeStore = useKnowledgeStore.getState();
        const validKnowledges: Knowledge[] = [];

        for (const item of resources) {
          if (item.status === "completed") {
            const resource = knowledgeStore.get(item.id);
            if (resource) {
              validKnowledges.push(resource);
            }
          }
        }

        const citedLocalResources: Array<{index: number, knowledge: Knowledge}> = [];

        // 查找所有 [L-数字] 格式的引用
        const localCitationRegex = /\[L-(\d+)\]/g;
        let match;
        const citedIndices = new Set<number>();

        while ((match = localCitationRegex.exec(content)) !== null) {
          const index = parseInt(match[1]) - 1; // 转换为0基索引
          if (index >= 0 && index < validKnowledges.length) {
            citedIndices.add(index);
          }
        }

        // 收集被引用的知识资源
        citedIndices.forEach(index => {
          citedLocalResources.push({
            index: index + 1, // 恢复为1基索引用于显示
            knowledge: validKnowledges[index]
          });
        });

        // 只显示被引用的本地资源
        if (citedLocalResources.length > 0) {
          // 获取配置信息
          let locationKnowledgePath = '';
          try {
            const configResponse = await fetch('/api/config');
            if (configResponse.ok) {
              const config = await configResponse.json();
              locationKnowledgePath = config.locationKnowledgePath || '';
            }
          } catch (error) {
            console.warn('Failed to fetch config:', error);
          }

          const localResourcesContent =
              "\n\n---\n\n" +
              `## 已研究 ${citedLocalResources.length} 个本地资源\n\n` +
              citedLocalResources
                  .sort((a, b) => a.index - b.index) // 按索引排序
                  .map((item) => {
                    if (locationKnowledgePath) {
                      // 确保基础URL以斜杠结尾
                      const normalizedBaseUrl = locationKnowledgePath.endsWith('/')
                          ? locationKnowledgePath
                          : locationKnowledgePath + '/';

                      // 🔥 修复：使用knowledge.title而不是resource.name
                      // 从拆分的文件名中提取原始文件名用于链接
                      function getOriginalFileName(fileName: string): string {
                        // 处理拆分文件名格式：原始名称-数字.扩展名
                        const match = fileName.match(/^(.+)-\d+\.(.+)$/);
                        if (match) {
                          return `${match[1]}.${match[2]}`; // 返回原始文件名
                        }
                        return fileName; // 如果不是拆分文件格式，返回原名
                      }

                      const originalFileName = getOriginalFileName(item.knowledge.title);
                      const resourceUrl = normalizedBaseUrl + encodeURIComponent(originalFileName);
                      // 显示名称保持原始拆分文件名，链接指向原始文件
                      return `[L-${item.index}]: [${item.knowledge.title}](${resourceUrl})`;
                    } else {
                      return `[L-${item.index}]: ${item.knowledge.title}`;
                    }
                  })
                  .join("\n");
          content += localResourcesContent;
          updateFinalReport(content);
        }
      }

      if (sources.length > 0) {
        const sourceContent =
            "\n\n---\n\n" +
            "## 参考文献\n\n" +
            sources
                .map(
                    (item, idx) =>
                        `[${idx + 1}]: ${item.url}${
                            item.title ? ` "${item.title.replaceAll('"', " ")}"` : ""
                        }`
                )
                .join("\n");
        content += sourceContent;
        updateFinalReport(content);
      }

      setSources(sources);
      const id = save(taskStore.backup());
      setId(id);

      // 最终完整更新
      updateFinalReport(content);

      return content;
    } catch (error) {
      console.error("生成报告时出错:", error);
      handleError(error);
      throw error;
    }
  }

  async function writeChapterReport() {
    const {
      reportPlan,
      tasks,
      setId,
      setTitle,
      setSources,
      requirement,
      question,      // 添加question变量
      updateFinalReport,
      resources,
    } = useTaskStore.getState();
    const { save } = useHistoryStore.getState();
    const { thinkingModel } = getModel();

    // 直接从store获取最新的大纲写作配置
    const currentOutline = useOutlineStore.getState().currentOutline;
    const writingConfig = currentOutline?.writingConfig || getEffectiveWritingConfig();
    console.log('Chapter report 使用的写作配置:', writingConfig);
    const { chapterDelay, sectionDelay } = getConfiguredDelays(writingConfig);

    // 使用新的高效报告构建器
    const reportBuilder = new SimplifiedReportBuilder(updateFinalReport);
    // 创建内存优化器实例
    const memoryOptimizer = new MemoryOptimizer();

    updateFinalReport("");
    setTitle("");
    setSources([]);

    const learnings = tasks.map((item) => item.learning);
    const { enableSearch } = useSettingStore.getState();
    const sources: Source[] = enableSearch === "1"
        ? unique(
            flat(tasks.map((item) => item.sources || [])),
            (item) => item.url
        )
        : [];
    const images: ImageSource[] = enableSearch === "1"
        ? unique(
            flat(tasks.map((item) => item.images || [])),
            (item) => item.url
        )
        : [];

    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();

    try {
      // 首先生成报告标题和基础内容
      setStatus(t("research.common.writingTitle"));
      const titleResult = await generateText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          writeReportTitlePrompt(reportPlan, learnings, question, requirement),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
      });

      // 清理标题中的think标签
      const rawTitle = titleResult.text.trim();
      const cleanedTitleResult = removeThinkTags(rawTitle);
      const reportTitle = cleanedTitleResult.content.trim();
      setTitle(reportTitle);

      // 设置基础内容（一次性设置，不会频繁更新）
      const baseContent = `# ${reportTitle}\n\n` +
          `**报告日期：** ${new Date().toLocaleDateString('zh-CN')}\n\n` +
          `**目标读者：** 投资决策机构\n\n` +
          `---\n\n`;

      reportBuilder.append(baseContent);

      // 存储各章节的子章节内容，用于最后合并
      const chapterContents: { [key: number]: string[] } = {};

      // 遍历6个主要章节，生成子章节内容
      // 🔥 测试模式：只生成第三章 - 项目公司的管理团队
      for (let chapterNum = 3; chapterNum <= 3; chapterNum++) {
        const subChapterCount = getSubChapterCount(chapterNum);

        chapterContents[chapterNum] = [];

        // 在每个大章节开始时，先添加大章节标题
        const chapterMainTitle = getChapterMainTitle(chapterNum);
        const chineseNumber = ['一', '二', '三', '四', '五', '六'][chapterNum - 1];
        const chapterTitleContent = `\n\n## ${chineseNumber}、${chapterMainTitle}\n\n`;
        reportBuilder.append(chapterTitleContent);

        // 为每个主章节生成其所有子章节
        for (let sectionIndex = 0; sectionIndex < subChapterCount; sectionIndex++) {
          const subChapterTitle = getSubChapterTitle(chapterNum, sectionIndex);

          setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：${subChapterTitle}...`);

          const subChapterResult = streamText({
            model: await createModelProvider(thinkingModel),
            system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
            prompt: [
              generateSubChapterPrompt(
                  chapterNum,
                  sectionIndex,
                  reportPlan,
                  learnings,
                  sources.map((item) => pick(item, ["title", "url"])),
                  resources,
                  images,
                  requirement
              ),
              getResponseLanguagePrompt(),
            ].join("\n\n"),
            onError: handleError,
          });

          let subChapterContent = "";
          let reasoning = "";

          for await (const part of subChapterResult.fullStream) {
            if (part.type === "text-delta") {
              thinkTagStreamProcessor.processChunk(
                  part.textDelta,
                  (data) => {
                    subChapterContent += data;
                    // 使用新的实时更新方法，提供流式体验同时控制更新频率
                    reportBuilder.updateCurrentChapter(subChapterContent + "\n\n");
                  },
                  (data) => {
                    reasoning += data;
                  }
              );
            } else if (part.type === "reasoning") {
              reasoning += part.textDelta;
            }
          }

          // 对子章节内容进行即时格式处理（不处理引用格式，留到最后统一处理）
          const formattedSubChapter = fixFormat(subChapterContent, {
            fixTitles: true,
            fixBold: true,
            fixSpacing: false,
            fixCitations: false,  // 不在子章节阶段处理引用格式，提高效率
            maxBoldPerParagraph: 2
          }).content;

          // 🔥 不在子章节阶段处理本地资源引用，留到最后统一处理
          const processedSubChapter = formattedSubChapter;

          // 🔥 新增：章节级AI验证
          let validatedSubChapter = processedSubChapter;

          // 优先使用双模型验证，如果未启用则使用单模型验证
          if (isDualModelValidationEnabled()) {
            const networkingStatus = isValidationNetworkingEnabled() ? "（联网搜索已启用）" : "（仅本地验证）";
            setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：🔍 验证模型正在检查内容...${networkingStatus}`);

            // 🔥 修复：准备本地知识库内容，正确处理拆分文件并去重
            const knowledgeStore = useKnowledgeStore.getState();
            const addedKnowledgeIds = new Set<string>();
            const validKnowledges: Knowledge[] = [];

            for (const item of resources) {
              if (item.status === "completed") {
                let resource = knowledgeStore.get(item.id);

                // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
                if (!resource) {
                  // 方式1：ID包含下划线，提取原始ID
                  if (item.id.includes('_')) {
                    const originalId = item.id.split('_')[0];
                    resource = knowledgeStore.get(originalId);
                  }

                  // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
                  if (!resource && item.name.match(/-\d+\./)) {
                    const originalFileName = item.name.replace(/-\d+\./, '.');
                    resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
                  }
                }

                // 去重逻辑
                if (resource && !addedKnowledgeIds.has(resource.id)) {
                  validKnowledges.push(resource);
                  addedKnowledgeIds.add(resource.id);
                }
              }
            }

            const localKnowledge = validKnowledges
                .map(resource => `${resource.title}:\n${resource.content}`)
                .join('\n\n---\n\n');

            try {
              const dualValidationResult = await validateContentWithDualModel(processedSubChapter, {
                mode: 'chapter',
                localKnowledge: localKnowledge,
                chapterContext: `第${chapterNum}章第${sectionIndex + 1}节：${subChapterTitle}`
              });

              if (dualValidationResult.hasChanges) {
                validatedSubChapter = dualValidationResult.validatedContent;

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✏️ 写作模型正在应用建议...`);
                await new Promise(resolve => setTimeout(resolve, 800));
                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 双模型验证完成，内容已优化`);
                await new Promise(resolve => setTimeout(resolve, 800));
              } else {

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 双模型验证完成，未发现问题`);
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            } catch (error) {
              console.warn('双模型验证失败，使用原内容:', error);
              setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：⚠️ 双模型验证失败，使用原内容`);
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          } else if (isChapterValidationEnabled()) {
            setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：🔍 正在验证内容准确性...`);

            // 🔥 修复：准备本地知识库内容，正确处理拆分文件并去重
            const knowledgeStore = useKnowledgeStore.getState();
            const addedKnowledgeIds = new Set<string>();
            const validKnowledges: Knowledge[] = [];

            for (const item of resources) {
              if (item.status === "completed") {
                let resource = knowledgeStore.get(item.id);

                // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
                if (!resource) {
                  // 方式1：ID包含下划线，提取原始ID
                  if (item.id.includes('_')) {
                    const originalId = item.id.split('_')[0];
                    resource = knowledgeStore.get(originalId);
                  }

                  // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
                  if (!resource && item.name.match(/-\d+\./)) {
                    const originalFileName = item.name.replace(/-\d+\./, '.');
                    resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
                  }
                }

                // 去重逻辑
                if (resource && !addedKnowledgeIds.has(resource.id)) {
                  validKnowledges.push(resource);
                  addedKnowledgeIds.add(resource.id);
                }
              }
            }

            const localKnowledge = validKnowledges
                .map(resource => `${resource.title}:\n${resource.content}`)
                .join('\n\n---\n\n');

            try {
              const validationResult = await validateContentWithAI(processedSubChapter, {
                mode: 'chapter',
                localKnowledge: localKnowledge,
                chapterContext: `第${chapterNum}章第${sectionIndex + 1}节：${subChapterTitle}`
              });

              if (validationResult.hasChanges) {
                validatedSubChapter = validationResult.validatedContent;

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 验证完成，准确性已提升`);
                await new Promise(resolve => setTimeout(resolve, 800));
              } else {

                setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：✅ 验证完成，未发现问题`);
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            } catch (error) {
              console.warn('章节验证失败，使用原内容:', error);
              setStatus(`${t("research.common.writingChapter")} - 第${chapterNum}章第${sectionIndex + 1}节：⚠️ 验证失败，使用原内容`);
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }

          // 存储子章节内容
          chapterContents[chapterNum].push(validatedSubChapter);

          // 子章节完成后添加到构建器
          reportBuilder.append(validatedSubChapter + "\n\n");

          // 重置ThinkTagStreamProcessor状态，为下一子章节做准备
          thinkTagStreamProcessor.end();

          // 内存优化
          reasoning = "";
          subChapterContent = "";
          memoryOptimizer.scheduleCleanup();

          // 子章节间延迟（使用配置的延迟）
          if (sectionIndex < subChapterCount - 1) {
            await new Promise(resolve => setTimeout(resolve, sectionDelay));
          }
        }

        // 章节间延迟（测试模式下只有一章，无需延迟）
        // if (chapterNum < 6) {
        //   await new Promise(resolve => setTimeout(resolve, 1000));
        // }
      }

      // 添加本地资源引用
      if (resources.length > 0) {
        try {
          // 🔥 修复：使用与AI分析一致的知识资源列表，正确处理拆分文件并去重
          const knowledgeStore = useKnowledgeStore.getState();
          const validKnowledges: Knowledge[] = [];
          const addedKnowledgeIds = new Set<string>();

          for (const item of resources) {
            if (item.status === "completed") {
              let resource = knowledgeStore.get(item.id);

              // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
              if (!resource) {
                // 方式1：ID包含下划线，提取原始ID
                if (item.id.includes('_')) {
                  const originalId = item.id.split('_')[0];
                  resource = knowledgeStore.get(originalId);
                }

                // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
                if (!resource && item.name.match(/-\d+\./)) {
                  const originalFileName = item.name.replace(/-\d+\./, '.');
                  resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
                }
              }

              // 去重逻辑
              if (resource && !addedKnowledgeIds.has(resource.id)) {
                validKnowledges.push(resource);
                addedKnowledgeIds.add(resource.id);
              }
            }
          }

          // 获取配置信息
          const configResponse = await fetch('/api/config');
          const config = await configResponse.json();
          const locationKnowledgePath = config.locationKnowledgePath;

          let localResourcesContent = "---\n\n" + `## 已研究 ${validKnowledges.length} 个本地资源\n\n`;

          if (locationKnowledgePath) {
            // 如果有配置路径，生成可点击链接
            localResourcesContent += validKnowledges
                .map((knowledge, idx) => {
                  // 从拆分的文件名中提取原始文件名用于链接
                  function getOriginalFileName(fileName: string): string {
                    // 处理拆分文件名格式：原始名称-数字.扩展名
                    const match = fileName.match(/^(.+)-\d+\.(.+)$/);
                    if (match) {
                      return `${match[1]}.${match[2]}`; // 返回原始文件名
                    }
                    return fileName; // 如果不是拆分文件格式，返回原名
                  }

                  const originalFileName = getOriginalFileName(knowledge.title);
                  const link = `${locationKnowledgePath}${encodeURIComponent(originalFileName)}`;
                  // 显示名称保持原始拆分文件名，链接指向原始文件
                  return `[L-${idx + 1}]: [${knowledge.title}](${link})`;
                })
                .join("\n");
          } else {
            // 如果没有配置路径，使用原有格式
            localResourcesContent += validKnowledges
                .map((knowledge, idx) => `[L-${idx + 1}]: ${knowledge.title}`)
                .join("\n");
          }

          reportBuilder.append(localResourcesContent + "\n\n");
        } catch (error) {
          console.error('获取配置失败，使用默认格式:', error);
          // 🔥 修复：使用知识资源而不是原始resources，正确处理拆分文件并去重
          const knowledgeStore = useKnowledgeStore.getState();
          const validKnowledges: Knowledge[] = [];
          const addedKnowledgeIds = new Set<string>();

          for (const item of resources) {
            if (item.status === "completed") {
              let resource = knowledgeStore.get(item.id);

              // 如果直接查找失败，可能是拆分文件，尝试多种方式查找原始文件
              if (!resource) {
                // 方式1：ID包含下划线，提取原始ID
                if (item.id.includes('_')) {
                  const originalId = item.id.split('_')[0];
                  resource = knowledgeStore.get(originalId);
                }

                // 方式2：文件名包含"-数字"，可能是拆分文件，尝试查找原始文件名
                if (!resource && item.name.match(/-\d+\./)) {
                  const originalFileName = item.name.replace(/-\d+\./, '.');
                  resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
                }
              }

              // 去重逻辑
              if (resource && !addedKnowledgeIds.has(resource.id)) {
                validKnowledges.push(resource);
                addedKnowledgeIds.add(resource.id);
              }
            }
          }

          const localResourcesContent =
              "---\n\n" +
              `## 已研究 ${validKnowledges.length} 个本地资源\n\n` +
              validKnowledges
                  .map((knowledge, idx) => `[L-${idx + 1}]: ${knowledge.title}`)
                  .join("\n");
          reportBuilder.append(localResourcesContent + "\n\n");
        }
      }

      // 添加参考文献和来源
      if (sources.length > 0) {
        const sourceContent =
            "---\n\n" +
            "## 参考文献\n\n" +
            sources
                .map(
                    (item, idx) =>
                        `[${idx + 1}]: ${item.url}${
                            item.title ? ` "${item.title.replaceAll('"', " ")}"` : ""
                        }`
                )
                .join("\n");
        reportBuilder.append(sourceContent);
      }

      setSources(sources);
      const id = save(taskStore.backup());
      setId(id);

      // 获取当前内容（子章节已经处理过引用了）
      const currentContent = reportBuilder.toString();

      // 页面级格式自动纠正（主要处理整体结构）

      setStatus("🔧 正在优化报告格式和引用格式...");
      const formatFixResult = fixFormat(currentContent, {
        fixTitles: true,
        fixBold: false,  // 子章节已经处理过粗体了
        fixSpacing: true, // 最终统一处理段落间距
        fixCitations: true, // 🔥 改为在最后统一处理引用格式，提高效率
        maxBoldPerParagraph: 2
      });

      // 🔥 统一处理本地资源引用，为引用添加可点击链接
      const knowledgeStore = useKnowledgeStore.getState();
      const validKnowledges: Knowledge[] = [];
      const addedKnowledgeIds = new Set<string>();

      for (const item of resources) {
        if (item.status === "completed") {
          let resource = knowledgeStore.get(item.id);
          if (!resource) {
            if (item.id.includes('_')) {
              const originalId = item.id.split('_')[0];
              resource = knowledgeStore.get(originalId);
            }
            if (!resource && item.name.match(/-\d+\./)) {
              const originalFileName = item.name.replace(/-\d+\./, '.');
              resource = knowledgeStore.knowledges.find((k: Knowledge) => k.title === originalFileName) || null;
            }
          }
          if (resource && !addedKnowledgeIds.has(resource.id)) {
            validKnowledges.push(resource);
            addedKnowledgeIds.add(resource.id);
          }
        }
      }

      const finalContent = await processLocalResourceCitations(formatFixResult.content, validKnowledges);

      // 🔥 新增：全文级AI验证
      let validatedFinalContent = finalContent;

      // 优先使用双模型验证，如果未启用则使用单模型验证
      // 注意：双模型验证也需要遵循全文验证开关的设置
      if (isDualModelValidationEnabled() && isFullTextValidationEnabled()) {
        const networkingStatus = isValidationNetworkingEnabled() ? "（联网搜索已启用）" : "（仅本地验证）";
        setStatus(t("research.common.writingChapter") + ` - 🔍 验证模型正在检查全文逻辑一致性...${networkingStatus}`);

        try {
          // 准备章节上下文信息
          const chapterContext = Object.keys(chapterContents)
              .map(chapterNum => `第${chapterNum}章: ${getChapterMainTitle(parseInt(chapterNum))}`)
              .join('\n');

          const dualFullTextValidationResult = await validateContentWithDualModel(finalContent, {
            mode: 'fulltext',
            chapterContext: chapterContext
          });

          if (dualFullTextValidationResult.hasChanges) {
            validatedFinalContent = dualFullTextValidationResult.validatedContent;

            setStatus(t("research.common.writingChapter") + " - ✏️ 写作模型正在应用全文优化建议...");
            await new Promise(resolve => setTimeout(resolve, 1000));
            setStatus(t("research.common.writingChapter") + " - ✅ 双模型全文验证完成，逻辑一致性已提升");
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus(t("research.common.writingChapter") + " - ✅ 双模型全文验证完成，逻辑一致性良好");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('双模型全文验证失败，使用原内容:', error);
          setStatus(t("research.common.writingChapter") + " - ⚠️ 双模型全文验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } else if (isFullTextValidationEnabled()) {
        setStatus(t("research.common.writingChapter") + " - 🔍 正在进行全文验证，检查逻辑一致性...");

        try {
          // 准备章节上下文信息
          const chapterContext = Object.keys(chapterContents)
              .map(chapterNum => `第${chapterNum}章: ${getChapterMainTitle(parseInt(chapterNum))}`)
              .join('\n');

          const fullTextValidationResult = await validateContentWithAI(finalContent, {
            mode: 'fulltext',
            chapterContext: chapterContext
          });

          if (fullTextValidationResult.hasChanges) {
            validatedFinalContent = fullTextValidationResult.validatedContent;

            setStatus(t("research.common.writingChapter") + " - ✅ 全文验证完成，逻辑一致性已提升");
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {

            setStatus(t("research.common.writingChapter") + " - ✅ 全文验证完成，逻辑一致性良好");
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.warn('全文验证失败，使用原内容:', error);
          setStatus(t("research.common.writingChapter") + " - ⚠️ 全文验证失败，使用原内容");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 最终完整更新
      updateFinalReport(validatedFinalContent);

      return validatedFinalContent;
    } catch (error) {
      console.error("生成章节化报告时出错:", error);
      handleError(error);
      throw error;
    } finally {
      // 清理资源
      memoryOptimizer.destroy();
      reportBuilder.destroy();
    }
  }

  async function deepResearch() {
    const { reportPlan, currentChapterContext } = useTaskStore.getState();
    const tasksBeforeThisRun = [...taskStore.tasks];
    const { thinkingModel } = getModel();

    setStatus(t("research.common.thinking"));
    try {
      const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
      const result = streamText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          generateSerpQueriesPrompt(reportPlan),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
        onError: handleError,
      });

      const querySchema = getSERPQuerySchema();
      let content = "";
      let reasoning = "";
      let queries: SearchTask[] = [];
      for await (const textPart of result.textStream) {
        thinkTagStreamProcessor.processChunk(
            textPart,
            (text) => {
              content += text;
              const data: PartialJson = parsePartialJson(
                  removeJsonMarkdown(content)
              );
              if (querySchema.safeParse(data.value)) {
                if (
                    data.state === "repaired-parse" ||
                    data.state === "successful-parse"
                ) {
                  if (data.value) {
                    queries = data.value.map(
                        (item: { query: string; researchGoal: string }) => {
                          // 🔥 关键修复：如果存在章节上下文，为查询添加章节标识
                          let processedQuery = item.query;
                          if (currentChapterContext) {
                            const chapterTag = `[第${currentChapterContext.chapterIndex + 1}章: ${currentChapterContext.chapterTitle}]`;
                            processedQuery = `${chapterTag} ${item.query}`;
                          }

                          // 🔥 关键修复：在researchGoal中融入原始研究主题
                          const enhancedResearchGoal = currentChapterContext?.originalResearchTopic
                              ? `基于原始研究主题"${currentChapterContext.originalResearchTopic}"，${item.researchGoal}`
                              : item.researchGoal;

                          return {
                            state: "unprocessed",
                            learning: "",
                            query: processedQuery,
                            researchGoal: enhancedResearchGoal, // 🔥 使用增强的研究目标
                            chapterIndex: currentChapterContext?.chapterIndex,
                            chapterTitle: currentChapterContext?.chapterTitle,
                          };
                        }
                    );
                  }
                }
              }
            },
            (text) => {
              reasoning += text;
            }
        );
      }
      if (queries.length > 0) {
        // 🔥 简化：直接更新任务，合并逻辑已在store中处理
        taskStore.update(queries);
        await runSearchTask(queries);
      }
    } catch (err) {
      console.error(err);
    }
  }

  return {
    status,
    askQuestions,
    writeReportPlan,
    writeFinalReport,
    writeChapterReport,
    deepResearch,
    reviewSearchResult,
  };
}

export default useDeepResearch;
