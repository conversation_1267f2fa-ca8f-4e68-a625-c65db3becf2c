"use client";

import { ChapterInfo } from "@/components/MagicDown";
import { ResearchOutline, OutlineSection } from "@/types/outline";

export interface ChapterReplacement {
  originalContent: string;
  newContent: string;
  startIndex: number;
  endIndex: number;
  chapterTitle: string;
  type: 'main' | 'sub';
}

export class ChapterReplacer {
  
  /**
   * 在报告中精确定位并替换章节内容
   */
  static replaceChapterContent(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string,
    outline: ResearchOutline | null
  ): string {
    
    try {
      console.log(`🔍 开始定位和替换章节: "${chapterInfo.title}"`);
      console.log(`   - 章节类型: ${chapterInfo.type}`);
      console.log(`   - 新内容长度: ${newContent.length} 字符`);
      console.log(`   - 新内容预览: ${newContent.substring(0, 100)}...`);
      
      // 🔥 确保新内容是纯Markdown格式，移除可能的格式化标记
      newContent = this.cleanMarkdownContent(newContent);
      
      // 验证新内容是否为有效的Markdown格式
      if (!this.validateMarkdownContent(newContent)) {
        console.warn('⚠️ 新内容不符合Markdown格式要求，尝试修复格式');
        newContent = this.fixMarkdownFormat(newContent, chapterInfo);
      }
      
      // 确保新内容以换行符结尾
      if (!newContent.endsWith('\n')) {
        newContent = newContent + '\n';
      }
      
      // 🔥 确保新内容包含原始章节标题
      if (!this.contentContainsTitle(newContent, chapterInfo.title)) {
        console.warn('⚠️ 新内容不包含原始章节标题，尝试添加标题');
        newContent = this.addTitleToContent(newContent, chapterInfo);
      }
      
      // 🔥 基于大纲的精确替换 - 唯一的替换方法
      if (!outline) {
        throw new Error('需要大纲信息才能进行章节替换');
      }

      const result = this.performOutlineBasedReplacement(originalReport, chapterInfo, newContent, outline);
      
      if (result === originalReport) {
        throw new Error(`无法在大纲中找到章节或定位失败: ${chapterInfo.title}`);
      }

      console.log(`✅ 大纲基础替换成功`);
      console.log(`   - 替换前报告长度: ${originalReport.length}`);
      console.log(`   - 替换后报告长度: ${result.length}`);
      console.log(`   - 差异: ${result.length - originalReport.length}`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ 章节替换失败: ${chapterInfo.title}`, error);
      return originalReport;
    }
  }
  
  /**
   * 清理Markdown内容，移除可能的格式标记
   */
  private static cleanMarkdownContent(content: string): string {
    if (!content) return '';
    
    let cleaned = content.trim();
    
    // 移除可能的Markdown代码块标记
    cleaned = cleaned.replace(/^```markdown\s*/i, '');
    cleaned = cleaned.replace(/\s*```\s*$/i, '');
    
    // 移除可能的"以下是重写内容"等说明
    cleaned = cleaned.replace(/^(以下是|下面是|这是)(重写|修改|编辑|更新)的?(内容|章节|文本)[：:]\s*/i, '');
    cleaned = cleaned.replace(/^(Here is|Below is|This is)(the)?(rewritten|modified|edited|updated)?(content|chapter|text)[：:]\s*/i, '');
    
    return cleaned;
  }
  
  /**
   * 检查内容是否包含章节标题
   */
  private static contentContainsTitle(content: string, title: string): boolean {
    if (!content || !title) return false;
    
    // 检查内容是否以标题开头（最准确的方式）
    const contentLines = content.trim().split('\n');
    const firstLine = contentLines[0]?.trim() || '';
    
    // 直接检查第一行是否包含完整标题
    if (firstLine.includes(title)) {
      console.log('✅ 内容第一行包含完整标题');
      return true;
    }
    
    // 提取标题的核心部分（去掉编号）
    const titleParts = title.match(/^(\d+\.\d+(?:\.\d+)?)[、：: ]*(.*)/);
    if (titleParts) {
      const chapterNumber = titleParts[1];
      const chapterName = titleParts[2];
      
      // 检查第一行是否包含章节编号
      if (firstLine.includes(chapterNumber)) {
        console.log('✅ 内容第一行包含章节编号');
        return true;
      }
      
      // 检查内容是否包含章节名称
      if (chapterName && firstLine.includes(chapterName)) {
        console.log('✅ 内容第一行包含章节名称');
        return true;
      }
    }
    
    // 最后检查内容中是否包含完整标题
    if (content.includes(title)) {
      console.log('✅ 内容中包含完整标题');
      return true;
    }
    
    console.log('❌ 内容中未找到章节标题');
    console.log('期望标题:', title);
    console.log('内容第一行:', firstLine);
    return false;
  }
  
  /**
   * 添加标题到内容
   */
  private static addTitleToContent(content: string, chapterInfo: ChapterInfo): string {
    console.log(`🔧 正在添加标题到内容: "${chapterInfo.title}"`);
    
    // 如果内容已经以Markdown标题开始，检查是否是正确的标题
    const markdownTitleMatch = content.match(/^(#+)\s+(.+)/);
    if (markdownTitleMatch) {
      const existingTitle = markdownTitleMatch[2];
      console.log('发现现有Markdown标题:', existingTitle);
      
      // 如果现有标题包含章节编号，则可能是正确的
      const chapterNumber = this.extractChapterNumber(chapterInfo.title);
      if (chapterNumber && existingTitle.includes(chapterNumber)) {
        console.log('现有标题包含正确的章节编号，保持不变');
        return content;
      }
    }
    
    // 确保内容不以标题开始时，添加完整的原始标题
    console.log('添加完整的原始标题到内容开头');
    return `## ${chapterInfo.title}\n\n${content}`;
  }
  
  /**
   * 从标题中提取章节编号
   */
  private static extractChapterNumber(title: string): string | null {
    const patterns = [
      /^(\d+\.\d+\.\d+)[、：\s]/,  // 1.1.1、
      /^(\d+\.\d+)[、：\s]/,       // 1.1、
      /^(\d+)[、：\s]/,            // 1、
      /^第(\d+|[一二三四五六七八九十]+)章/, // 第1章
    ];
    
    for (const pattern of patterns) {
      const match = title.match(pattern);
      if (match) {
        return match[1];
      }
    }
    
    return null;
  }
  
  /**
   * 验证内容是否为有效的Markdown格式
   */
  private static validateMarkdownContent(content: string): boolean {
    if (!content || content.trim().length === 0) {
      console.error('❌ 内容为空');
      return false;
    }
    
    // 检查是否包含章节标题或章节编号
    const titlePatterns = [
      // 主章节标题模式
      /(第\d+章|第[一二三四五六七八九十]+章)/,
      // 子章节标题模式
      /(\d+\.\d+[、：:]|\d+\.\d+\.\d+[、：:])/,
      // Markdown标题模式
      /^#+\s+/m
    ];
    
    const hasTitleFormat = titlePatterns.some(pattern => pattern.test(content));
    if (!hasTitleFormat) {
      console.warn('⚠️ 内容中未找到符合格式的章节标题或编号');
    }
    
    // 检查基本的Markdown语法
    const markdownFeatures = [
      /\n\n/, // 段落分隔
      /^#+\s+/m, // 标题
      /\*\*.*?\*\*/, // 粗体
      /-\s+/, // 列表项
      /\d+\.\s+/, // 有序列表
      /\[.*?\]\(.*?\)/, // 链接
    ];
    
    const markdownScore = markdownFeatures.filter(pattern => pattern.test(content)).length;
    console.log(`Markdown格式特征评分: ${markdownScore}/6`);
    
    // 检查内容结构
    const paragraphs = content.split(/\n\n+/).filter(p => p.trim().length > 0);
    if (paragraphs.length < 2) {
      console.warn('⚠️ 内容结构不完整，缺少足够的段落');
    }
    
    // 检查内容长度
    if (content.length < 200) {
      console.warn('⚠️ 内容长度过短，可能不完整');
      return false;
    }
    
    // 综合评估
    return markdownScore >= 2 && paragraphs.length >= 2;
  }
  
  /**
   * 修复Markdown格式问题
   */
  private static fixMarkdownFormat(content: string, chapterInfo: ChapterInfo): string {
    console.log('🔧 尝试修复Markdown格式');
    
    let fixedContent = content.trim();
    
    // 确保内容以章节标题开始
    const titlePrefix = chapterInfo.type === 'main' ? '## 第' : '## ';
    if (!fixedContent.startsWith('#')) {
      // 尝试从内容中提取标题
      const titleMatch = fixedContent.match(/^(第\d+章|^\d+\.\d+、).*?$/m);
      if (titleMatch) {
        // 将找到的标题转换为Markdown格式
        const originalTitle = titleMatch[0];
        const markdownTitle = `${titlePrefix}${originalTitle}`;
        fixedContent = fixedContent.replace(originalTitle, markdownTitle);
      } else {
        // 如果无法提取标题，添加一个基于chapterInfo的标题
        fixedContent = `${titlePrefix}${chapterInfo.title}\n\n${fixedContent}`;
      }
    }
    
    // 确保段落之间有空行
    fixedContent = fixedContent.replace(/([^\n])\n([^\n])/g, '$1\n\n$2');
    
    // 确保列表项格式正确
    fixedContent = fixedContent.replace(/^(\d+)[.．。。](?!\s)/gm, '$1. ');
    fixedContent = fixedContent.replace(/^[-–—](?!\s)/gm, '- ');
    
    console.log('✅ Markdown格式修复完成');
    return fixedContent;
  }
  
  /**
   * 基于大纲的精确替换 - 用户建议的最佳方案
   * 从大纲中获取当前章节和下一章节标题，精确替换中间区域
   */
  private static performOutlineBasedReplacement(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string,
    outline: ResearchOutline
  ): string {
    console.log(`🎯 尝试基于大纲的精确替换: ${chapterInfo.title}`);
    
    try {
      // 1. 从大纲中找到当前章节
      const currentChapter = outline.chapters.find(ch => ch.id === chapterInfo.id);
      if (!currentChapter) {
        console.log(`❌ 在大纲中未找到当前章节: ${chapterInfo.id}`);
        return originalReport;
      }
      
      console.log(`📋 找到当前章节: ${currentChapter.title}`);
      
      // 2. 找到下一个章节
      const enabledChapters = outline.chapters.filter(ch => ch.enabled);
      const currentIndex = enabledChapters.findIndex(ch => ch.id === chapterInfo.id);
      
      if (currentIndex === -1) {
        console.log(`❌ 当前章节不在启用章节列表中`);
        return originalReport;
      }
      
      let nextChapter = null;
      let nextChapterTitle = '';
      
      if (currentIndex < enabledChapters.length - 1) {
        nextChapter = enabledChapters[currentIndex + 1];
        nextChapterTitle = nextChapter.title;
        console.log(`📋 找到下一章节: ${nextChapterTitle}`);
      } else {
        console.log(`📋 当前是最后一个章节`);
      }
      
      // 3. 在报告中定位当前章节标题
      const currentTitleVariants = [
        currentChapter.title,
        `**${currentChapter.title}**`,
        `## ${currentChapter.title}`,
        `### ${currentChapter.title}`,
        `${currentChapter.number}. ${currentChapter.title}`,
        `第${currentChapter.number}章 ${currentChapter.title}`,
      ];
      
      let currentTitleIndex = -1;
      let usedCurrentTitle = '';
      
      for (const titleVariant of currentTitleVariants) {
        const index = originalReport.indexOf(titleVariant);
        if (index !== -1) {
          currentTitleIndex = index;
          usedCurrentTitle = titleVariant;
          break;
        }
      }
      
      if (currentTitleIndex === -1) {
        console.log(`❌ 在报告中未找到当前章节标题的任何变体`);
        console.log(`尝试的标题变体:`, currentTitleVariants);
        return originalReport;
      }
      
      console.log(`✅ 找到当前章节标题: "${usedCurrentTitle}" 在位置 ${currentTitleIndex}`);
      
      // 4. 确定替换的开始位置（当前章节标题行的开始）
      const lineStart = originalReport.lastIndexOf('\n', currentTitleIndex);
      const replaceStart = lineStart === -1 ? 0 : lineStart + 1;
      
      // 5. 确定替换的结束位置
      let replaceEnd = originalReport.length;
      
      if (nextChapterTitle) {
        // 如果有下一章节，在报告中找到下一章节标题
        const nextTitleVariants = [
          nextChapterTitle,
          `**${nextChapterTitle}**`,
          `## ${nextChapterTitle}`,
          `### ${nextChapterTitle}`,
          `${nextChapter?.number}. ${nextChapterTitle}`,
          `第${nextChapter?.number}章 ${nextChapterTitle}`,
        ];
        
        for (const titleVariant of nextTitleVariants) {
          const index = originalReport.indexOf(titleVariant, currentTitleIndex + usedCurrentTitle.length);
          if (index !== -1) {
            const nextLineStart = originalReport.lastIndexOf('\n', index);
            replaceEnd = nextLineStart === -1 ? index : nextLineStart + 1;
            console.log(`✅ 找到下一章节标题: "${titleVariant}" 在位置 ${index}`);
            console.log(`✅ 替换结束位置: ${replaceEnd}`);
            break;
          }
        }
        
        if (replaceEnd === originalReport.length) {
          console.log(`⚠️ 未找到下一章节标题，使用报告结尾`);
        }
      }
      
      console.log(`🔄 替换范围: ${replaceStart} - ${replaceEnd} (${replaceEnd - replaceStart} 字符)`);
      
      // 6. 准备新内容
      let cleanNewContent = newContent.trim();
      if (!cleanNewContent.startsWith('#') && !cleanNewContent.includes(currentChapter.title)) {
        // 确保新内容包含章节标题
        cleanNewContent = `## ${currentChapter.title}\n\n${cleanNewContent}`;
      }
      cleanNewContent = cleanNewContent + '\n\n';
      
      // 7. 执行替换
      const result = originalReport.substring(0, replaceStart) + 
                    cleanNewContent + 
                    originalReport.substring(replaceEnd);
      
      console.log(`✅ 大纲基础替换完成:`);
      console.log(`   - 原长度: ${originalReport.length}`);
      console.log(`   - 新长度: ${result.length}`);
      console.log(`   - 差异: ${result.length - originalReport.length}`);
      console.log(`   - 替换内容长度: ${replaceEnd - replaceStart} -> ${cleanNewContent.length}`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ 大纲基础替换出错:`, error);
      return originalReport;
    }
  }


  
  /**
   * 简单替换方案 - 当精确定位失败时的备用方案
   */
  private static performSimpleReplacement(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string
  ): string {
    console.log(`🔧 尝试简单替换方案...`);
    
    // 方案1：基于标题的智能范围替换
    const intelligentReplacement = this.performIntelligentTitleReplacement(originalReport, chapterInfo, newContent);
    if (intelligentReplacement !== originalReport) {
      console.log(`✅ 智能标题替换成功`);
      return intelligentReplacement;
    }
    
    // 方案2：基于标题的简单范围替换
    const title = chapterInfo.title;
    const titleIndex = originalReport.indexOf(title);
    
    if (titleIndex !== -1) {
      console.log(`✅ 找到章节标题位置: ${titleIndex}`);
      
      // 尝试找到章节的开始位置（标题所在行的开始）
      const lineStart = originalReport.lastIndexOf('\n', titleIndex) + 1;
      const actualStart = lineStart === 0 ? 0 : lineStart;
      
      // 🔥 改进的下一章节查找逻辑
      let nextChapterIndex = originalReport.length;
      
      // 从当前章节标题之后开始搜索下一个章节标题
      const searchStartIndex = titleIndex + title.length + 50; // 给一些缓冲距离
      const remainingText = originalReport.substring(searchStartIndex);
      
      // 🔥 更精确的章节标题匹配模式
      const patterns = [
        /\n(第[一二三四五六七八九十\d]+章[^\n]*)/g,     // 主章节标题（必须在行首）
        /\n(\d+\.\d+、[^\n]*)/g,                        // 子章节标题（必须在行首）
        /\n(\d+、[^\n]*)/g,                             // 简单章节标题（必须在行首）
        /\n(#{1,6}\s+[^\n]+)/g,                        // Markdown标题（必须在行首）
        /\n(\*\*\d+\.\d+、[^*\n]+\*\*)/g,              // 粗体子章节标题
        /\n(\*\*第[一二三四五六七八九十\d]+章[^*\n]*\*\*)/g // 粗体主章节标题
      ];
      
      console.log(`🔍 搜索范围: ${searchStartIndex} - ${originalReport.length}`);
      console.log(`剩余文本长度: ${remainingText.length}`);
      
      for (const pattern of patterns) {
        pattern.lastIndex = 0; // 重置正则表达式状态
        const match = pattern.exec(remainingText);
        
        if (match && match.index !== undefined) {
          const potentialNextIndex = searchStartIndex + match.index;
          const foundTitle = match[1];
          
          console.log(`🎯 发现潜在下一章节: "${foundTitle}" 在位置 ${potentialNextIndex}`);
          
                     // 🔥 验证这确实是一个有效的章节标题
           if (this.isValidNextChapterTitle(foundTitle, title)) {
            nextChapterIndex = Math.min(nextChapterIndex, potentialNextIndex);
            console.log(`✅ 确认下一章节: "${foundTitle}" 在位置 ${potentialNextIndex}`);
            break; // 找到第一个有效章节就停止
          }
        }
      }
      
      // 如果没有找到下一个章节，尝试找到一个合理的结束位置
      if (nextChapterIndex === originalReport.length) {
        // 尝试找到一个大段落的结束位置
        const paragraphEnd = originalReport.indexOf("\n\n\n", titleIndex + title.length + 500);
        if (paragraphEnd !== -1) {
          nextChapterIndex = paragraphEnd;
          console.log(`未找到下一章节，使用段落结束作为结束位置: ${paragraphEnd}`);
        } else {
          // 如果没有找到明确的结束，使用一个合理的长度
          nextChapterIndex = Math.min(titleIndex + title.length + 10000, originalReport.length);
          console.log(`使用固定长度作为结束位置: ${nextChapterIndex - (titleIndex + title.length)} 字符`);
        }
      }
      
      console.log(`🔄 替换范围: ${actualStart} - ${nextChapterIndex} (${nextChapterIndex - actualStart} 字符)`);
      
      // 执行完整替换 - 确保新内容完全替换原内容
      const originalSegment = originalReport.substring(actualStart, nextChapterIndex);
      console.log(`原始段落长度: ${originalSegment.length} 字符`);
      console.log(`新内容长度: ${newContent.length} 字符`);
      
      const result = originalReport.substring(0, actualStart) + newContent + originalReport.substring(nextChapterIndex);
      
      console.log(`✅ 简单替换完成: ${originalReport.length} -> ${result.length} 字符`);
      console.log(`替换前后长度差异: ${result.length - originalReport.length} 字符`);
      return result;
    }
    
    console.log(`❌ 简单替换方案也失败`);
    return originalReport;
  }
  
  /**
   * 基于标题的智能范围替换
   */
  private static performIntelligentTitleReplacement(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string
  ): string {
    const title = chapterInfo.title;
    const titleIndex = originalReport.indexOf(title);
    
    if (titleIndex === -1) {
      console.log(`❌ 未找到章节标题: "${title}"`);
      return originalReport;
    }
    
    console.log(`🔍 找到章节标题位置: ${titleIndex}`);
    
    // 确定章节类型
    const chapterType = this.detectChapterTypeFromContext(originalReport, titleIndex, title);
    console.log(`📝 检测到章节类型: ${chapterType}`);
    
    // 找到章节开始位置（通常是标题行的开始）
    const lineStart = originalReport.lastIndexOf('\n', titleIndex) + 1;
    const actualStart = lineStart === 0 ? 0 : lineStart;
    
    // 根据章节类型确定结束位置
    let endIndex = originalReport.length;
    
    if (chapterType === 'main') {
      // 主章节：找到下一个主章节
      const nextMainChapterPattern = /(?=第\d+章|^#+ 第\d+章|^\*\*第\d+章)/gm;
      const remainingText = originalReport.substring(titleIndex + title.length);
      const nextMatch = nextMainChapterPattern.exec(remainingText);
      if (nextMatch) {
        endIndex = titleIndex + title.length + nextMatch.index;
      }
    } else {
      // 子章节：找到下一个章节（主章节或子章节）
      const nextChapterPattern = /(?=第\d+章|^\d+\.\d+、|^#+ |^\*\*)/gm;
      const remainingText = originalReport.substring(titleIndex + title.length);
      const nextMatch = nextChapterPattern.exec(remainingText);
      if (nextMatch) {
        endIndex = titleIndex + title.length + nextMatch.index;
      }
    }
    
    console.log(`🎯 替换范围: ${actualStart} - ${endIndex} (${endIndex - actualStart} 字符)`);
    
    // 执行替换 - 确保新内容完全替换原内容
    const originalSegment = originalReport.substring(actualStart, endIndex);
    console.log(`原始段落长度: ${originalSegment.length} 字符`);
    console.log(`新内容长度: ${newContent.length} 字符`);
    
    const result = originalReport.substring(0, actualStart) + newContent + originalReport.substring(endIndex);
    
    console.log(`✅ 智能标题替换完成: ${originalReport.length} -> ${result.length} 字符`);
    console.log(`替换前后长度差异: ${result.length - originalReport.length} 字符`);
    return result;
  }
  
  /**
   * 从上下文检测章节类型
   */
  private static detectChapterTypeFromContext(originalReport: string, titleIndex: number, title: string): 'main' | 'sub' {
    // 检查标题本身的格式
    if (title.includes('第') && title.includes('章')) {
      return 'main';
    }
    if (title.match(/^\d+\.\d+、/)) {
      return 'sub';
    }
    
    // 检查上下文
    const beforeContext = originalReport.substring(Math.max(0, titleIndex - 300), titleIndex);
    const afterContext = originalReport.substring(titleIndex, Math.min(originalReport.length, titleIndex + 300));
    
    // 如果前面有主章节，当前很可能是子章节
    if (beforeContext.match(/第\d+章/)) {
      return 'sub';
    }
    
    // 如果后面有子章节编号，当前很可能是主章节
    if (afterContext.match(/\d+\.\d+、/)) {
      return 'main';
    }
    
    // 默认为子章节
    return 'sub';
  }
  
  /**
   * 识别章节在报告中的精确位置
   */
  private static identifyChapterLocation(
    originalReport: string,
    chapterInfo: ChapterInfo,
    outline: ResearchOutline | null
  ): ChapterReplacement | null {
    
    if (chapterInfo.type === 'main') {
      return this.locateMainChapter(originalReport, chapterInfo, outline);
    } else {
      return this.locateSubChapter(originalReport, chapterInfo, outline);
    }
  }
  
  /**
   * 定位主章节位置（包含所有子章节）
   */
  private static locateMainChapter(
    originalReport: string,
    chapterInfo: ChapterInfo,
    outline: ResearchOutline | null
  ): ChapterReplacement | null {
    
    const mainChapter = outline?.chapters.find(ch => ch.id === chapterInfo.id);
    if (!mainChapter) return null;
    
    // 构建多种主章节匹配模式，支持Markdown格式
    const patterns = [
      // 标准格式: 第X章
      new RegExp(`(第${mainChapter.number}章[^\\n]*\\n[\\s\\S]*?)(?=第\\d+章|$)`, 'g'),
      // Markdown标题格式: # 第X章
      new RegExp(`(^# 第${mainChapter.number}章[^\\n]*\\n[\\s\\S]*?)(?=^# 第\\d+章|$)`, 'gm'),
      // Markdown二级标题: ## 第X章
      new RegExp(`(^## 第${mainChapter.number}章[^\\n]*\\n[\\s\\S]*?)(?=^## 第\\d+章|$)`, 'gm'),
      // 粗体格式: **第X章**
      new RegExp(`(^\\*\\*第${mainChapter.number}章[^\\n*]*\\*\\*[^\\n]*\\n[\\s\\S]*?)(?=^\\*\\*第\\d+章|$)`, 'gm')
    ];
    
    for (const pattern of patterns) {
      const match = pattern.exec(originalReport);
      if (match) {
        return {
          originalContent: match[1],
          newContent: '',
          startIndex: match.index,
          endIndex: match.index + match[1].length,
          chapterTitle: chapterInfo.title,
          type: 'main'
        };
      }
    }
    
    // 备用匹配方案：使用章节标题
    return this.fallbackTitleMatch(originalReport, chapterInfo.title, 'main');
  }
  
  /**
   * 定位子章节位置
   */
  private static locateSubChapter(
    originalReport: string,
    chapterInfo: ChapterInfo,
    outline: ResearchOutline | null
  ): ChapterReplacement | null {
    
    const section = this.findSectionInOutline(chapterInfo.id, outline);
    if (!section) return null;
    
    // 构建多种子章节匹配模式，支持Markdown格式
    const patterns = [
      // 标准格式: X.X、标题
      new RegExp(`(${this.escapeRegExp(section.number)}、[^\\n]*\\n[\\s\\S]*?)(?=\\d+\\.\\d+、|第\\d+章|$)`, 'g'),
      // Markdown三级标题: ### X.X、标题
      new RegExp(`(^### ${this.escapeRegExp(section.number)}、[^\\n]*\\n[\\s\\S]*?)(?=^### \\d+\\.\\d+、|^## |^# |$)`, 'gm'),
      // Markdown二级标题: ## X.X、标题
      new RegExp(`(^## ${this.escapeRegExp(section.number)}、[^\\n]*\\n[\\s\\S]*?)(?=^## \\d+\\.\\d+、|^# |$)`, 'gm'),
      // 粗体格式: **X.X、标题**
      new RegExp(`(^\\*\\*${this.escapeRegExp(section.number)}、[^\\n*]*\\*\\*[^\\n]*\\n[\\s\\S]*?)(?=^\\*\\*\\d+\\.\\d+、|^\\*\\*第\\d+章|$)`, 'gm')
    ];
    
    for (const pattern of patterns) {
      const match = pattern.exec(originalReport);
      if (match) {
        return {
          originalContent: match[1],
          newContent: '',
          startIndex: match.index,
          endIndex: match.index + match[1].length,
          chapterTitle: chapterInfo.title,
          type: 'sub'
        };
      }
    }
    
    // 备用匹配方案：使用章节标题
    return this.fallbackTitleMatch(originalReport, chapterInfo.title, 'sub');
  }
  
  /**
   * 在大纲中查找指定的子章节
   */
  private static findSectionInOutline(sectionId: string, outline: ResearchOutline | null): OutlineSection | null {
    if (!outline) return null;
    
    for (const chapter of outline.chapters) {
      const section = chapter.sections.find(s => s.id === sectionId);
      if (section) return section;
    }
    return null;
  }
  
  /**
   * Markdown格式的章节替换方案
   */
  private static performMarkdownReplacement(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string,
    outline: ResearchOutline | null
  ): string {
    console.log(`🔧 尝试Markdown格式替换...`);
    
    if (chapterInfo.type === 'main') {
      return this.replaceMarkdownMainChapter(originalReport, chapterInfo, newContent, outline);
    } else {
      return this.replaceMarkdownSubChapter(originalReport, chapterInfo, newContent, outline);
    }
  }
  
  /**
   * 替换Markdown格式的主章节
   */
  private static replaceMarkdownMainChapter(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string,
    outline: ResearchOutline | null
  ): string {
    const mainChapter = outline?.chapters.find(ch => ch.id === chapterInfo.id);
    if (!mainChapter) return originalReport;
    
    // 多种Markdown主章节匹配模式
    const patterns = [
      // 标准Markdown标题格式: # 第X章 标题
      new RegExp(`(^# 第${mainChapter.number}章[^\\n]*\\n[\\s\\S]*?)(?=^# 第\\d+章|$)`, 'gm'),
      // 带粗体的格式: **第X章 标题**
      new RegExp(`(^\\*\\*第${mainChapter.number}章[^\\n*]*\\*\\*[^\\n]*\\n[\\s\\S]*?)(?=^\\*\\*第\\d+章|$)`, 'gm'),
      // 二级标题格式: ## 第X章 标题
      new RegExp(`(^## 第${mainChapter.number}章[^\\n]*\\n[\\s\\S]*?)(?=^## 第\\d+章|$)`, 'gm'),
      // 带编号的格式: 第X章、标题
      new RegExp(`(^第${mainChapter.number}章、[^\\n]*\\n[\\s\\S]*?)(?=^第\\d+章、|$)`, 'gm')
    ];
    
    for (const pattern of patterns) {
      const match = pattern.exec(originalReport);
      if (match) {
        console.log(`✅ 找到Markdown主章节匹配: ${pattern.source}`);
        const replacement = originalReport.substring(0, match.index) + 
                           newContent + 
                           originalReport.substring(match.index + match[1].length);
        return replacement;
      }
    }
    
    return originalReport;
  }
  
  /**
   * 替换Markdown格式的子章节
   */
  private static replaceMarkdownSubChapter(
    originalReport: string,
    chapterInfo: ChapterInfo,
    newContent: string,
    outline: ResearchOutline | null
  ): string {
    const section = this.findSectionInOutline(chapterInfo.id, outline);
    if (!section) return originalReport;
    
    // 多种Markdown子章节匹配模式
    const patterns = [
      // 三级标题格式: ### X.X、标题
      new RegExp(`(^### ${this.escapeRegExp(section.number)}、[^\\n]*\\n[\\s\\S]*?)(?=^### \\d+\\.\\d+、|^## |^# |$)`, 'gm'),
      // 二级标题格式: ## X.X、标题
      new RegExp(`(^## ${this.escapeRegExp(section.number)}、[^\\n]*\\n[\\s\\S]*?)(?=^## \\d+\\.\\d+、|^# |$)`, 'gm'),
      // 粗体格式: **X.X、标题**
      new RegExp(`(^\\*\\*${this.escapeRegExp(section.number)}、[^\\n*]*\\*\\*[^\\n]*\\n[\\s\\S]*?)(?=^\\*\\*\\d+\\.\\d+、|^\\*\\*第\\d+章|$)`, 'gm'),
      // 标准格式: X.X、标题
      new RegExp(`(^${this.escapeRegExp(section.number)}、[^\\n]*\\n[\\s\\S]*?)(?=^\\d+\\.\\d+、|^第\\d+章|$)`, 'gm')
    ];
    
    for (const pattern of patterns) {
      const match = pattern.exec(originalReport);
      if (match) {
        console.log(`✅ 找到Markdown子章节匹配: ${pattern.source}`);
        const replacement = originalReport.substring(0, match.index) + 
                           newContent + 
                           originalReport.substring(match.index + match[1].length);
        return replacement;
      }
    }
    
    return originalReport;
  }
  
  /**
   * 备用标题匹配方案
   */
  private static fallbackTitleMatch(
    originalReport: string,
    title: string,
    type: 'main' | 'sub'
  ): ChapterReplacement | null {
    
    // 多种标题格式尝试
    const titleVariants = [
      title,  // 原始标题
      `**${title}**`,  // 粗体格式
      `### ${title}`,  // 三级标题
      `## ${title}`,   // 二级标题
      `# ${title}`,    // 一级标题
    ];
    
    for (const titleVariant of titleVariants) {
      const titleIndex = originalReport.indexOf(titleVariant);
      if (titleIndex !== -1) {
        console.log(`✅ 找到标题变体: "${titleVariant}"`);
        
        // 查找标题所在行的开始
        const lineStart = originalReport.lastIndexOf('\n', titleIndex);
        const actualStart = lineStart === -1 ? 0 : lineStart + 1;
        
        // 根据类型和格式确定结束位置
        let endPatterns: RegExp[];
        if (type === 'main') {
          endPatterns = [
            /(?=^# 第\d+章)/m,        // Markdown一级标题
            /(?=^## 第\d+章)/m,       // Markdown二级标题
            /(?=^\*\*第\d+章)/m,      // 粗体格式
            /(?=第\d+章)/,            // 标准格式
            /$/                       // 文档结尾
          ];
        } else {
          endPatterns = [
            /(?=^### \d+\.\d+、)/m,   // Markdown三级标题
            /(?=^## \d+\.\d+、)/m,    // Markdown二级标题
            /(?=^\*\*\d+\.\d+、)/m,   // 粗体格式
            /(?=^\d+\.\d+、)/m,       // 标准格式
            /(?=^# 第\d+章)/m,        // 下一个主章节
            /(?=第\d+章)/,            // 标准主章节
            /$/                       // 文档结尾
          ];
        }
        
        const remainingText = originalReport.substring(titleIndex);
        let endIndex = originalReport.length; // 默认到文档结尾
        
        for (const endPattern of endPatterns) {
          const endMatch = remainingText.search(endPattern);
          if (endMatch !== -1) {
            endIndex = titleIndex + endMatch;
            break;
          }
        }
        
        return {
          originalContent: originalReport.substring(actualStart, endIndex),
          newContent: '',
          startIndex: actualStart,
          endIndex: endIndex,
          chapterTitle: title,
          type
        };
      }
    }
    
    return null;
  }
  
  /**
   * 转义正则表达式特殊字符
   */
  private static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
  
  /**
   * 验证替换结果的完整性
   */
  static validateReplacement(
    originalReport: string,
    updatedReport: string,
    chapterInfo: ChapterInfo
  ): { isValid: boolean; message: string } {
    
    console.log(`🔍 开始验证章节替换结果:`);
    console.log(`   - 章节标题: "${chapterInfo.title}"`);
    console.log(`   - 章节类型: ${chapterInfo.type}`);
    console.log(`   - 原报告长度: ${originalReport.length}`);
    console.log(`   - 新报告长度: ${updatedReport.length}`);
    
    // 基础长度检查 - 降低严格度，允许合理的长度变化
    const lengthRatio = updatedReport.length / originalReport.length;
    if (lengthRatio < 0.5 || lengthRatio > 3.0) {
      return {
        isValid: false,
        message: `替换后的报告长度异常 (比例: ${lengthRatio.toFixed(2)})，可能发生了严重问题`
      };
    }
    
    // 智能章节标题存在检查
    const titleValidation = this.validateChapterTitle(updatedReport, chapterInfo);
    if (!titleValidation.isValid) {
      return titleValidation;
    }
    
    console.log(`✅ 章节替换验证通过`);
    return {
      isValid: true,
      message: '章节替换验证通过'
    };
  }
  
  /**
   * 智能验证章节标题是否存在
   */
  private static validateChapterTitle(
    updatedReport: string,
    chapterInfo: ChapterInfo
  ): { isValid: boolean; message: string } {
    
    const title = chapterInfo.title;
    console.log(`🔍 验证章节标题存在性: "${title}"`);
    
    // 构建所有可能的标题格式
    const titleFormats = [
      title,                        // 原始标题
      `**${title}**`,              // 粗体格式
      `### ${title}`,              // 三级标题
      `## ${title}`,               // 二级标题
      `# ${title}`,                // 一级标题
    ];
    
    // 如果有章节数据，添加编号格式
    if (chapterInfo.chapterData) {
      const data = chapterInfo.chapterData as any;
      if (data.number) {
        titleFormats.push(
          `${data.number}、${title}`,           // 标准编号格式
          `**${data.number}、${title}**`,       // 粗体编号格式
          `### ${data.number}、${title}`,       // 三级标题编号格式
          `## ${data.number}、${title}`,        // 二级标题编号格式
          `# ${data.number}、${title}`,         // 一级标题编号格式
        );
        
        // 主章节格式
        if (chapterInfo.type === 'main') {
          titleFormats.push(
            `第${data.number}章 ${title}`,           // 主章节标准格式
            `**第${data.number}章 ${title}**`,       // 主章节粗体格式
            `### 第${data.number}章 ${title}`,       // 主章节三级标题
            `## 第${data.number}章 ${title}`,        // 主章节二级标题
            `# 第${data.number}章 ${title}`,         // 主章节一级标题
          );
        }
      }
    }
    
    // 方式1：检查所有标题格式变体
    for (const format of titleFormats) {
      if (updatedReport.includes(format)) {
        console.log(`✅ 找到标题格式: "${format}"`);
        return { isValid: true, message: `找到标题格式: ${format}` };
      }
    }
    
    // 方式2：模糊匹配关键词（章节标题的主要词汇）
    const keywords = this.extractKeywords(title);
    const keywordMatches = keywords.filter(keyword => 
      updatedReport.includes(keyword) && keyword.length > 1
    );
    
    if (keywordMatches.length >= Math.min(3, keywords.length)) {
      console.log(`✅ 关键词匹配通过，匹配到: ${keywordMatches.join(', ')}`);
      return { isValid: true, message: '通过关键词匹配验证' };
    }
    
    // 方式3：检查是否包含章节的核心概念
    const coreTerms = this.getCoreTerms(title);
    const coreMatches = coreTerms.filter(term => updatedReport.includes(term));
    
    if (coreMatches.length > 0) {
      console.log(`✅ 核心概念匹配通过，匹配到: ${coreMatches.join(', ')}`);
      return { isValid: true, message: '通过核心概念匹配验证' };
    }
    
    // 方式4：检查Markdown内容结构
    const hasMarkdownStructure = this.validateMarkdownStructure(updatedReport);
    if (hasMarkdownStructure) {
      console.log(`✅ 检测到有效的Markdown结构`);
      return { isValid: true, message: '检测到有效的Markdown结构' };
    }
    
    // 最终检查：如果是模拟内容，检查特定标记
    if (updatedReport.includes('[注：这是模拟生成的') || 
        updatedReport.includes('模拟生成的章节内容') ||
        updatedReport.includes('实际使用中将调用真实的AI API')) {
      console.log(`✅ 检测到模拟生成内容，视为有效`);
      return { isValid: true, message: '检测到模拟生成的有效内容' };
    }
    
    console.log(`❌ 章节标题验证失败`);
    console.log(`   - 尝试的匹配方式: 标题格式匹配、关键词匹配、核心概念匹配、Markdown结构验证`);
    console.log(`   - 提取的关键词: ${keywords.join(', ')}`);
    console.log(`   - 匹配的关键词: ${keywordMatches.join(', ')}`);
    console.log(`   - 尝试的标题格式: ${titleFormats.join(', ')}`);
    
    return {
      isValid: false,
      message: `替换后的报告中未找到章节标题或相关内容: ${title}`
    };
  }
  
  /**
   * 验证Markdown结构的有效性
   */
  private static validateMarkdownStructure(content: string): boolean {
    // 检查是否包含Markdown标记
    const markdownIndicators = [
      /^#+ /m,           // 标题
      /\*\*.*?\*\*/,     // 粗体
      /\*.*?\*/,         // 斜体
      /`.*?`/,           // 行内代码
      /^- /m,            // 列表
      /^\d+\. /m,        // 有序列表
      /\[.*?\]\(.*?\)/,  // 链接
    ];
    
    const indicators = markdownIndicators.filter(pattern => pattern.test(content));
    
    // 如果包含多种Markdown标记，认为是有效结构
    return indicators.length >= 2;
  }
  
  /**
   * 提取章节标题的关键词
   */
  private static extractKeywords(title: string): string[] {
    // 去除常见的连接词和标点符号
    const stopWords = ['的', '与', '及', '和', '或', '、', '，', '。', '：', '；'];
    const words = title.split(/[、，。：；\s]+/).filter(word => 
      word.length > 1 && !stopWords.includes(word)
    );
    return words;
  }
  
  /**
   * 获取章节标题的核心术语
   */
  private static getCoreTerms(title: string): string[] {
    const coreTermsMap = {
      '创新性': ['创新', '技术', '优势', '领先'],
      '盈利模式': ['盈利', '收入', '营收', '模式'],
      '营销策略': ['营销', '策略', '推广', '市场'],
      '市场规模': ['市场', '规模', '容量', '需求'],
      '行业情况': ['行业', '市场', '竞争', '发展'],
      '团队': ['团队', '人员', '管理', '组织'],
      '财务': ['财务', '收入', '利润', '成本'],
      '管理': ['管理', '治理', '运营', '制度']
    };
    
    const terms: string[] = [];
    for (const [key, values] of Object.entries(coreTermsMap)) {
      if (title.includes(key)) {
        terms.push(...values);
      }
    }
    
    return [...new Set(terms)]; // 去重
  }

  /**
   * 验证是否为有效的下一章节标题
   */
  private static isValidNextChapterTitle(foundTitle: string, currentTitle: string): boolean {
    console.log(`🧪 验证下一章节标题: "${foundTitle}" (当前: "${currentTitle}")`);
    
    // 排除与当前标题相同的情况
    if (foundTitle === currentTitle) {
      console.log(`❌ 标题相同，跳过`);
      return false;
    }
    
    // 🔥 更严格的章节格式验证
    const chapterPatterns = [
      /^第(\d+)章\s*(.*)$/,           // 第X章 格式
      /^(\d+)\.(\d+)、(.*)$/,         // X.Y、格式  
      /^(\d+)、(.*)$/,                // X、格式
      /^##\s+第(\d+)章\s*(.*)$/,      // ## 第X章 格式
      /^##\s+(\d+)\.(\d+)、(.*)$/,    // ## X.Y、格式
    ];
    
    let foundIsValid = false;
    for (const pattern of chapterPatterns) {
      if (pattern.test(foundTitle)) {
        foundIsValid = true;
        break;
      }
    }
    
    if (!foundIsValid) {
      console.log(`❌ 找到的标题不符合章节格式: "${foundTitle}"`);
      return false;
    }
    
    // 验证章节编号递增逻辑
    const currentNumber = this.extractChapterNumber(currentTitle);
    const foundNumber = this.extractChapterNumber(foundTitle);
    
    console.log(`📊 章节编号对比: 当前="${currentNumber}", 找到="${foundNumber}"`);
    
    if (currentNumber && foundNumber) {
      // 比较章节编号是否合理递增
      const currentParts = currentNumber.split('.');
      const foundParts = foundNumber.split('.');
      
      console.log(`📊 编号部分对比: 当前=[${currentParts.join(',')}], 找到=[${foundParts.join(',')}]`);
      
      // 🔥 严格的编号验证
      if (currentParts.length === foundParts.length) {
        if (currentParts.length === 1) {
          // 主章节比较 (如 "1" vs "2")
          const currentNum = parseInt(currentParts[0]);
          const foundNum = parseInt(foundParts[0]);
          const isValid = foundNum > currentNum;
          console.log(`📊 主章节编号比较: ${currentNum} < ${foundNum} = ${isValid}`);
          return isValid;
        } else if (currentParts.length === 2) {
          // 子章节比较 (如 "1.1" vs "1.2" 或 "1.1" vs "2.1")
          const currentMajor = parseInt(currentParts[0]);
          const currentMinor = parseInt(currentParts[1]);
          const foundMajor = parseInt(foundParts[0]);
          const foundMinor = parseInt(foundParts[1]);
          
          // 同一主章节下的子章节递增
          if (currentMajor === foundMajor) {
            const isValid = foundMinor > currentMinor;
            console.log(`📊 同主章节子章节比较: ${currentMajor}.${currentMinor} < ${foundMajor}.${foundMinor} = ${isValid}`);
            return isValid;
          }
          
          // 不同主章节（下一个主章节的开始）
          if (foundMajor > currentMajor) {
            console.log(`📊 跨主章节比较: ${currentMajor}.${currentMinor} < ${foundMajor}.${foundMinor} = true`);
            return true;
          }
          
          console.log(`❌ 编号递减或无效: ${currentMajor}.${currentMinor} vs ${foundMajor}.${foundMinor}`);
          return false;
        }
      }
      
      // 🔥 如果层级不同，也要验证合理性
      if (currentParts.length < foundParts.length) {
        // 从主章节到子章节 (如 "第1章" 到 "1.1、")
        console.log(`📊 主章节到子章节的层级变化，允许`);
        return true;
      }
      
      if (currentParts.length > foundParts.length) {
        // 从子章节到主章节 (如 "1.1、" 到 "第2章")
        const currentMajor = parseInt(currentParts[0]);
        const foundMajor = parseInt(foundParts[0]);
        const isValid = foundMajor > currentMajor;
        console.log(`📊 子章节到主章节: ${currentMajor} < ${foundMajor} = ${isValid}`);
        return isValid;
      }
    }
    
    console.log(`❌ 无法提取或比较章节编号`);
    return false;
  }
} 