import { useState, useCallback } from 'react';
import { useOutlineStore } from '@/store/outline';
import { useTaskStore } from '@/store/task';
import { ResearchOutline, OutlineGenerationConfig } from '@/types/outline';

export function useOutlineManager() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  const outlineStore = useOutlineStore();
  const taskStore = useTaskStore();

  // AI生成大纲
  const generateOutlineFromQuestions = useCallback(async (
    questions: string[],
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _config: OutlineGenerationConfig
  ): Promise<ResearchOutline | null> => {
    setIsGenerating(true);
    setGenerationProgress(0);
    
    try {
      // 模拟AI生成过程
      setGenerationProgress(25);
      
      // 基于问题生成章节结构
      const chapters = questions.map((question, index) => ({
        id: `chapter-${index + 1}`,
        number: index + 1,
        title: `第${index + 1}章：${question}`,
        description: `针对"${question}"的深入分析`,
        wordCount: { min: 3000, max: 4000 },
        researchPoints: [
          `${question}的核心要素分析`,
          `相关数据和案例研究`,
          `影响因素和发展趋势`
        ],
        enabled: true,
        priority: 'medium' as const,
        sections: []
      }));
      
      setGenerationProgress(75);
      
      // 创建新大纲
      const outline = outlineStore.createOutline(
        '基于研究问题的大纲',
        '通过AI分析研究问题自动生成的大纲结构'
      );
      
      // 添加章节
      chapters.forEach(chapter => {
        outlineStore.addChapter(chapter);
      });
      
      setGenerationProgress(100);
      
      // 更新大纲来源
      const updatedOutline = {
        ...outline,
        source: 'ai_generated' as const,
        chapters
      };
      
      outlineStore.updateOutline(updatedOutline);
      
      return updatedOutline;
    } catch (error) {
      console.error('Failed to generate outline:', error);
      return null;
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [outlineStore]);

  // 从报告计划转换为大纲
  const convertReportPlanToOutline = useCallback((
    reportPlan: string,
    title?: string
  ): ResearchOutline | null => {
    try {
      // 解析报告计划文本，提取章节信息
      const lines = reportPlan.split('\n').filter(line => line.trim());
      const chapters: any[] = [];
      
      let currentChapter: any = null;
      let chapterNumber = 1;
      
      lines.forEach(line => {
        const trimmedLine = line.trim();
        
        // 检测章节标题（以数字开头或包含"章"字）
        if (/^\d+[、.]/.test(trimmedLine) || trimmedLine.includes('章')) {
          if (currentChapter) {
            chapters.push(currentChapter);
          }
          
          currentChapter = {
            id: `chapter-${chapterNumber}`,
            number: chapterNumber++,
            title: trimmedLine.replace(/^\d+[、.]/, '').trim(),
            description: '',
            wordCount: { min: 3000, max: 4000 },
            researchPoints: [],
            enabled: true,
            priority: 'medium' as const,
            sections: []
          };
        } else if (currentChapter && trimmedLine) {
          // 将其他内容作为描述或研究要点
          if (!currentChapter.description) {
            currentChapter.description = trimmedLine;
          } else {
            currentChapter.researchPoints.push(trimmedLine);
          }
        }
      });
      
      if (currentChapter) {
        chapters.push(currentChapter);
      }
      
      // 创建大纲
      const outline = outlineStore.createOutline(
        title || '从报告计划转换的大纲',
        '基于现有报告计划转换生成的大纲结构'
      );
      
      // 添加章节
      chapters.forEach(chapter => {
        outlineStore.addChapter(chapter);
      });
      
      // 更新大纲
      const updatedOutline = {
        ...outline,
        source: 'hybrid' as const,
        chapters
      };
      
      outlineStore.updateOutline(updatedOutline);
      
      return updatedOutline;
    } catch (error) {
      console.error('Failed to convert report plan:', error);
      return null;
    }
  }, [outlineStore]);

  // 验证大纲完整性
  const validateOutline = useCallback((outline: ResearchOutline) => {
    return outlineStore.validateOutline(outline);
  }, [outlineStore]);

  // 导出大纲
  const exportOutline = useCallback((outlineId: string) => {
    return outlineStore.exportOutline(outlineId);
  }, [outlineStore]);

  // 导入大纲
  const importOutline = useCallback((data: string) => {
    return outlineStore.importOutline(data);
  }, [outlineStore]);

  // 应用大纲到研究任务
  const applyOutlineToTask = useCallback((outline: ResearchOutline) => {
    // 设置为当前大纲
    outlineStore.setCurrentOutline(outline);
    // 设置到任务存储
    taskStore.setResearchOutline(outline);
    taskStore.setOutlineDriven(true);
  }, [taskStore, outlineStore]);

  // 获取大纲统计信息
  const getOutlineStats = useCallback((outlineId: string) => {
    return outlineStore.getOutlineStats(outlineId);
  }, [outlineStore]);

  return {
    // 状态
    isGenerating,
    generationProgress,
    
    // 大纲数据
    outlines: outlineStore.outlines,
    currentOutline: outlineStore.currentOutline,
    templates: outlineStore.templates,
    
    // 操作方法
    generateOutlineFromQuestions,
    convertReportPlanToOutline,
    validateOutline,
    exportOutline,
    importOutline,
    applyOutlineToTask,
    getOutlineStats,
    
    // Store方法
    createOutline: outlineStore.createOutline,
    updateOutline: outlineStore.updateOutline,
    deleteOutline: outlineStore.deleteOutline,
    setCurrentOutline: outlineStore.setCurrentOutline,
    
    // 章节管理
    addChapter: outlineStore.addChapter,
    updateChapter: outlineStore.updateChapter,
    deleteChapter: outlineStore.deleteChapter,
    
    // 模板管理
    saveAsTemplate: outlineStore.saveAsTemplate,
    applyTemplate: outlineStore.applyTemplate,
    deleteTemplate: outlineStore.deleteTemplate,
  };
} 