// 数据重置脚本 - 在浏览器控制台运行
// 这将清理不一致的数据，然后您可以重新上传文件

console.log("开始重置知识库数据...");

// 1. 备份当前数据（可选）
const currentKnowledge = localStorage.getItem('knowledgeStore');
const currentTask = localStorage.getItem('taskStore');
console.log("数据已备份到控制台，如需恢复可查看上方日志");

// 2. 清理不一致的数据
localStorage.removeItem('knowledgeStore');
localStorage.removeItem('taskStore');

console.log("数据重置完成！");
console.log("请刷新页面，然后重新上传您的文件。");
console.log("修复后的代码将确保所有文件正确保存到知识库中。");

// 如果需要恢复数据，运行以下代码：
// localStorage.setItem('knowledgeStore', currentKnowledge);
// localStorage.setItem('taskStore', currentTask); 