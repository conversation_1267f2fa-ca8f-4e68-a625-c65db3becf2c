/**
 * 格式处理功能演示
 * 展示修复后的格式化器如何正确处理代码块和特殊内容
 */

import { fixFormat } from './formatFixer';

/**
 * 演示代码块保护功能
 */
export function demonstrateCodeBlockProtection() {
  console.log('🎯 演示代码块保护功能\n');

  const problematicContent = `
项目公司商业模式分析

公司采用以下技术架构：

\`\`\`mermaid
flowchart TD
    A[用户请求] --> B{*是否认证*}
    B -->|Yes| C[**处理请求**]
    B -->|No| D[拒绝访问]
    C --> E[返回结果]
    D --> F[**记录日志**]
\`\`\`

核心算法实现：

\`\`\`javascript
// *重要*：这里的星号不应该被处理
function processData(data) {
  const result = data.map(item => {
    // **计算权重**
    return item * 0.8;
  });
  return result;
}

// 配置对象包含*特殊字符*
const config = {
  name: "项目公司",
  weight: "**高优先级**", 
  status: "*活跃*"
};
\`\`\`

## 业务指标

公司在*核心领域*表现**优异**，具体包括**营收增长**、**市场份额**和**技术创新**等方面。

根据报告(1)和调研【L-2】显示，表现良好。
`;

  console.log('原始内容:');
  console.log('---');
  console.log(problematicContent.trim());
  console.log('---\n');

  console.log('应用格式修复...\n');

  const result = fixFormat(problematicContent, {
    fixTitles: true,
    fixBold: true,
    fixSpacing: false,
    fixCitations: true,
    maxBoldPerParagraph: 2
  });

  console.log('修复后内容:');
  console.log('---');
  console.log(result.content.trim());
  console.log('---\n');

  console.log(`✅ 修复了 ${result.fixedIssues.length} 个问题:`);
  result.fixedIssues.forEach((issue, index) => {
    console.log(`  ${index + 1}. 第${issue.line}行: ${issue.expected} (${issue.type})`);
  });

  if (result.remainingIssues.length > 0) {
    console.log(`\n⚠️ 还有 ${result.remainingIssues.length} 个问题需要注意:`);
    result.remainingIssues.forEach((issue, index) => {
      console.log(`  ${index + 1}. 第${issue.line}行: ${issue.expected} (${issue.type}) [${issue.severity}]`);
    });
  }

  console.log('\n🔍 验证代码块保护效果:');
  
  // 检查mermaid代码块是否保持完整
  const mermaidMatch = result.content.match(/```mermaid[\s\S]*?```/);
  if (mermaidMatch) {
    const mermaidContent = mermaidMatch[0];
    if (mermaidContent.includes('*是否认证*') && mermaidContent.includes('**处理请求**')) {
      console.log('✅ Mermaid代码块内容完整保护');
    } else {
      console.log('❌ Mermaid代码块内容被意外修改');
    }
  }

  // 检查JavaScript代码块是否保持完整
  const jsMatch = result.content.match(/```javascript[\s\S]*?```/);
  if (jsMatch) {
    const jsContent = jsMatch[0];
    if (jsContent.includes('*重要*') && jsContent.includes('**计算权重**') && jsContent.includes('*特殊字符*')) {
      console.log('✅ JavaScript代码块内容完整保护');
    } else {
      console.log('❌ JavaScript代码块内容被意外修改');
    }
  }

  // 检查正常内容是否正确处理
  const normalContent = result.content.replace(/```[\s\S]*?```/g, '');
  if (normalContent.includes('**营收增长**') && !normalContent.includes('*核心领域*')) {
    console.log('✅ 正常内容格式正确处理');
  } else {
    console.log('⚠️ 正常内容处理可能有问题');
  }

  console.log('\n🎉 代码块保护功能演示完成！');
  
  return result;
}

/**
 * 演示边界情况处理
 */
export function demonstrateEdgeCases() {
  console.log('\n🔬 演示边界情况处理\n');

  const edgeCaseContent = `
项目分析

\`\`\`
未闭合的代码块
包含**粗体**和*斜体*
这里应该被保护

正常内容继续，包含*问题格式*。
`;

  console.log('测试未闭合代码块的处理:');
  console.log('原始内容:', edgeCaseContent.trim());

  const result1 = fixFormat(edgeCaseContent, {
    fixTitles: true,
    fixBold: true,
    fixCitations: false
  });

  console.log('修复后内容:', result1.content.trim());
  console.log('修复问题数:', result1.fixedIssues.length);

  console.log('\n---\n');

  const mixedContent = `
\`\`\`mermaid
graph TD
    A --> B
\`\`\`

正常段落*需要修复*。

\`\`\`javascript
console.log("test");
\`\`\`

另一个*需要修复*的段落。
`;

  console.log('测试混合内容处理:');
  console.log('原始内容:', mixedContent.trim());

  const result2 = fixFormat(mixedContent, {
    fixBold: true
  });

  console.log('修复后内容:', result2.content.trim());
  console.log('修复问题数:', result2.fixedIssues.length);

  console.log('\n✅ 边界情况处理演示完成！');

  return { result1, result2 };
}

/**
 * 运行完整演示
 */
export function runFormatDemo() {
  console.log('🚀 开始格式处理功能完整演示\n');
  console.log('='.repeat(60));
  
  const demoResult = demonstrateCodeBlockProtection();
  
  console.log('\n' + '='.repeat(60));
  
  const edgeResult = demonstrateEdgeCases();
  
  console.log('\n' + '='.repeat(60));
  console.log('\n🎯 演示总结:');
  console.log('1. ✅ 代码块内容完全受保护，不会被格式化');
  console.log('2. ✅ Mermaid图表语法保持完整');
  console.log('3. ✅ 正常文本内容正确格式化');
  console.log('4. ✅ 未闭合代码块也能正确处理');
  console.log('5. ✅ 混合内容场景工作正常');
  
  console.log('\n🎉 演示完成！现在格式化器可以安全处理包含代码块的内容了。');
  
  return {
    demoResult,
    edgeResult
  };
} 