import { z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";
import {
  systemInstruction,
  systemQuestionPrompt,
  reportPlanPrompt,
  serpQueriesPrompt,
  queryResultPrompt,
  offlineQueryResultPrompt,
  citationRulesPrompt,
  searchResultPrompt,
  searchKnowledgeResultPrompt,
  reviewPrompt,
  finalReportCitationImagePrompt,
  finalReportReferencesPrompt,
  finalReportPrompt,
  chapterReportPrompts,
  reportTitlePrompt,
  strictFormatTemplate,
  chapterAvoidanceMap,
  getChapterWordCount,
} from "@/constants/prompts";
import { applyWritingConfigToPrompt } from "@/utils/writing-config-helper";

export function getSERPQuerySchema() {
  return z
    .array(
      z
        .object({
          query: z.string().describe("The SERP query."),
          researchGoal: z
            .string()
            .describe(
              "First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions. JSON reserved words should be escaped."
            ),
        })
        .required({ query: true, researchGoal: true })
    )
    .describe(`List of SERP queries.`);
}

export function getSERPQueryOutputSchema() {
  const SERPQuerySchema = getSERPQuerySchema();
  return JSON.stringify(zodToJsonSchema(SERPQuerySchema), null, 4);
}

export function getSystemPrompt() {
  return systemInstruction.replace("{now}", new Date().toISOString());
}

export function generateQuestionsPrompt(query: string) {
  return systemQuestionPrompt.replace("{query}", query);
}

export function writeReportPlanPrompt(query: string) {
  return reportPlanPrompt.replace("{query}", query);
}

export function generateSerpQueriesPrompt(plan: string) {
  return serpQueriesPrompt
    .replace("{plan}", plan)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function processResultPrompt(query: string, researchGoal: string, enableSearch: boolean = true) {
  // 🔥 修复：根据联网设置选择合适的提示词
  const selectedPrompt = enableSearch ? queryResultPrompt : offlineQueryResultPrompt;
  return selectedPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal);
}

export function processSearchResultPrompt(
  query: string,
  researchGoal: string,
  results: Source[],
  enableReferences: boolean
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${result.url}">\n${
        result.content
      }\n</content>`
  );
  // 🔥 增强：确保研究目标信息被正确传递到搜索结果处理
  return (
    searchResultPrompt + (enableReferences ? `\n\n${citationRulesPrompt}` : "")
  )
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function processSearchKnowledgeResultPrompt(
  query: string,
  researchGoal: string,
  results: Knowledge[]
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" title="${result.title}">\n${
        result.content
      }\n</content>`
  );
  // 🔥 增强：确保本地知识搜索也包含研究目标上下文
  return searchKnowledgeResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function reviewSerpQueriesPrompt(
  plan: string,
  learning: string[],
  suggestion: string
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  return reviewPrompt
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{suggestion}", suggestion)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function writeFinalReportPrompt(
  plan: string,
  learning: string[],
  source: Source[],
  localResources: Resource[],
  images: ImageSource[],
  requirement: string,
  enableCitationImage: boolean,
  enableReferences: boolean,
  writingConfig?: import('@/types/outline').WritingConfig
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  const sources = source.map(
    (item, idx) =>
      `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`
  );
  const localResourcesList = localResources.map(
    (item, idx) =>
      `<local_resource index="L-${idx + 1}" name="${item.name}">\n${item.name}\n</local_resource>`
  );
  const imageList = images.map(
    (source, idx) => `${idx + 1}. ![${source.description}](${source.url})`
  );
  // 应用写作配置
  let enhancedPrompt = finalReportPrompt;
  if (writingConfig) {
    enhancedPrompt = applyWritingConfigToPrompt(finalReportPrompt, writingConfig);
  }

  return (
    enhancedPrompt +
    (enableCitationImage ? `\n\n${finalReportCitationImagePrompt}` : "") +
    (enableReferences ? `\n\n${finalReportReferencesPrompt}` : "")
  )
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{sources}", sources.join("\n"))
    .replace("{localResources}", localResourcesList.join("\n"))
    .replace("{images}", imageList.join("\n"))
    .replace("{requirement}", requirement);
}

export function writeReportTitlePrompt(
  plan: string,
  learning: string[],
  question: string,      // 新增：核心研究主题
  requirement: string    // 保留：补充要求
) {
  return reportTitlePrompt
    .replace("{question}", question || "未设置研究主题")
    .replace("{requirement}", requirement || "无特殊要求");
}

export function writeChapterReportPrompt(
  chapterNumber: number,
  plan: string,
  learning: string[],
  source: Source[],
  localResources: Resource[],
  images: ImageSource[],
  requirement: string,
  enableCitationImage: boolean,
  enableReferences: boolean,
  writingConfig?: import('@/types/outline').WritingConfig
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  const sources = source.map(
    (item, idx) =>
      `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`
  );
  const localResourcesList = localResources.map(
    (item, idx) =>
      `<local_resource index="L-${idx + 1}" name="${item.name}">\n${item.name}\n</local_resource>`
  );
  const imageList = images.map(
    (source, idx) => `${idx + 1}. ![${source.description}](${source.url})`
  );
  
  const chapterKey = `chapter${chapterNumber}` as keyof typeof chapterReportPrompts;
  const chapterPrompt = chapterReportPrompts[chapterKey];
  
  if (!chapterPrompt) {
    throw new Error(`Chapter ${chapterNumber} prompt not found`);
  }

  // 应用写作配置到章节提示词
  let enhancedChapterPrompt = chapterPrompt;
  if (writingConfig) {
    enhancedChapterPrompt = applyWritingConfigToPrompt(chapterPrompt, writingConfig);
  }

  // 定义各章节的标准标题
  const chapterTitles = [
    '一、项目公司的商业模式',
    '二、项目公司的经营状况', 
    '三、项目公司的管理团队',
    '四、项目公司的治理结构',
    '五、项目公司的发展情况与投资回报',
    '六、总结与评价'
  ];

  // 生成格式约束前缀
  const expectedTitle = chapterTitles[chapterNumber - 1];
  const avoidDuplication = chapterAvoidanceMap[chapterNumber as keyof typeof chapterAvoidanceMap] || '';
  
  const formatPrefix = strictFormatTemplate
    .replace('{expectedTitle}', expectedTitle)
    .replace('{avoidDuplication}', avoidDuplication);
  
  return (
    formatPrefix +
    enhancedChapterPrompt +
    (enableCitationImage ? `\n\n${finalReportCitationImagePrompt}` : "") +
    (enableReferences ? `\n\n${finalReportReferencesPrompt}` : "")
  )
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{sources}", sources.join("\n"))
    .replace("{localResources}", localResourcesList.join("\n"))
    .replace("{images}", imageList.join("\n"))
    .replace("{requirement}", requirement)
    .replace("{wordCount}", getChapterWordCount(chapterNumber));
}

export function getChapterList() {
  return [
    { number: 1, name: "项目公司的商业模式" },
    { number: 2, name: "项目公司的经营状况" },
    { number: 3, name: "项目公司的管理团队" },
    { number: 4, name: "项目公司的治理结构" },
    { number: 5, name: "项目公司的发展情况与投资回报" },
    { number: 6, name: "总结与评价" },
  ];
}
