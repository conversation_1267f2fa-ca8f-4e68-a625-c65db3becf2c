"use client";
import dynamic from "next/dynamic";
import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  LoaderCircle,
  CircleCheck,
  TextSearch,
  Download,
  Trash,
  RotateCcw,
  NotebookText,
  BookOpen,
} from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import useAccurateTimer from "@/hooks/useAccurateTimer";
import useDeepResearch from "@/hooks/useDeepResearch";
import useKnowledge from "@/hooks/useKnowledge";
import { useTaskStore } from "@/store/task";
import { useKnowledgeStore } from "@/store/knowledge";
import { downloadFile } from "@/utils/file";
import { useOutlineDrivenResearch } from "@/hooks/useOutlineDrivenResearch";


const MagicDown = dynamic(() => import("@/components/MagicDown"));
const MagicDownView = dynamic(() => import("@/components/MagicDown/View"));
const Lightbox = dynamic(() => import("@/components/Internal/Lightbox"));

const formSchema = z.object({
  suggestion: z.string().optional(),
});

function addQuoteBeforeAllLine(text: string = "") {
  return text
      .split("\n")
      .map((line) => `> ${line}`)
      .join("\n");
}

function TaskState({ state }: { state: SearchTask["state"] }) {
  if (state === "completed") {
    return <CircleCheck className="h-5 w-5" />;
  } else if (state === "processing") {
    return <LoaderCircle className="animate-spin h-5 w-5" />;
  } else {
    return <TextSearch className="h-5 w-5" />;
  }
}

// function parseChapterInfo(query: string) {
//   if (!query || typeof query !== 'string') {
//     return null;
//   }
//
//   // 🔥 调试：输出查询字符串和匹配结果
//   const chapterMatch = query.match(/\[第(\d+)章:\s*([^\]]+)\]/);
//   if (chapterMatch) {
//     const result = {
//       chapterNumber: parseInt(chapterMatch[1]),
//       chapterTitle: chapterMatch[2].trim(),
//       cleanQuery: query.replace(/\[第\d+章:\s*[^\]]+\]\s*/, '').trim(),
//     };
//     return result;
//   }
//   return null;
// }

// 🔥 新增：扩展的任务类型
interface ExtendedSearchTask extends SearchTask {
  originalQuery?: string;
}

function groupTasksByChapter(tasks: SearchTask[]) {
  const chapterGroups: { [key: string]: ExtendedSearchTask[] } = {};
  const generalTasks: SearchTask[] = [];

  tasks.forEach((task) => {
    // 确保任务有query字段
    if (!task || !task.query) {
      return;
    }

    // 🔥 优先使用显式的章节字段
    if (typeof task.chapterIndex === 'number' && task.chapterTitle) {
      const key = `第${task.chapterIndex + 1}章：${task.chapterTitle}`;
      if (!chapterGroups[key]) chapterGroups[key] = [];
      chapterGroups[key].push({
        ...task,
        originalQuery: task.query,
        // 确保显示的query不包含章节标识（已经在tab中显示）
        query: task.query.replace(/\[第\d+章:\s*[^\]]+\]\s*/, '').trim() || task.query
      });
      return;
    }

    // 🔥 备用：解析query中的章节标识
    const chapterMatch = task.query.match(/\[第(\d+)章:\s*([^\]]+)\]/);
    if (chapterMatch) {
      const chapterNumber = parseInt(chapterMatch[1]);
      const chapterTitle = chapterMatch[2].trim();
      const key = `第${chapterNumber}章：${chapterTitle}`;
      if (!chapterGroups[key]) {
        chapterGroups[key] = [];
      }

      chapterGroups[key].push({
        ...task,
        query: task.query.replace(/\[第\d+章:\s*[^\]]+\]\s*/, '').trim(),
        originalQuery: task.query,
      });
    } else {
      // 🔥 处理未分类任务
      const taskStore = useTaskStore.getState();
      if (taskStore.isOutlineDriven) {
        // 在大纲驱动模式下，没有章节标识的任务可能是历史任务
        console.warn('发现未分类任务:', task.query);
        // 暂时不显示这些任务，避免混淆
      } else {
        // 传统模式：分配到通用任务
        generalTasks.push(task);
      }
    }
  });

  return { chapterGroups, generalTasks };
}

function SearchResult() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const { status: deepResearchStatus, runSearchTask, reviewSearchResult } = useDeepResearch();
  const { status: outlineResearchStatus, startOutlineDrivenResearch, isResearching } = useOutlineDrivenResearch();

  // 🔥 修复：根据研究模式选择正确的状态
  const status = taskStore.isOutlineDriven ? outlineResearchStatus : deepResearchStatus;
  const { generateId } = useKnowledge();
  const {
    formattedTime,
    start: accurateTimerStart,
    stop: accurateTimerStop,
  } = useAccurateTimer();
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const [chapterSuggestions, setChapterSuggestions] = useState<{[key: string]: string}>({});
  // const [forceUpdate, setForceUpdate] = useState(0);

  const { chapterGroups, generalTasks } = useMemo(() => {
    const result = groupTasksByChapter(taskStore.tasks);
    // 🔥 调试：输出任务分组信息
    return result;
  }, [taskStore.tasks]);

  // 🔥 新增：基于大纲生成章节Tab页，即使没有任务也显示
  const outlineChapters = useMemo(() => {
    if (taskStore.isOutlineDriven && taskStore.researchOutline) {
      const chapters = taskStore.researchOutline.chapters
          .map((chapter: any, originalIndex: number) => {
            if (!chapter.enabled) return null;

            const chapterNumber = originalIndex + 1; // 使用原始索引
            const key = `第${chapterNumber}章：${chapter.title}`;

            return {
              key,
              title: chapter.title,
              chapterNumber,
              tasks: chapterGroups[key] || []
            };
          })
          .filter((chapter): chapter is NonNullable<typeof chapter> => chapter !== null); // 类型安全的过滤

      // 🔥 调试：输出大纲章节信息

      return chapters;
    }
    return [];
  }, [taskStore.isOutlineDriven, taskStore.researchOutline, chapterGroups]);

  // 决定是否显示Tab页：有章节分组或者是大纲驱动模式
  const shouldShowTabs = Object.keys(chapterGroups).length > 0 || outlineChapters.length > 0;

  const unfinishedTasks = useMemo(() => {
    return taskStore.tasks.filter((item) => item.state !== "completed");
  }, [taskStore.tasks]);

  const taskFinished = useMemo(() => {
    return taskStore.tasks.length > 0 && unfinishedTasks.length === 0;
  }, [taskStore.tasks, unfinishedTasks]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      suggestion: taskStore.suggestion,
    },
  });

  function getSearchResultContent(item: SearchTask) {
    return [
      `## ${item.query}`,
      addQuoteBeforeAllLine(item.researchGoal),
      "---",
      item.learning,
      item.images?.length > 0
          ? `#### ${t("research.searchResult.relatedImages")}\n\n${item.images
              .map(
                  (source) =>
                      `![${source.description || source.url}](${source.url})`
              )
              .join("\n")}`
          : "",
      item.sources?.length > 0
          ? `#### ${t("research.common.sources")}\n\n${item.sources
              .map(
                  (source, idx) =>
                      `${idx + 1}. [${source.title || source.url}][${idx + 1}]`
              )
              .join("\n")}`
          : "",
    ].join("\n\n");
  }

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    const { setSuggestion } = useTaskStore.getState();
    try {
      accurateTimerStart();
      setIsThinking(true);
      if (unfinishedTasks.length > 0) {
        await runSearchTask(unfinishedTasks);
      } else {
        // 🔥 修改：进一步研究只针对输入框内容
        if (values.suggestion?.trim()) {
          await handleFurtherResearch(values.suggestion.trim());
        }
      }
    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  // 🔥 新增：专门处理进一步研究的函数
  async function handleFurtherResearch(suggestion: string) {
    // 直接基于用户输入创建新的研究任务，不调用reviewSearchResult
    const newTask = {
      query: suggestion,
      researchGoal: `针对"${suggestion}"的进一步深度研究`,
      learning: "",
      sources: [],
      images: [],
      state: "unprocessed" as const,
    };

    // 添加新任务到现有任务列表
    taskStore.update([...taskStore.tasks, newTask]);

    // 执行新任务的研究
    await runSearchTask([newTask]);

    // 清空输入框
    form.setValue("suggestion", "");
  }

  // 🔥 新增：处理章节特定的研究建议
  async function handleChapterSuggestion(chapterTitle: string, suggestion: string) {
    if (!suggestion.trim()) return;

    try {
      accurateTimerStart();
      setIsThinking(true);

      // 🔥 修复：在大纲驱动模式下，直接为特定章节生成研究任务
      if (taskStore.isOutlineDriven) {
        // 提取章节编号
        const chapterMatch = chapterTitle.match(/第(\d+)章/);
        if (chapterMatch) {
          const chapterNumber = parseInt(chapterMatch[1]);
          const chapterInfo = taskStore.researchOutline?.chapters[chapterNumber - 1];

          if (chapterInfo) {
            // 生成章节特定的研究任务
            const chapterQuery = `[第${chapterNumber}章: ${chapterInfo.title}] ${suggestion}`;
            const newTask = {
              query: chapterQuery,
              researchGoal: `针对第${chapterNumber}章"${chapterInfo.title}"的深度研究`,
              learning: "",
              sources: [],
              images: [],
              state: "unprocessed" as const,
            };

            // 添加新任务并执行研究
            taskStore.update([...taskStore.tasks, newTask]);
            await runSearchTask([newTask]);
          }
        }
      } else {
        // 传统模式：使用原有逻辑
        const { setSuggestion } = useTaskStore.getState();
        setSuggestion(`[${chapterTitle}] ${suggestion}`);
        await reviewSearchResult();
      }

      // 清空该章节的建议
      setChapterSuggestions(prev => ({
        ...prev,
        [chapterTitle]: ''
      }));

    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  function addToKnowledgeBase(item: SearchTask) {
    const { save } = useKnowledgeStore.getState();
    const currentTime = Date.now();
    save({
      id: generateId("knowledge"),
      title: item.query,
      content: getSearchResultContent(item),
      type: "knowledge",
      createdAt: currentTime,
      updatedAt: currentTime,
    });
    toast.message(t("research.common.addToKnowledgeBaseTip"));
  }

  async function handleRetry(originalQuery: string, researchGoal: string) {
    const { updateTask } = useTaskStore.getState();
    const newTask: SearchTask = {
      query: originalQuery,
      researchGoal,
      learning: "",
      sources: [],
      images: [],
      state: "unprocessed",
    };
    updateTask(originalQuery, newTask);
    await runSearchTask([newTask]);
  }

  function handleRemove(originalQuery: string) {
    const { removeTask } = useTaskStore.getState();
    removeTask(originalQuery);
  }

  function renderTask(item: ExtendedSearchTask | SearchTask, idx: number, keyPrefix: string = '') {
    const taskKey = keyPrefix ? `${keyPrefix}-${idx}` : `task-${idx}`;
    const queryToUse = (item as ExtendedSearchTask).originalQuery || item.query;

    return (
        <AccordionItem key={taskKey} value={taskKey}>
          <AccordionTrigger>
            <div className="flex items-center">
              <TaskState state={item.state} />
              <span className="ml-2">{item.query}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="prose prose-slate dark:prose-invert max-w-full min-h-20">
            <MagicDownView>
              {addQuoteBeforeAllLine(item.researchGoal)}
            </MagicDownView>
            <Separator className="mb-4" />
            <MagicDown
                value={item.learning}
                onChange={(value) =>
                    taskStore.updateTask(queryToUse, { learning: value })
                }
                tools={
                  <>
                    <div className="px-1">
                      <Separator className="dark:bg-slate-700" />
                    </div>
                    <Button
                        className="float-menu-button"
                        type="button"
                        size="icon"
                        variant="ghost"
                        title={t("research.common.restudy")}
                        side="left"
                        sideoffset={8}
                        onClick={() =>
                            handleRetry(queryToUse, item.researchGoal)
                        }
                    >
                      <RotateCcw />
                    </Button>
                    <Button
                        className="float-menu-button"
                        type="button"
                        size="icon"
                        variant="ghost"
                        title={t("research.common.delete")}
                        side="left"
                        sideoffset={8}
                        onClick={() => handleRemove(queryToUse)}
                    >
                      <Trash />
                    </Button>
                    <div className="px-1">
                      <Separator className="dark:bg-slate-700" />
                    </div>
                    <Button
                        className="float-menu-button"
                        type="button"
                        size="icon"
                        variant="ghost"
                        title={t("research.common.addToKnowledgeBase")}
                        side="left"
                        sideoffset={8}
                        onClick={() => addToKnowledgeBase(item)}
                    >
                      <NotebookText />
                    </Button>
                    <Button
                        className="float-menu-button"
                        type="button"
                        size="icon"
                        variant="ghost"
                        title={t("research.common.export")}
                        side="left"
                        sideoffset={8}
                        onClick={() =>
                            downloadFile(
                                getSearchResultContent(item),
                                `${item.query}.md`,
                                "text/markdown;charset=utf-8"
                            )
                        }
                    >
                      <Download />
                    </Button>
                  </>
                }
            ></MagicDown>
            {item.images?.length > 0 ? (
                <>
                  <hr className="my-6" />
                  <h4>{t("research.searchResult.relatedImages")}</h4>
                  <Lightbox data={item.images}></Lightbox>
                </>
            ) : null}
            {item.sources?.length > 0 ? (
                <>
                  <hr className="my-6" />
                  <h4>{t("research.common.sources")}</h4>
                  <ol>
                    {item.sources.map((source, idx) => {
                      return (
                          <li className="ml-2" key={idx}>
                            <a href={source.url} target="_blank">
                              {source.title || source.url}
                            </a>
                          </li>
                      );
                    })}
                  </ol>
                </>
            ) : null}
          </AccordionContent>
        </AccordionItem>
    );
  }

  useEffect(() => {
    form.setValue("suggestion", taskStore.suggestion);
  }, [taskStore.suggestion, form]);

  // 🔥 调试：定时强制更新，确保数据能及时显示
  useEffect(() => {
    if (isResearching || taskStore.tasks.some(t => t.state === "processing")) {
      const interval = setInterval(() => {
        // setForceUpdate(prev => prev + 1);
      }, 1000); // 每秒强制更新一次

      return () => clearInterval(interval);
    }
  }, [isResearching, taskStore.tasks]);

  return (
      <section className="p-4 border rounded-md mt-4 print:hidden">
        <h3 className="font-semibold text-lg border-b mb-2 leading-10">
          {t("research.searchResult.title")}
        </h3>

        {/* 移除章节研究进度显示 */}
        {false && taskStore.chapterResearchProgress !== "" && (
            <div className="mb-6 p-4 border rounded-md bg-blue-50 dark:bg-blue-950">
              <h4 className="text-base font-semibold mb-2">
                章节研究进度
              </h4>

              <MagicDown
                  className="min-h-20"
                  value={taskStore.chapterResearchProgress}
                  onChange={(value) => taskStore.setChapterResearchProgress(value)}
              ></MagicDown>
            </div>
        )}

        {/* 移除研究状态显示 */}
        {false && isResearching && (
            <div className="mb-6 p-4 border rounded-md bg-yellow-50 dark:bg-yellow-950">
              <h4 className="text-base font-semibold mb-2 flex items-center">
                <LoaderCircle className="animate-spin mr-2 h-4 w-4" />
                研究进行中
              </h4>
              <div className="text-sm">
                <div className="font-medium">{status}</div>
                <div className="text-muted-foreground mt-1">
                  <small className="font-mono">{formattedTime}</small>
                </div>
              </div>
            </div>
        )}

        {/* 🔥 移除重复的分章节研究开始按钮 - 已在Feedback组件中提供主要入口 */}

        {taskStore.tasks.length === 0 ? (
            <div>{t("research.searchResult.emptyTip")}</div>
        ) : (
            <div>
              {/* 🔥 分章节Tab页显示 */}
              {shouldShowTabs ? (
                  <Tabs defaultValue={outlineChapters.length > 0 ? outlineChapters[0].key : Object.keys(chapterGroups)[0]} className="mb-4">
                    <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${outlineChapters.length > 0 ? outlineChapters.length : Object.keys(chapterGroups).length + (generalTasks.length > 0 && !taskStore.isOutlineDriven ? 1 : 0)}, 1fr)` }}>
                      {outlineChapters.length > 0 ? (
                          // 大纲驱动模式：显示大纲章节
                          outlineChapters.map((chapter: any) => (
                              <TabsTrigger key={chapter.key} value={chapter.key} className="text-xs">
                                第{chapter.chapterNumber}章
                              </TabsTrigger>
                          ))
                      ) : (
                          // 传统模式：显示任务分组
                          Object.keys(chapterGroups).map((chapterTitle) => (
                              <TabsTrigger key={chapterTitle} value={chapterTitle} className="text-xs">
                                {chapterTitle.replace(/^第\d+章：/, '第' + chapterTitle.match(/^第(\d+)章/)?.[1] + '章')}
                              </TabsTrigger>
                          ))
                      )}
                      {/* 🔥 修复：大纲驱动模式下不显示通用任务Tab */}
                      {generalTasks.length > 0 && !taskStore.isOutlineDriven && (
                          <TabsTrigger value="general" className="text-xs">
                            通用任务
                          </TabsTrigger>
                      )}
                    </TabsList>

                    {outlineChapters.length > 0 ? (
                        // 大纲驱动模式：显示大纲章节内容
                        outlineChapters.map((chapter: any) => (
                            <TabsContent key={chapter.key} value={chapter.key} className="space-y-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <BookOpen className="h-5 w-5" />
                                  <span className="ml-2 font-medium">{chapter.key}</span>
                                  <Badge variant="secondary" className="ml-2">
                                    {chapter.tasks.length} 个研究任务
                                  </Badge>
                                  {/* 🔥 显示章节研究内容的token数量 */}
                                  {(() => {
                                    const completedTasks = chapter.tasks.filter((task: any) => task.state === 'completed' && task.learning);
                                    if (completedTasks.length > 0) {
                                      const totalContent = completedTasks
                                          .map((task: any) => `## ${task.query}\n\n${task.learning}`)
                                          .join('\n\n---\n\n');
                                      const tokenCount = Math.ceil(totalContent.length / 3); // 简单估算：3字符≈1token
                                      const tokenCountK = (tokenCount / 1000).toFixed(1); // 转换为K单位
                                      return (
                                          <Badge variant="outline" className="ml-2 text-xs">
                                            {tokenCountK}K tokens
                                          </Badge>
                                      );
                                    }
                                    return null;
                                  })()}
                                </div>
                              </div>

                              {chapter.tasks.length > 0 ? (
                                  <Accordion type="multiple">
                                    {chapter.tasks.map((item: any, idx: number) => {
                                      // 🔥 调试：输出任务渲染信息
                                      return renderTask(item, idx, chapter.key);
                                    })}
                                  </Accordion>
                              ) : (
                                  <div className="text-center py-8 text-gray-500">
                                    <BookOpen className="w-12 h-12 mx-auto mb-2 opacity-50" />
                                    <p>该章节暂无研究内容</p>
                                    <p className="text-sm">请在下方添加研究建议开始研究</p>
                                  </div>
                              )}

                              {/* 🔥 章节特定的研究建议 */}
                              <div className="mt-6 p-4 border rounded-md bg-gray-50 dark:bg-gray-900">
                                <h5 className="font-medium mb-2">为本章节添加研究建议</h5>
                                <Textarea
                                    rows={3}
                                    placeholder={`为"${chapter.key}"添加更多研究(撰写)方向或具体问题...`}
                                    value={chapterSuggestions[chapter.key] || ''}
                                    onChange={(e) => setChapterSuggestions(prev => ({
                                      ...prev,
                                      [chapter.key]: e.target.value
                                    }))}
                                    disabled={isThinking}
                                />
                                <Button
                                    className="w-full mt-2"
                                    onClick={() => handleChapterSuggestion(chapter.key, chapterSuggestions[chapter.key] || '')}
                                    disabled={isThinking || isResearching || !chapterSuggestions[chapter.key]?.trim()}
                                >
                                  {(isThinking || isResearching) ? (
                                      <>
                                        <LoaderCircle className="animate-spin" />
                                        <span>研究中...</span>
                                      </>
                                  ) : (
                                      "继续研究本章节"
                                  )}
                                </Button>
                              </div>
                            </TabsContent>
                        ))
                    ) : (
                        // 传统模式：显示任务分组内容
                        Object.entries(chapterGroups).map(([chapterTitle, tasks]) => (
                            <TabsContent key={chapterTitle} value={chapterTitle} className="space-y-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <BookOpen className="h-5 w-5" />
                                  <span className="ml-2 font-medium">{chapterTitle}</span>
                                  <Badge variant="secondary" className="ml-2">
                                    {tasks.length} 个研究任务
                                  </Badge>
                                  {/* 🔥 显示章节研究内容的token数量 */}
                                  {(() => {
                                    const completedTasks = tasks.filter(task => task.state === 'completed' && task.learning);
                                    if (completedTasks.length > 0) {
                                      const totalContent = completedTasks
                                          .map(task => `## ${task.query}\n\n${task.learning}`)
                                          .join('\n\n---\n\n');
                                      const tokenCount = Math.ceil(totalContent.length / 3); // 简单估算：3字符≈1token
                                      const tokenCountK = (tokenCount / 1000).toFixed(1); // 转换为K单位
                                      return (
                                          <Badge variant="outline" className="ml-2 text-xs">
                                            {tokenCountK}K tokens
                                          </Badge>
                                      );
                                    }
                                    return null;
                                  })()}
                                </div>
                              </div>

                              <Accordion type="multiple">
                                {tasks.map((item, idx) => renderTask(item, idx, chapterTitle))}
                              </Accordion>

                              {/* 🔥 章节特定的研究建议 */}
                              <div className="mt-6 p-4 border rounded-md bg-gray-50 dark:bg-gray-900">
                                <h5 className="font-medium mb-2">为本章节添加研究建议</h5>
                                <Textarea
                                    rows={3}
                                    placeholder={`为"${chapterTitle}"添加更多研究(撰写)方向或具体问题...`}
                                    value={chapterSuggestions[chapterTitle] || ''}
                                    onChange={(e) => setChapterSuggestions(prev => ({
                                      ...prev,
                                      [chapterTitle]: e.target.value
                                    }))}
                                    disabled={isThinking}
                                />
                                <Button
                                    className="w-full mt-2"
                                    onClick={() => handleChapterSuggestion(chapterTitle, chapterSuggestions[chapterTitle] || '')}
                                    disabled={isThinking || isResearching || !chapterSuggestions[chapterTitle]?.trim()}
                                >
                                  {(isThinking || isResearching) ? (
                                      <>
                                        <LoaderCircle className="animate-spin" />
                                        <span>研究中...</span>
                                      </>
                                  ) : (
                                      "继续研究本章节"
                                  )}
                                </Button>
                              </div>
                            </TabsContent>
                        ))
                    )}

                    {/* 🔥 修复：大纲驱动模式下不显示通用任务内容 */}
                    {generalTasks.length > 0 && !taskStore.isOutlineDriven && (
                        <TabsContent value="general" className="space-y-4">
                          <div className="flex items-center">
                            <TextSearch className="h-5 w-5" />
                            <span className="ml-2 font-medium">通用研究任务</span>
                            <Badge variant="outline" className="ml-2">
                              {generalTasks.length} 个任务
                            </Badge>
                          </div>
                          <Accordion type="multiple">
                            {generalTasks.map((item, idx) => renderTask(item, idx, 'general'))}
                          </Accordion>
                        </TabsContent>
                    )}
                  </Tabs>
              ) : (
                  // 传统显示方式（无章节分组时）
                  <Accordion className="mb-4" type="multiple">
                    {generalTasks.map((item, idx) => renderTask(item, idx))}
                  </Accordion>
              )}

              {/* 🔥 通用研究建议（仅在非大纲驱动模式下显示） */}
              {!taskStore.isOutlineDriven && (
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)}>
                      <FormField
                          control={form.control}
                          name="suggestion"
                          render={({ field }) => (
                              <FormItem>
                                <FormLabel className="mb-2 font-semibold">
                                  {t("research.searchResult.suggestionLabel")}
                                </FormLabel>
                                <FormControl>
                                  <Textarea
                                      rows={3}
                                      placeholder={t(
                                          "research.searchResult.suggestionPlaceholder"
                                      )}
                                      disabled={isThinking}
                                      {...field}
                                  />
                                </FormControl>
                              </FormItem>
                          )}
                      />
                      <Button
                          className="w-full mt-4"
                          type="submit"
                          variant="default"
                          disabled={
                            isThinking ||
                            isResearching ||
                            (taskFinished && !form.watch("suggestion")?.trim())
                          }
                      >
                        {(isThinking || isResearching) ? (
                            <>
                              <LoaderCircle className="animate-spin" />
                              <span>{status}</span>
                              <small className="font-mono">{formattedTime}</small>
                            </>
                        ) : taskFinished ? (
                            t("research.common.indepthResearch")
                        ) : (
                            t("research.common.continueResearch")
                        )}
                      </Button>
                    </form>
                  </Form>
              )}
            </div>
        )}
      </section>
  );
}

export default SearchResult;
