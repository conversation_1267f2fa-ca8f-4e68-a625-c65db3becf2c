"use client";
import { useState } from "react";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/Internal/Button";
import { Plus, FileText, Settings, Layers, CheckCircle } from "lucide-react";
import { useOutlineStore } from "@/store/outline";
import { ResearchOutline } from "@/types/outline";
import OutlineHeader from "./OutlineHeader";
import ChapterList from "./ChapterList";
import OutlineSettings from "./OutlineSettings";
import OutlineTemplates from "./OutlineTemplates";
import OutlineValidation from "./OutlineValidation";

interface OutlineEditorProps {
  outline?: ResearchOutline;
  onSave?: (outline: ResearchOutline) => void;
  onCancel?: () => void;
}

export default function OutlineEditor({ outline, onSave, onCancel }: OutlineEditorProps) {
  const [activeTab, setActiveTab] = useState("structure");
  const [showPreview, setShowPreview] = useState(false);
  
  const outlineStore = useOutlineStore();
  // 优先使用store中的currentOutline，确保能响应状态变化
  const currentOutline = outlineStore.currentOutline || outline;

  const handleCreateNewOutline = () => {
    const newOutline = outlineStore.createOutline("新建大纲", "请输入大纲描述");
    if (onSave) {
      onSave(newOutline);
    }
  };

  if (!currentOutline) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">还没有大纲</h3>
          <p className="text-muted-foreground mb-6">
            创建一个新的研究大纲来开始您的项目
          </p>
          <div className="flex justify-center space-x-4">
            <Button onClick={handleCreateNewOutline}>
              <Plus className="w-4 h-4 mr-2" />
              创建新大纲
            </Button>
            <Button variant="outline" onClick={() => setActiveTab("templates")}>
              <Layers className="w-4 h-4 mr-2" />
              使用模板
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 大纲头部信息 */}
      <OutlineHeader outline={currentOutline} />

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="structure">
            <FileText className="w-4 h-4 mr-2" />
            大纲结构
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="w-4 h-4 mr-2" />
            写作配置
          </TabsTrigger>
          <TabsTrigger value="templates">
            <Layers className="w-4 h-4 mr-2" />
            模板管理
          </TabsTrigger>
          <TabsTrigger value="validation">
            <CheckCircle className="w-4 h-4 mr-2" />
            验证检查
          </TabsTrigger>
        </TabsList>

        <TabsContent value="structure" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">章节结构</h3>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? "编辑模式" : "预览模式"}
              </Button>
            </div>
          </div>
          
          <ChapterList outline={currentOutline} showPreview={showPreview} />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <OutlineSettings outline={currentOutline} />
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <OutlineTemplates />
        </TabsContent>

        <TabsContent value="validation" className="space-y-6">
          <OutlineValidation outline={currentOutline} />
        </TabsContent>
      </Tabs>

      {/* 底部操作按钮 */}
      {(onSave || onCancel) && (
        <div className="flex justify-end space-x-4 pt-6 border-t">
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              取消
            </Button>
          )}
          {onSave && (
            <Button onClick={() => onSave(currentOutline)}>
              保存大纲
            </Button>
          )}
        </div>
      )}
    </div>
  );
} 