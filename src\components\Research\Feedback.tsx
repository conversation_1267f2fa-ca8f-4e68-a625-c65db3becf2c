"use client";
import dynamic from "next/dynamic";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import useDeepResearch from "@/hooks/useDeepResearch";
import { useOutlineDrivenResearch } from "@/hooks/useOutlineDrivenResearch";
import useAccurateTimer from "@/hooks/useAccurateTimer";
import { useTaskStore } from "@/store/task";
import { useGlobalStore } from "@/store/global";
// import ChapterResearchProgress from "./ChapterResearchProgress";
import { toast } from "sonner";

// 使用动态导入
const MagicDown = dynamic(() => import("@/components/MagicDown"), {
  ssr: false
});

const formSchema = z.object({
  feedback: z.string()
});

function Feedback() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const { status, deepResearch, writeReportPlan, askQuestions } = useDeepResearch();
  const { 
    startOutlineDrivenResearch,
    isResearching: isOutlineResearching
    // researchChapter, 
    // writeChapterContent 
  } = useOutlineDrivenResearch();
  const {
    formattedTime,
    start: accurateTimerStart,
    stop: accurateTimerStop,
  } = useAccurateTimer();
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const [isResearch, setIsResaerch] = useState<boolean>(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      feedback: taskStore.feedback,
    },
  });

  async function startDeepResearch() {
    try {
      accurateTimerStart();
      setIsResaerch(true);
      await deepResearch();
    } finally {
      setIsResaerch(false);
      accurateTimerStop();
    }
  }

  // 检查研究主题是否已填写
  function checkTopic(): boolean {
    const topicInput = document.querySelector('textarea[name="topic"]') as HTMLTextAreaElement;
    if (!topicInput || !topicInput.value.trim()) {
      toast.error("请先填写研究(撰写)主题", {
        position: "top-center",
        style: {
          background: "#FEE2E2",
          border: "2px solid #EF4444",
          color: "#991B1B",
          fontSize: "1rem",
          padding: "1rem",
          textAlign: "center",
          width: "auto",
          maxWidth: "400px",
          margin: "0 auto"
        },
        duration: 3000
      });
      return false;
    }
    return true;
  }

  // 传统思考流程
  async function handleTraditionalThinking() {
    if (!checkTopic()) return;
    
    // 🔥 修复：从DOM获取主题并保存到store
    const topicInput = document.querySelector('textarea[name="topic"]') as HTMLTextAreaElement;
    const currentTopic = topicInput?.value?.trim() || '';
    
    if (!currentTopic) {
      toast.error("请先填写研究(撰写)主题");
      return;
    }
    
    // 🔥 关键修复：将主题保存到store中
    taskStore.setQuestion(currentTopic);
    
    try {
      setIsThinking(true);
      accurateTimerStart();
      await askQuestions();
    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  // 大纲驱动流程 - 打开大纲管理器
  function handleOutlineDriven() {
    if (!checkTopic()) return;
    
    // 🔥 修复：从DOM获取主题并保存到store
    const topicInput = document.querySelector('textarea[name="topic"]') as HTMLTextAreaElement;
    const currentTopic = topicInput?.value?.trim() || '';
    
    if (!currentTopic) {
      toast.error("请先填写研究(撰写)主题");
      return;
    }
    
    // 🔥 关键修复：将主题保存到store中
    taskStore.setQuestion(currentTopic);
    
    const { setOpenOutlineManager } = useGlobalStore.getState();
    setOpenOutlineManager(true);
  }

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    console.log('handleSubmit 被调用', { values });
    const { question, questions, setFeedback } = useTaskStore.getState();
    console.log('当前状态:', { question, questions, feedback: values.feedback });
    setFeedback(values.feedback);
    const prompt = [
      `Initial Query: ${question}`,
      `Follow-up Questions: ${questions}`,
      `Follow-up Feedback: ${values.feedback}`,
    ].join("\n\n");
    taskStore.setQuery(prompt);
    try {
      accurateTimerStart();
      setIsThinking(true);
      console.log('开始撰写报告提纲...', { prompt });
      await writeReportPlan();
      console.log('报告提纲撰写完成');
    } catch (error) {
      console.error('撰写报告提纲失败:', error);
      toast.error('撰写报告提纲失败，请检查网络连接和API配置');
    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  useEffect(() => {
    form.setValue("feedback", taskStore.feedback);
  }, [taskStore.feedback, form]);

  return (
    <section className="p-4 border rounded-md mt-4 print:hidden">
      <h3 className="font-semibold text-lg border-b mb-2 leading-10">
        {t("research.feedback.title")}
      </h3>
      {/* 如果处于异常状态，显示重置按钮 */}
      {taskStore.isOutlineDriven && !taskStore.researchOutline && taskStore.questions === "" && (
        <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="text-sm text-orange-800 mb-2">
            检测到研究状态异常，请重置后重新开始：
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              taskStore.setOutlineDriven(false);
              taskStore.setResearchOutline(null);
            }}
          >
            重置研究状态
          </Button>
        </div>
      )}
      {taskStore.questions === "" && taskStore.reportPlan === "" && !taskStore.researchOutline ? (
        // 显示两个路径选择按钮（无论question是否为空）
        <div className="space-y-4">
          <div className="text-center text-gray-600 mb-4">
            请选择研究方式：
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 大纲驱动流程 */}
            <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors">
              <div className="text-center">
                <h4 className="font-semibold text-lg mb-2">📋 大纲驱动研究(撰写)</h4>
                <p className="text-sm text-gray-600 mb-4">
                  选择或创建结构化大纲，进行系统性深度研究
                </p>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={handleOutlineDriven}
                  disabled={isThinking}
                >
                  选择大纲模板
                </Button>
              </div>
            </div>

            {/* 传统思考流程 */}
            <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors">
              <div className="text-center">
                <h4 className="font-semibold text-lg mb-2">💭 AI自主研究(撰写)</h4>
                <p className="text-sm text-gray-600 mb-4">
                  AI将分析您的问题，生成相关思考问题和研究计划
                </p>
                <Button
                  className="w-full"
                  onClick={handleTraditionalThinking}
                  disabled={isThinking}
                >
                  {isThinking ? (
                    <>
                      <LoaderCircle className="animate-spin" />
                      <span>思考中...</span>
                      <small className="font-mono">{formattedTime}</small>
                    </>
                  ) : (
                    t("research.common.startThinking")
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div>
          {/* 传统研究-系统提问 */}
          {taskStore.questions !== "" && (
            <>
              <div className="flex justify-between items-center">
                <h4 className="text-base font-semibold">
                  {t("research.feedback.questions")}
                </h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    taskStore.updateQuestions("");
                    taskStore.setFeedback("");
                  }}
                >
                  返回上一步
                </Button>
              </div>
              <MagicDown
                className="mt-2 min-h-20"
                value={taskStore.questions}
                onChange={(value) => taskStore.updateQuestions(value)}
              ></MagicDown>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)}>
                  <FormField
                    control={form.control}
                    name="feedback"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="mb-2 text-base font-semibold">
                          {t("research.feedback.feedbackLabel")}
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            rows={3}
                            placeholder={t("research.feedback.feedbackPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <div className="mt-4">
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isThinking}
                      onClick={() => {
                        console.log('按钮被点击了!');
                      }}
                    >
                      {isThinking ? (
                        <>
                          <LoaderCircle className="animate-spin" />
                          <span>思考中...</span>
                          <small className="font-mono">{formattedTime}</small>
                        </>
                      ) : (
                        t("research.common.writeReportPlan")
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </>
          )}

          {/* 🔥 大纲概览显示 */}
          {taskStore.isOutlineDriven && taskStore.researchOutline && (
            <>
              <h4 className="mt-4 text-base font-semibold">
                大纲概览
              </h4>
              <div className="mt-2 p-4 border rounded-md bg-blue-50 dark:bg-blue-950">
                <h5 className="font-medium mb-2">{taskStore.researchOutline.title}</h5>
                <p className="text-sm text-muted-foreground mb-3">
                  {taskStore.researchOutline.description}
                </p>
                <div className="space-y-1 mb-4">
                  <p className="text-sm font-medium">章节概览：</p>
                  {taskStore.researchOutline.chapters
                    .filter((c: any) => c.enabled)
                    .map((chapter: any, i: number) => (
                      <div key={chapter.id} className="text-sm text-muted-foreground">
                        第{i + 1}章：{chapter.title}
                      </div>
                    ))}
                </div>
                {taskStore.tasks.length === 0 && taskStore.chapterResearchProgress === "" ? (
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      大纲已确定，点击下方按钮开始分章节研究。
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // 返回重新选择：清除大纲相关状态
                          taskStore.setOutlineDriven(false);
                          taskStore.setResearchOutline(null);
                          taskStore.setChapterResearchProgress("");
                        }}
                        disabled={isThinking || isOutlineResearching}
                      >
                        ← 返回重新选择
                      </Button>
                      <Button
                        onClick={() => startOutlineDrivenResearch(taskStore.researchOutline!)}
                        disabled={isThinking || isOutlineResearching}
                      >
                        {(isThinking || isOutlineResearching) ? (
                          <>
                            <LoaderCircle className="animate-spin" />
                            <span>研究中...</span>
                          </>
                        ) : (
                          "开始分章节研究"
                        )}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    分章节研究已开始，请在预写区域查看进度。
                  </p>
                )}
              </div>
            </>
          )}

        </div>
      )}
      {(taskStore.questions !== "" && taskStore.reportPlan !== "") ? (
        <div className="mt-6">
          <h4 className="text-base font-semibold">
            {t("research.feedback.reportPlan")}
          </h4>
          <MagicDown
            className="mt-2 min-h-20"
            value={taskStore.reportPlan}
            onChange={(value) => taskStore.updateReportPlan(value)}
          ></MagicDown>
          <Button
            className="w-full mt-4"
            variant="default"
            onClick={() => startDeepResearch()}
            disabled={isResearch}
          >
            {isResearch ? (
              <>
                <LoaderCircle className="animate-spin" />
                <span>{status}</span>
                <small className="font-mono">{formattedTime}</small>
              </>
            ) : taskStore.tasks.length === 0 ? (
              t("research.common.startResearch")
            ) : (
              t("research.common.restartResearch")
            )}
          </Button>
        </div>
      ) : null}
      
      {/* 移除大纲整合功能部分 */}
    </section>
  );
}

export default Feedback;
