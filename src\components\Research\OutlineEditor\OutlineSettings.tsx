"use client";
import { useState } from "react";

import { Save, RotateCcw } from "lucide-react";
import { Button } from "@/components/Internal/Button";

import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useOutlineStore } from "@/store/outline";
import { ResearchOutline, WritingConfig } from "@/types/outline";

interface OutlineSettingsProps {
  outline: ResearchOutline;
}

export default function OutlineSettings({ outline }: OutlineSettingsProps) {
  const [formData, setFormData] = useState<WritingConfig>(outline.writingConfig);
  
  const outlineStore = useOutlineStore();

  const handleSave = () => {
    console.log('💾 保存写作配置:', formData);
    outlineStore.updateWritingConfig(formData);
    
    // 延迟获取更新后的配置，确保状态已同步
    setTimeout(() => {
      const updatedConfig = outlineStore.currentOutline?.writingConfig;
      console.log('✅ 配置保存后，大纲中的配置:', updatedConfig);
      console.log('🔄 配置生效验证 - 写作风格:', updatedConfig?.style);
      console.log('🔄 配置生效验证 - 语言要求:', updatedConfig?.languageRequirements);
      
      // 强制重新渲染表单以反映最新状态
      setFormData(updatedConfig || formData);
    }, 100);
  };

  const handleReset = () => {
    setFormData(outline.writingConfig);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">写作配置</h3>
          <p className="text-sm text-muted-foreground mt-1">
            🎯 当前配置来源：<span className="font-medium text-blue-600">大纲专属配置</span>
          </p>
          <p className="text-xs text-muted-foreground">
            配置将应用到所有基于此大纲的报告生成
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="w-4 h-4 mr-1" />
            重置
          </Button>
          <Button onClick={handleSave}>
            <Save className="w-4 h-4 mr-1" />
            保存配置
          </Button>
        </div>
      </div>

      {/* 写作模式 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">写作模式</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">模式选择</label>
            <Select
              value={formData.mode}
              onValueChange={(value: 'traditional' | 'chapter' | 'section') => 
                setFormData({ ...formData, mode: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="traditional">传统模式</SelectItem>
                <SelectItem value="chapter">章节模式</SelectItem>
                <SelectItem value="section">子章节模式</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {formData.mode === 'traditional' && '按照传统方式一次性生成完整报告'}
              {formData.mode === 'chapter' && '按章节逐个生成，适合长篇报告'}
              {formData.mode === 'section' && '按子章节逐个生成，最精细的控制'}
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">启用上下文记忆</label>
              <p className="text-xs text-muted-foreground">
                在章节间保持写作上下文的连贯性
              </p>
            </div>
            <Switch
              checked={formData.enableContextMemory}
              onCheckedChange={(checked) => 
                setFormData({ ...formData, enableContextMemory: checked })
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* 格式控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">格式控制</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">标题格式</label>
            <Select
              value={formData.format.titleFormat}
              onValueChange={(value: 'numbered' | 'plain' | 'custom') => 
                setFormData({ 
                  ...formData, 
                  format: { ...formData.format, titleFormat: value }
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="numbered">编号格式 (1、2、3、)</SelectItem>
                <SelectItem value="plain">纯文本格式</SelectItem>
                <SelectItem value="custom">自定义格式</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">包含引用</label>
              <p className="text-xs text-muted-foreground">
                在文章中包含研究来源的引用
              </p>
            </div>
            <Switch
              checked={formData.format.includeCitations}
              onCheckedChange={(checked) => 
                setFormData({ 
                  ...formData, 
                  format: { ...formData.format, includeCitations: checked }
                })
              }
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">段落间距</label>
            <Select
              value={formData.format.paragraphSpacing}
              onValueChange={(value: 'single' | 'double' | 'custom') => 
                setFormData({ 
                  ...formData, 
                  format: { ...formData.format, paragraphSpacing: value }
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="single">单倍行距</SelectItem>
                <SelectItem value="double">双倍行距</SelectItem>
                <SelectItem value="custom">自定义间距</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 质量控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">质量控制</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">写作风格</label>
            <Select
              value={formData.style}
              onValueChange={(value: 'academic' | 'business' | 'journalistic' | 'technical') => 
                setFormData({ ...formData, style: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="academic">学术风格</SelectItem>
                <SelectItem value="business">商务风格</SelectItem>
                <SelectItem value="journalistic">新闻风格</SelectItem>
                <SelectItem value="technical">技术风格</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">语言要求</label>
            <Textarea
              value={formData.languageRequirements}
              onChange={(e) => setFormData({ ...formData, languageRequirements: e.target.value })}
              placeholder="描述具体的语言和表达要求..."
              rows={3}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">启用质量检查</label>
              <p className="text-xs text-muted-foreground">
                自动检查内容质量和一致性
              </p>
            </div>
            <Switch
              checked={formData.enableQualityCheck}
              onCheckedChange={(checked) => 
                setFormData({ ...formData, enableQualityCheck: checked })
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* 自定义指令 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">自定义指令</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">额外写作指令</label>
            <Textarea
              value={formData.customInstructions}
              onChange={(e) => setFormData({ ...formData, customInstructions: e.target.value })}
              placeholder="输入额外的写作指令和要求..."
              rows={4}
            />
            <p className="text-xs text-muted-foreground">
              这些指令将在生成每个章节时被包含在提示词中
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 