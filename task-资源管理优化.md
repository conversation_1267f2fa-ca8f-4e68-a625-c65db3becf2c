# 资源管理优化

## 文件名: task-资源管理优化.md
## 创建时间: 2025-01-27
## 创建者: AI助手
## Yolo模式: 禁用

## 任务描述
用户需求：
1. 移除网页添加功能
2. 上传本地文件时限制原始文件大小和数量（分割后不计入）
3. 添加后在"可选"后面显示已添加文件数量和总大小
4. 单个文件限制3MB，数量十个

## 项目概览
Deep Research 系统是一个AI驱动的深度研究平台，支持添加本地资源进行研究。系统会自动将大文件分割成多个小文件处理，但在显示和限制时需要按原始文件数量计算。

⚠️ 警告：请勿修改此部分 ⚠️
[RIPER-5协议规则的核心摘要：
- MODE: RESEARCH - 信息收集和深入理解
- MODE: INNOVATE - 头脑风暴和多方案探索  
- MODE: PLAN - 创建详尽的技术规范
- MODE: EXECUTE - 严格按照计划实施
- MODE: REVIEW - 验证实施与计划的一致性
- 每个模式开始必须声明 [MODE: XXX]
- 执行模式必须100%忠实于计划
- 代码更改必须使用工具完成，不输出代码给用户]
⚠️ 警告：请勿修改此部分 ⚠️

## 分析

通过代码分析发现：

1. **文件分割逻辑**：
   - 大文件会被分割成多个小文件（MAX_CHUNK_LENGTH = 10000）
   - 分割后的文件ID格式为"原始ID_索引"，如"abc123_1"
   - 每个分割后的文件都会被添加为独立资源

2. **当前显示问题**：
   - 资源数量显示的是所有资源数量，包括分割后的文件
   - 文件数量限制检查也是基于总资源数量

## 当前执行步骤: "优化资源显示和限制"

## 任务进度

[2025-01-27 时间戳]
- 修改文件：src/utils/file.ts, src/components/Research/Topic.tsx
- 变更内容：
  1. 在src/utils/file.ts中添加countOriginalFiles函数，用于计算原始文件数量
  2. 修改Topic.tsx中的资源数量显示，使用原始文件数量
  3. 修改文件数量限制检查，使用原始文件数量
  4. 移除网页添加功能
- 变更原因：按用户要求优化资源管理功能
- 阻碍因素：无
- 状态：成功

## 最终审查
[待完成] 