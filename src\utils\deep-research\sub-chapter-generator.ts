/**
 * 子章节生成和合并工具类
 * 用于简化子章节写作流程，应对大模型token输出限制
 */

import {
  subChapterConfig,
  generateSubChapterPrompt,
  generateChapterMergePrompt,
  generateFinalReportMergePrompt,
  getSubChapterCount,
  getSubChapterWordCount,
  reportTitlePrompt
} from '@/constants/prompts';

export interface ResearchData {
  plan: string;
  learnings: string[];
  sources: any[];
  localResources: any[];
  images: any[];
  requirement: string;
}

export interface SubChapterContent {
  chapterNum: number;
  sectionIndex: number;
  sectionId: string;
  title: string;
  content: string;
  wordCount: string;
}

export interface ChapterContent {
  chapterNum: number;
  title: string;
  content: string;
  subChapters: SubChapterContent[];
}

/**
 * 子章节生成器类
 */
export class SubChapterGenerator {
  private researchData: ResearchData;
  private generatedSubChapters: Map<string, SubChapterContent> = new Map();
  private generatedChapters: Map<number, ChapterContent> = new Map();

  constructor(researchData: ResearchData) {
    this.researchData = researchData;
  }

  /**
   * 生成单个子章节的提示词
   */
  generateSubChapterPrompt(chapterNum: number, sectionIndex: number): string {
    const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
    if (!config || !config.sections[sectionIndex]) {
      throw new Error(`Invalid chapter ${chapterNum} or section ${sectionIndex}`);
    }

    return generateSubChapterPrompt(
      chapterNum,
      sectionIndex,
      this.researchData.plan,
      this.researchData.learnings,
      this.researchData.sources,
      this.researchData.localResources,
      this.researchData.images,
      this.researchData.requirement
    );
  }

  /**
   * 保存生成的子章节内容
   */
  saveSubChapterContent(
    chapterNum: number,
    sectionIndex: number,
    content: string
  ): void {
    const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
    if (!config || !config.sections[sectionIndex]) {
      throw new Error(`Invalid chapter ${chapterNum} or section ${sectionIndex}`);
    }

    const section = config.sections[sectionIndex];
    const subChapter: SubChapterContent = {
      chapterNum,
      sectionIndex,
      sectionId: section.id,
      title: section.title,
      content,
      wordCount: getSubChapterWordCount(chapterNum, sectionIndex)
    };

    this.generatedSubChapters.set(`${chapterNum}.${sectionIndex}`, subChapter);
  }

  /**
   * 获取章节的所有子章节
   */
  getChapterSubChapters(chapterNum: number): SubChapterContent[] {
    const count = getSubChapterCount(chapterNum);
    const subChapters: SubChapterContent[] = [];

    for (let i = 0; i < count; i++) {
      const key = `${chapterNum}.${i}`;
      const subChapter = this.generatedSubChapters.get(key);
      if (subChapter) {
        subChapters.push(subChapter);
      }
    }

    return subChapters;
  }

  /**
   * 生成章节合并提示词
   */
  generateChapterMergePrompt(chapterNum: number): string {
    const subChapters = this.getChapterSubChapters(chapterNum);
    if (subChapters.length === 0) {
      throw new Error(`No sub-chapters found for chapter ${chapterNum}`);
    }

    const subChapterContents = subChapters.map(sc => sc.content);
    return generateChapterMergePrompt(chapterNum, subChapterContents);
  }

  /**
   * 保存合并后的章节内容
   */
  saveChapterContent(chapterNum: number, content: string): void {
    const subChapters = this.getChapterSubChapters(chapterNum);
    const chapterTitle = this.getChapterTitle(chapterNum);

    const chapter: ChapterContent = {
      chapterNum,
      title: chapterTitle,
      content,
      subChapters
    };

    this.generatedChapters.set(chapterNum, chapter);
  }

  /**
   * 生成最终报告合并提示词
   */
  generateFinalReportMergePrompt(reportTitle: string): string {
    const chapters: string[] = [];
    
    for (let i = 1; i <= 6; i++) {
      const chapter = this.generatedChapters.get(i);
      if (chapter) {
        chapters.push(chapter.content);
      } else {
        throw new Error(`Chapter ${i} content not found`);
      }
    }

    return generateFinalReportMergePrompt(reportTitle, chapters);
  }

  /**
   * 生成报告标题提示词
   */
  generateReportTitlePrompt(): string {
    return reportTitlePrompt
      .replace('{plan}', this.researchData.plan)
      .replace('{learnings}', this.researchData.learnings.join('\n'))
      .replace('{requirement}', this.researchData.requirement);
  }

  /**
   * 获取章节标题
   */
  private getChapterTitle(chapterNum: number): string {
    const titles = {
      1: '项目公司的商业模式',
      2: '项目公司的经营状况',
      3: '项目公司的管理团队',
      4: '项目公司的治理结构',
      5: '项目公司的发展情况与投资回报',
      6: '总结与评价'
    };
    return titles[chapterNum as keyof typeof titles] || '未知章节';
  }

  /**
   * 获取所有待生成的子章节信息
   */
  getAllSubChapterInfo(): Array<{
    chapterNum: number;
    sectionIndex: number;
    sectionId: string;
    title: string;
    wordCount: string;
  }> {
    const info: Array<{
      chapterNum: number;
      sectionIndex: number;
      sectionId: string;
      title: string;
      wordCount: string;
    }> = [];

    for (let chapterNum = 1; chapterNum <= 6; chapterNum++) {
      const count = getSubChapterCount(chapterNum);
      for (let sectionIndex = 0; sectionIndex < count; sectionIndex++) {
        const config = subChapterConfig[chapterNum as keyof typeof subChapterConfig];
        const section = config.sections[sectionIndex];
        
        info.push({
          chapterNum,
          sectionIndex,
          sectionId: section.id,
          title: section.title,
          wordCount: getSubChapterWordCount(chapterNum, sectionIndex)
        });
      }
    }

    return info;
  }

  /**
   * 检查生成进度
   */
  getProgress(): {
    totalSubChapters: number;
    completedSubChapters: number;
    totalChapters: number;
    completedChapters: number;
    progressPercentage: number;
  } {
    const allInfo = this.getAllSubChapterInfo();
    const totalSubChapters = allInfo.length;
    const completedSubChapters = this.generatedSubChapters.size;
    const totalChapters = 6;
    const completedChapters = this.generatedChapters.size;
    
    const progressPercentage = totalSubChapters > 0 
      ? Math.round((completedSubChapters / totalSubChapters) * 100)
      : 0;

    return {
      totalSubChapters,
      completedSubChapters,
      totalChapters,
      completedChapters,
      progressPercentage
    };
  }

  /**
   * 获取下一个待生成的子章节
   */
  getNextSubChapter(): {
    chapterNum: number;
    sectionIndex: number;
    sectionId: string;
    title: string;
    wordCount: string;
  } | null {
    const allInfo = this.getAllSubChapterInfo();
    
    for (const info of allInfo) {
      const key = `${info.chapterNum}.${info.sectionIndex}`;
      if (!this.generatedSubChapters.has(key)) {
        return info;
      }
    }
    
    return null; // 所有子章节都已生成
  }

  /**
   * 导出生成状态（用于保存/恢复）
   */
  exportState(): {
    subChapters: Array<SubChapterContent>;
    chapters: Array<ChapterContent>;
  } {
    return {
      subChapters: Array.from(this.generatedSubChapters.values()),
      chapters: Array.from(this.generatedChapters.values())
    };
  }

  /**
   * 导入生成状态（用于保存/恢复）
   */
  importState(state: {
    subChapters: Array<SubChapterContent>;
    chapters: Array<ChapterContent>;
  }): void {
    this.generatedSubChapters.clear();
    this.generatedChapters.clear();

    // 恢复子章节
    state.subChapters.forEach(subChapter => {
      const key = `${subChapter.chapterNum}.${subChapter.sectionIndex}`;
      this.generatedSubChapters.set(key, subChapter);
    });

    // 恢复章节
    state.chapters.forEach(chapter => {
      this.generatedChapters.set(chapter.chapterNum, chapter);
    });
  }
}

/**
 * 简化的使用示例函数
 */
export function createSubChapterWorkflow(researchData: ResearchData) {
  const generator = new SubChapterGenerator(researchData);
  
  return {
    generator,
    
    // 生成所有子章节提示词的批处理函数
    generateAllSubChapterPrompts() {
      const prompts: Array<{
        key: string;
        chapterNum: number;
        sectionIndex: number;
        title: string;
        prompt: string;
      }> = [];

      const allInfo = generator.getAllSubChapterInfo();
      allInfo.forEach(info => {
        const prompt = generator.generateSubChapterPrompt(info.chapterNum, info.sectionIndex);
        prompts.push({
          key: `${info.chapterNum}.${info.sectionIndex}`,
          chapterNum: info.chapterNum,
          sectionIndex: info.sectionIndex,
          title: info.title,
          prompt
        });
      });

      return prompts;
    },

    // 生成所有章节合并提示词的批处理函数
    generateAllChapterMergePrompts() {
      const prompts: Array<{
        chapterNum: number;
        title: string;
        prompt: string;
      }> = [];

      for (let chapterNum = 1; chapterNum <= 6; chapterNum++) {
        try {
          const prompt = generator.generateChapterMergePrompt(chapterNum);
          prompts.push({
            chapterNum,
            title: generator['getChapterTitle'](chapterNum),
            prompt
          });
        } catch (error) {
          console.warn(`Cannot generate merge prompt for chapter ${chapterNum}:`, error);
        }
      }

      return prompts;
    }
  };
}

export default SubChapterGenerator; 