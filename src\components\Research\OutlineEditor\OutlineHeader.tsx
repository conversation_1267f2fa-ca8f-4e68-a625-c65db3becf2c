"use client";
import { useState } from "react";

import { Edit2, Save, X, Calendar, User } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useOutlineStore } from "@/store/outline";
import { ResearchOutline } from "@/types/outline";

// 安全的日期格式化函数
const formatDate = (date: Date | string): string => {
  try {
    if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    return new Date(date).toLocaleDateString();
  } catch {
    return '无效日期';
  }
};

interface OutlineHeaderProps {
  outline: ResearchOutline;
}

export default function OutlineHeader({ outline }: OutlineHeaderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    title: outline.title,
    description: outline.description,
  });
  
  const outlineStore = useOutlineStore();

  const handleSave = () => {
    const updatedOutline = {
      ...outline,
      title: formData.title.trim(),
      description: formData.description.trim(),
    };
    
    outlineStore.updateOutline(updatedOutline);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData({
      title: outline.title,
      description: outline.description,
    });
    setIsEditing(false);
  };

  const getSourceLabel = (source: string) => {
    switch (source) {
      case 'ai_generated': return 'AI生成';
      case 'user_created': return '手动创建';
      case 'imported': return '导入';
      case 'hybrid': return '混合模式';
      default: return '未知';
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'ai_generated': return 'bg-blue-100 text-blue-800';
      case 'user_created': return 'bg-green-100 text-green-800';
      case 'imported': return 'bg-purple-100 text-purple-800';
      case 'hybrid': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {isEditing ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">大纲标题</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="输入大纲标题"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">大纲描述</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="输入大纲描述"
                    rows={3}
                  />
                </div>
                
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleSave}>
                    <Save className="w-4 h-4 mr-1" />
                    保存
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleCancel}>
                    <X className="w-4 h-4 mr-1" />
                    取消
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <CardTitle className="text-xl">{outline.title}</CardTitle>
                  <Badge className={getSourceColor(outline.source)}>
                    {getSourceLabel(outline.source)}
                  </Badge>
                </div>
                
                {outline.description && (
                  <p className="text-muted-foreground">{outline.description}</p>
                )}
                
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>创建于 {formatDate(outline.createdAt)}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <User className="w-4 h-4" />
                    <span>版本 {outline.version}</span>
                  </div>
                  
                  {new Date(outline.updatedAt).getTime() !== new Date(outline.createdAt).getTime() && (
                    <div className="flex items-center space-x-1">
                      <span>更新于 {formatDate(outline.updatedAt)}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {!isEditing && (
            <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
              <Edit2 className="w-4 h-4 mr-1" />
              编辑
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {outline.chapters.length}
            </div>
            <div className="text-sm text-muted-foreground">章节</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-green-600">
              {outline.chapters.reduce((sum, chapter) => sum + chapter.sections.length, 0)}
            </div>
            <div className="text-sm text-muted-foreground">子章节</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {outline.chapters.reduce((sum, chapter) => sum + chapter.wordCount.min, 0)}-
              {outline.chapters.reduce((sum, chapter) => sum + chapter.wordCount.max, 0)}
            </div>
            <div className="text-sm text-muted-foreground">预计字数</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 