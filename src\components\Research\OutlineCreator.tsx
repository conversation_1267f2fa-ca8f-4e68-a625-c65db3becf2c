"use client";
import { useState, useEffect } from 'react';
import { useOutlineStore } from '@/store/outline';
import { useOutlineManager } from '@/hooks/useOutlineManager';
import { useTaskStore } from '@/store/task';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/Internal/Button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { PlusCircle, FileText } from 'lucide-react';

interface OutlineCreatorProps {
  open: boolean;
  onClose: () => void;
}

export default function OutlineCreator({ open, onClose }: OutlineCreatorProps) {
  const [view, setView] = useState<'list' | 'create'>('list');
  const [newOutlineTitle, setNewOutlineTitle] = useState('');
  const [newOutlineDesc, setNewOutlineDesc] = useState('');
  
  const outlineStore = useOutlineStore();
  const taskStore = useTaskStore();
  const { applyTemplate, createOutline } = useOutlineManager();

  // 当组件加载时，确保内置模板已初始化
  useEffect(() => {
    if (open) {
      outlineStore.initializeBuiltInTemplates();
    }
    // 仅在 open 变化时运行，避免因 outlineStore 变化而导致的无限循环
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const handleSelectOutline = (outlineId: string) => {
    const outline = outlineStore.getOutlineById(outlineId);
    if (outline) {
      taskStore.setResearchOutline(outline);
      taskStore.setOutlineDriven(true);
      onClose(); // 关闭对话框
    }
  };

  const handleSelectTemplate = (templateId: string) => {
    const newOutline = applyTemplate(templateId);
    if (newOutline) {
      taskStore.setResearchOutline(newOutline);
      taskStore.setOutlineDriven(true);
      onClose(); // 关闭对话框
    }
  };

  const handleCreateNewOutline = () => {
    if (newOutlineTitle.trim() === '') return;
    const newOutline = createOutline(newOutlineTitle, newOutlineDesc);
    taskStore.setResearchOutline(newOutline);
    taskStore.setOutlineDriven(true);
    onClose(); // 关闭对话框
  };

  const handleClose = () => {
    setView('list');
    setNewOutlineTitle('');
    setNewOutlineDesc('');
    onClose();
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>从大纲开始研究</DialogTitle>
          <DialogDescription>
            选择一个预设模板或创建一个新的空白大纲来启动您的研究。
          </DialogDescription>
        </DialogHeader>

        {view === 'list' && (
          <div className="space-y-6">
            {/* 已创建的大纲 */}
            {outlineStore.outlines.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">选择已创建的大纲</h3>
                <ScrollArea className="h-48">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-1">
                    {outlineStore.outlines.map(outline => (
                      <Card key={outline.id} className="cursor-pointer hover:border-blue-500" onClick={() => handleSelectOutline(outline.id)}>
                        <CardHeader>
                          <CardTitle className="text-base flex items-center">
                            <FileText className="w-4 h-4 mr-2" />
                            {outline.title}
                          </CardTitle>
                          <CardDescription>
                            {outline.chapters.length} 个章节 • 创建于 {outline.createdAt.toLocaleDateString()}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground line-clamp-2">{outline.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* 模板和创建选项 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">
                {outlineStore.outlines.length > 0 ? '或从模板创建新大纲' : '选择一个模板'}
              </h3>
              <ScrollArea className="h-72">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-1">
                  {/* 创建新大纲的选项 */}
                  <Card 
                    className="cursor-pointer hover:border-blue-500 flex flex-col items-center justify-center text-center p-4"
                    onClick={() => setView('create')}
                  >
                    <PlusCircle className="w-12 h-12 text-gray-400 mb-2" />
                    <CardTitle className="text-base">创建空白大纲</CardTitle>
                    <CardDescription>从零开始构建您的研究结构。</CardDescription>
                  </Card>
                  
                  {/* 模板列表 */}
                  {outlineStore.templates.map(template => (
                    <Card key={template.id} className="cursor-pointer hover:border-blue-500 border-dashed" onClick={() => handleSelectTemplate(template.id)}>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center">
                          <FileText className="w-4 h-4 mr-2" />
                          {template.name}
                          {template.isBuiltIn && (
                            <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">内置</span>
                          )}
                        </CardTitle>
                        <CardDescription>{template.category}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground line-clamp-2">{template.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}

        {view === 'create' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">创建新的空白大纲</h3>
            <div className="space-y-2">
              <label htmlFor="outline-title">大纲标题 *</label>
              <Input 
                id="outline-title"
                value={newOutlineTitle}
                onChange={(e) => setNewOutlineTitle(e.target.value)}
                placeholder="例如：关于量子计算的未来发展研究"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="outline-desc">大纲描述</label>
              <Textarea
                id="outline-desc"
                value={newOutlineDesc}
                onChange={(e) => setNewOutlineDesc(e.target.value)}
                placeholder="简要描述这个大纲的研究目标和范围"
                rows={3}
              />
            </div>
            <DialogFooter>
              <Button variant="ghost" onClick={() => setView('list')}>返回</Button>
              <Button onClick={handleCreateNewOutline} disabled={!newOutlineTitle.trim()}>创建并开始</Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
} 