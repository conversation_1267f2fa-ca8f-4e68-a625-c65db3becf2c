"use client";
import { useState } from "react";

import { Save, X, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { OutlineSection } from "@/types/outline";

interface SectionEditorProps {
  section?: OutlineSection;
  onSave: (section: Omit<OutlineSection, 'id'>) => void;
  onCancel: () => void;
}

export default function SectionEditor({ section, onSave, onCancel }: SectionEditorProps) {
  const [formData, setFormData] = useState({
    number: section?.number || '1.1',
    title: section?.title || '',
    description: section?.description || '',
    wordCount: {
      min: section?.wordCount.min || 800,
      max: section?.wordCount.max || 1200,
    },
    researchPoints: section?.researchPoints || [''],
    enabled: section?.enabled ?? true,
    dependencies: section?.dependencies || [],
  });

  const handleResearchPointChange = (index: number, value: string) => {
    const newPoints = [...formData.researchPoints];
    newPoints[index] = value;
    setFormData({ ...formData, researchPoints: newPoints });
  };

  const handleAddResearchPoint = () => {
    setFormData({
      ...formData,
      researchPoints: [...formData.researchPoints, '']
    });
  };

  const handleRemoveResearchPoint = (index: number) => {
    const newPoints = formData.researchPoints.filter((_, i) => i !== index);
    setFormData({ ...formData, researchPoints: newPoints });
  };

  const handleSave = () => {
    const cleanedPoints = formData.researchPoints.filter(point => point.trim() !== '');
    
    const sectionData = {
      number: formData.number,
      title: formData.title.trim(),
      description: formData.description.trim(),
      wordCount: formData.wordCount,
      researchPoints: cleanedPoints,
      enabled: formData.enabled,
      dependencies: formData.dependencies,
    };
    
    console.log('📝 SectionEditor保存数据:', sectionData);
    onSave(sectionData);
  };

  const isValid = formData.title.trim() !== '' && 
                  formData.wordCount.min > 0 && 
                  formData.wordCount.max > formData.wordCount.min;

  return (
    <div className="space-y-4">
      {/* 基本信息 */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">子章节编号</label>
          <Input
            value={formData.number}
            onChange={(e) => setFormData({ ...formData, number: e.target.value })}
            placeholder="如: 1.1, 1.2"
          />
        </div>
      </div>

      {/* 标题 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">子章节标题 *</label>
        <Input
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          placeholder="输入子章节标题"
        />
      </div>

      {/* 描述 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">子章节描述</label>
        <Textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="输入子章节描述"
          rows={2}
        />
      </div>

      {/* 字数范围 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">字数范围</label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Input
              type="number"
              value={formData.wordCount.min}
              onChange={(e) => setFormData({
                ...formData,
                wordCount: { ...formData.wordCount, min: parseInt(e.target.value) || 0 }
              })}
              placeholder="最小字数"
              min={0}
            />
          </div>
          <div>
            <Input
              type="number"
              value={formData.wordCount.max}
              onChange={(e) => setFormData({
                ...formData,
                wordCount: { ...formData.wordCount, max: parseInt(e.target.value) || 0 }
              })}
              placeholder="最大字数"
              min={formData.wordCount.min}
            />
          </div>
        </div>
      </div>

      {/* 研究要点 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">研究要点</label>
          <Button
            type="button"
            size="sm"
            variant="outline"
            onClick={handleAddResearchPoint}
          >
            <Plus className="w-4 h-4 mr-1" />
            添加要点
          </Button>
        </div>
        
        <div className="space-y-2">
          {formData.researchPoints.map((point, index) => (
            <div key={index} className="flex items-center space-x-2">
              <Input
                value={point}
                onChange={(e) => handleResearchPointChange(index, e.target.value)}
                placeholder={`研究要点 ${index + 1}`}
              />
              {formData.researchPoints.length > 1 && (
                <Button
                  type="button"
                  size="sm"
                  variant="ghost"
                  onClick={() => handleRemoveResearchPoint(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel}>
          <X className="w-4 h-4 mr-1" />
          取消
        </Button>
        <Button onClick={handleSave} disabled={!isValid}>
          <Save className="w-4 h-4 mr-1" />
          保存
        </Button>
      </div>
    </div>
  );
} 