server {
    listen 80;
    server_name your-domain.com;
    
    # 设置根目录为构建输出目录
    root D:/nginx-1.22.1/html/deepresearch;
    index index.html;

    # 添加 MIME 类型映射
    location ~* \.mjs$ {
        add_header Content-Type "application/javascript; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 处理静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 特别处理 PDF worker 文件 - 更具体的匹配
    location = /scripts/pdf.worker.min.mjs {
        add_header Content-Type "application/javascript; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 处理所有 scripts 目录下的 .mjs 文件
    location ~* ^/scripts/.*\.mjs$ {
        add_header Content-Type "application/javascript; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 处理 Service Worker
    location /sw.js {
        add_header Content-Type "application/javascript; charset=utf-8";
        expires off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 处理 manifest.json
    location /manifest.json {
        add_header Content-Type "application/json; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public";
    }

    # 主要的 SPA 路由处理
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
} 