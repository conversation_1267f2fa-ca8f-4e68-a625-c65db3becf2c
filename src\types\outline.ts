/**
 * 研究大纲相关类型定义
 */

export interface ResearchOutline {
  id: string;
  title: string;
  description: string;
  chapters: OutlineChapter[];
  writingConfig: WritingConfig;
  createdAt: Date;
  updatedAt: Date;
  source: 'ai_generated' | 'user_created' | 'hybrid';
  version: string;
}

export interface OutlineChapter {
  id: string;
  number: number;
  title: string;
  description: string;
  sections: OutlineSection[];
  wordCount: WordCountRange;
  researchPoints: string[];
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
}

export interface OutlineSection {
  id: string;
  number: string; // 如 "1.1", "1.2"
  title: string;
  description: string;
  wordCount: WordCountRange;
  researchPoints: string[];
  enabled: boolean;
  dependencies: string[]; // 依赖的其他章节ID
}

export interface WordCountRange {
  min: number;
  max: number;
}

export interface WritingConfig {
  mode: WritingMode;
  format: ReportFormat;
  avoidDuplication: boolean;
  enableContextMemory: boolean;
  chapterDelay: number; // 章节间延迟（毫秒）
  sectionDelay: number; // 子章节间延迟（毫秒）
  style: 'academic' | 'business' | 'journalistic' | 'technical';
  languageRequirements: string;
  enableQualityCheck: boolean;
  customInstructions: string;
}

export type WritingMode = 'chapter' | 'section' | 'traditional';

export interface ReportFormat {
  titleFormat: 'numbered' | 'plain' | 'custom';
  includeTableOfContents: boolean;
  includeCitations: boolean;
  includeImages: boolean;
  paragraphSpacing: 'single' | 'double' | 'normal' | 'custom';
}

export interface TitleFormat {
  chapter: string; // 如"一、"、"第一章、"
  section: string; // 如"1.1、"、"（一）"
  subsection: string; // 如"1.1.1、"
}

export interface CitationFormat {
  external: string; // 如"[1]"、"（1）"
  local: string; // 如"[L-1]"、"【本地-1】"
  style: 'number' | 'author' | 'custom';
}

export interface SpacingConfig {
  beforeTitle: number;
  afterTitle: number;
  betweenSections: number;
  betweenParagraphs: number;
}

export interface WritingProgress {
  currentChapter: number;
  currentSection: number;
  completedChapters: number[];
  completedSections: string[];
  totalProgress: number; // 0-100
  estimatedTimeRemaining: number; // 分钟
  startTime: Date | null;
  lastUpdateTime: Date | null;
}

export interface OutlineTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  chapters: Omit<OutlineChapter, 'id'>[];
  defaultConfig: WritingConfig;
  tags: string[];
  isBuiltIn: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface OutlineValidationResult {
  isValid: boolean;
  errors: OutlineValidationError[];
  warnings: OutlineValidationWarning[];
  suggestions: string[];
}

export interface OutlineValidationError {
  type: 'missing_title' | 'invalid_word_count' | 'duplicate_chapter' | 'circular_dependency';
  message: string;
  chapterId?: string;
  sectionId?: string;
}

export interface OutlineValidationWarning {
  type: 'low_word_count' | 'high_word_count' | 'missing_research_points' | 'unbalanced_chapters';
  message: string;
  chapterId?: string;
  sectionId?: string;
}

// 大纲生成配置
export interface OutlineGenerationConfig {
  maxChapters: number;
  maxSectionsPerChapter: number;
  defaultWordCountPerChapter: WordCountRange;
  defaultWordCountPerSection: WordCountRange;
  includeResearchPoints: boolean;
  generateDescriptions: boolean;
}

// 大纲导入导出格式
export interface OutlineExportData {
  outline: ResearchOutline;
  metadata: {
    exportedAt: Date;
    exportedBy: string;
    version: string;
  };
} 