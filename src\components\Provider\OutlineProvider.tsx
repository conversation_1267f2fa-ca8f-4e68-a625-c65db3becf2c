"use client";
import { useEffect } from 'react';
import { useOutlineStore } from '@/store/outline';

interface OutlineProviderProps {
  children: React.ReactNode;
}

export default function OutlineProvider({ children }: OutlineProviderProps) {
  const outlineStore = useOutlineStore();

  // 在应用启动时初始化内置模板
  useEffect(() => {
    outlineStore.initializeBuiltInTemplates();
  }, [outlineStore]);

  return <>{children}</>;
} 