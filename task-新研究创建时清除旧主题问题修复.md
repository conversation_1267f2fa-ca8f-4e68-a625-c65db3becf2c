# 新研究创建时清除旧主题问题修复

## 文件名: task-新研究创建时清除旧主题问题修复.md
## 创建时间: 2025-01-27
## 创建者: AI助手
## Yolo模式: 禁用

## 任务描述
用户反馈：开始新研究后，之前的研究主题和写作要求（已包含研究主题）不应该再带入进到新的里面，现在会带进去。

## 项目概览
Deep Research 系统是一个AI驱动的深度研究平台，使用Zustand进行状态管理，支持研究主题、写作要求等数据的持久化存储。

⚠️ 警告：请勿修改此部分 ⚠️
[RIPER-5协议规则的核心摘要：
- MODE: RESEARCH - 信息收集和深入理解
- MODE: INNOVATE - 头脑风暴和多方案探索  
- MODE: PLAN - 创建详尽的技术规范
- MODE: EXECUTE - 严格按照计划实施
- MODE: REVIEW - 验证实施与计划的一致性
- 每个模式开始必须声明 [MODE: XXX]
- 执行模式必须100%忠实于计划
- 代码更改必须使用工具完成，不输出代码给用户]
⚠️ 警告：请勿修改此部分 ⚠️

## 分析

通过代码分析发现问题的根本原因：

### 1. 问题定位
- **文件**: `src/components/History.tsx` 第123行
- **问题**: 使用了`reset()`而不是`resetWithTopicProtection()`
- **影响**: 加载历史记录时会清除当前研究主题和写作要求

### 2. 代码现状分析
```typescript:src/components/History.tsx
function loadHistory(id: string) {
  const data = load(id);
  if (data) {
    // 检查当前是否有未保存的工作
    const currentState = backup();
    const hasUnsavedWork = currentState.question?.trim() || 
                         currentState.tasks.length > 0 || 
                         currentState.finalReport?.trim() ||
                         currentState.reportPlan?.trim();
    
    if (hasUnsavedWork) {
      const shouldContinue = confirm('当前有未保存的工作，是否保存后再加载历史记录？');
      if (!shouldContinue) return;
      
      // 自动保存当前工作
      autoSave(currentState);
    }
    
    reset(); // 🔥 问题所在：这里会清除研究主题
    restore(data);
  }
  onClose();
}
```

### 3. 现有保护机制
在`src/store/task.ts`中已经有保护机制：
```typescript:src/store/task.ts
reset: (preserveTopic = false) => {
  // 🔥 保存当前研究主题，避免在重置状态时被清空
  const currentQuestion = preserveTopic ? get().question : "";
  const currentTitle = preserveTopic ? get().title : "";
  const currentRequirement = preserveTopic ? get().requirement : "";
  
  // 重置所有状态为默认值
  set(() => ({ ...defaultValues }));
  
  // 🔥 恢复研究主题相关信息（如果需要保护的话）
  if (preserveTopic) {
    if (currentQuestion) set(() => ({ question: currentQuestion }));
    if (currentTitle) set(() => ({ title: currentTitle }));
    if (currentRequirement) set(() => ({ requirement: currentRequirement }));
  }
},

// 🔥 新增：专门的重置方法，保护研究主题
resetWithTopicProtection: () => {
  get().reset(true);
},
```

### 4. 其他相关调用检查
检查了其他可能的reset调用：
- `src/components/Research/Topic.tsx` - 正确使用了`resetWithTopicProtection()`
- `src/components/Setting.tsx` - 是设置重置，不影响研究主题
- 只有History.tsx存在问题

## 当前执行步骤: "1. 修复History.tsx中的reset调用"

## 任务进度

[2025-01-27 时间戳 - 第一个任务]
- 修改文件：src/components/History.tsx
- 变更内容：
  1. 第85行：在useTaskStore导入中添加resetWithTopicProtection
  2. 第86行：移除未使用的update导入  
  3. 第123行：将reset()调用替换为resetWithTopicProtection()
- 变更原因：修复加载历史记录时会清除当前研究主题的问题
- 阻碍因素：无
- 状态：成功

[2025-01-27 时间戳 - 第二个任务]
- 新建文件：src/constants/limits.ts
- 修改文件：src/utils/file.ts, src/components/Research/Topic.tsx
- 变更内容：
  1. 创建文件上传限制常量（单个文件3MB，最多10个文件）
  2. 在utils/file.ts中添加文件大小和数量验证函数
  3. 移除Topic.tsx中的网页添加选项
  4. 在handleFileUpload中添加文件限制验证
  5. 在UI中显示已添加文件数量和总大小统计
- 变更原因：按用户要求限制文件上传和显示统计信息
- 阻碍因素：无
- 状态：成功

## 最终审查
[待完成] 