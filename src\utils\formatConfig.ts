/**
 * 格式处理配置
 */

export interface FormatConfig {
  // 是否启用格式自动纠正
  enabled: boolean;
  
  // 标题格式化选项
  titleFormat: {
    enabled: boolean;
    fixChapterTitles: boolean;  // 修复章节标题格式
    fixSubTitles: boolean;      // 修复子标题格式
  };
  
  // 粗体使用选项
  boldFormat: {
    enabled: boolean;
    maxPerParagraph: number;    // 每段最大粗体数量
    fixIncomplete: boolean;     // 修复不完整的粗体格式
  };
  
  // 段落间距选项
  spacingFormat: {
    enabled: boolean;
    addAfterLongParagraphs: boolean;  // 在长段落后添加空行
    minParagraphLength: number;       // 触发添加空行的最小段落长度
  };
  
  // 引用格式选项
  citationFormat: {
    enabled: boolean;
    fixBrackets: boolean;       // 修复括号格式
    mergeConsecutive: boolean;  // 合并连续引用
  };
  
  // 日志选项
  logging: {
    enabled: boolean;
    showFixed: boolean;         // 显示已修复的问题
    showRemaining: boolean;     // 显示剩余问题
    onlyErrors: boolean;        // 只显示错误级别的问题
  };
}

/**
 * 默认格式配置
 */
export const DEFAULT_FORMAT_CONFIG: FormatConfig = {
  enabled: true,
  
  titleFormat: {
    enabled: true,
    fixChapterTitles: true,
    fixSubTitles: true,
  },
  
  boldFormat: {
    enabled: true,
    maxPerParagraph: 2,
    fixIncomplete: true,
  },
  
  spacingFormat: {
    enabled: false,  // 默认关闭，避免过度调整
    addAfterLongParagraphs: false,
    minParagraphLength: 60,
  },
  
  citationFormat: {
    enabled: true,
    fixBrackets: true,
    mergeConsecutive: true,
  },
  
  logging: {
    enabled: true,
    showFixed: true,
    showRemaining: true,
    onlyErrors: false,
  },
};

/**
 * 获取格式配置（可从本地存储读取）
 */
export function getFormatConfig(): FormatConfig {
  try {
    const stored = localStorage.getItem('formatConfig');
    if (stored) {
      const config = JSON.parse(stored);
      return { ...DEFAULT_FORMAT_CONFIG, ...config };
    }
  } catch (error) {
    console.warn('读取格式配置失败，使用默认配置:', error);
  }
  
  return DEFAULT_FORMAT_CONFIG;
}

/**
 * 保存格式配置到本地存储
 */
export function saveFormatConfig(config: Partial<FormatConfig>): void {
  try {
    const currentConfig = getFormatConfig();
    const newConfig = { ...currentConfig, ...config };
    localStorage.setItem('formatConfig', JSON.stringify(newConfig));
  } catch (error) {
    console.warn('保存格式配置失败:', error);
  }
}

/**
 * 重置格式配置为默认值
 */
export function resetFormatConfig(): void {
  try {
    localStorage.removeItem('formatConfig');
  } catch (error) {
    console.warn('重置格式配置失败:', error);
  }
} 